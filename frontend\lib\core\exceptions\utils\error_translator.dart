// lib/core/utils/error_translator.dart

import 'package:flutter/material.dart';
import 'package:frontend/core/exceptions/app_exceptions.dart';
import 'package:frontend/l10n/generated/l10n.dart';

class ErrorTranslator {
  /// Translate any error to user-friendly message
  static String translate(BuildContext context, Object error) {
    // If it's a custom exception with translation key
    if (error is AppException) {
      return _translateKey(context, error.message);
    }

    // If it's a raw string that looks like a translation key
    String errorString = error.toString();
    if (_isTranslationKey(errorString)) {
      return _translateKey(context, errorString);
    }

    // For any other error, try to extract meaningful message
    return _extractGenericMessage(context, errorString);
  }

  /// Check if string looks like a translation key
  static bool _isTranslationKey(String text) {
    return text.contains('userNotFound') ||
        text.contains('userAlreadyExists') ||
        text.contains('invalidCredentials') ||
        text.contains('serverError') ||
        text.contains('networkError') ||
        text.contains('connectionTimeout') ||
        text.contains('noInternet') ||
        text.contains('validationError') ||
        text.contains('accessDenied') ||
        text.contains('emailNotVerified') ||
        text.contains('weakPassword') ||
        text.contains('invalidEmailFormat') ||
        text.contains('resourceNotFound');
  }

  /// Translate translation key to localized message
  static String _translateKey(BuildContext context, String key) {
    switch (key) {
      case 'userAlreadyExists':
        return S.of(context).userAlreadyExists;
      case 'invalidCredentials':
        return S.of(context).invalidCredentials;
      case 'emailNotVerified':
        return S.of(context).emailNotVerified;
      case 'weakPassword':
        return S.of(context).weakPassword;
      case 'invalidEmailFormat':
        return S.of(context).invalidEmailFormat;
      case 'connectionTimeout':
        return S.of(context).connectionTimeout;
      case 'noInternet':
        return S.of(context).noInternet;
      case 'userNotFound':
        return S.of(context).userNotFound;
      case 'resourceNotFound':
        return S.of(context).resourceNotFound;
      case 'serverError':
        return S.of(context).serverError;
      case 'validationError':
        return S.of(context).validationError;
      case 'networkError':
        return S.of(context).networkError;
      case 'accessDenied':
        return S.of(context).accessDenied;
      default:
        return S.of(context).networkError; // Generic fallback
    }
  }

  /// Extract generic message from complex error strings
  static String _extractGenericMessage(
      BuildContext context, String errorString) {
    final lowerError = errorString.toLowerCase();

    // Check for common error patterns and return generic messages
    if (lowerError.contains('user') && lowerError.contains('not found')) {
      return S.of(context).userNotFound;
    }
    if (lowerError.contains('email') && lowerError.contains('exist')) {
      return S.of(context).userAlreadyExists;
    }
    if (lowerError.contains('invalid') && lowerError.contains('credential')) {
      return S.of(context).invalidCredentials;
    }
    if (lowerError.contains('server') && lowerError.contains('error')) {
      return S.of(context).serverError;
    }
    if (lowerError.contains('network') || lowerError.contains('connection')) {
      return S.of(context).networkError;
    }
    if (lowerError.contains('timeout')) {
      return S.of(context).connectionTimeout;
    }

    // Default generic error
    return S.of(context).networkError;
  }
}
