import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/infrastructure/repairs/repair_service.dart';
import 'package:frontend/models/get_repairs_by_id_command.dart';
import 'package:frontend/models/get_repairs_command.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'repair_provider.g.dart';

// Provider to fetch all repairs
@riverpod
Future<List<RepairModel>> fetchAllRepairs(Ref ref) async {
  print("Fetching all repairs");
  return RepairService.fetchRepairs();
}

// Provider to fetch repair by ID
@riverpod
Future<RepairDetailsModel?> fetchRepairById(
    Ref ref, ({int id, String languageCode}) params) async {
  print(
      "Fetching repair with ID: ${params.id} and language: ${params.languageCode}");
  return RepairService.fetchRepairById(params.id,
      languageCode: params.languageCode);
}
