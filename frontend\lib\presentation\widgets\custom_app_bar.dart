import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/providers/app_providers.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';

class CustomAppBar extends ConsumerWidget implements PreferredSizeWidget {
  final String title;

  const CustomAppBar({
    super.key,
    required this.title,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    // Get the current location from GoRouter
    final location = GoRouterState.of(context).fullPath;

    // Define routes where the back button should be hidden
    final Set<String> noBackButtonRoutes = {
      '/home',
      '/favorites',
      '/location',
      '/messages',
      '/settings',
    };

    // Check if we're on the SmartYambolScreen
    final bool hideBackButton = noBackButtonRoutes.contains(location);

    return AppBar(
      backgroundColor: theme.appBarTheme.backgroundColor,
      foregroundColor: theme.appBarTheme.foregroundColor,
      elevation: 7,
      shadowColor: Colors.black26,
      title: Text(
        title,
        overflow: TextOverflow.ellipsis,
        style: GoogleFonts.roboto(
          fontWeight: FontWeight.w700,
          fontSize: 22,
          height: 22 / 18,
          letterSpacing: 0.0,
          color: theme.appBarTheme.foregroundColor,
        ),
      ),
      centerTitle: true,
      automaticallyImplyLeading: false,
      leading: Opacity(
        opacity: hideBackButton ? 0.0 : 1.0, // Hide button on home screen
        child: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_new_rounded,
            color: theme.appBarTheme.foregroundColor,
            size: 24,
          ),
          onPressed: hideBackButton
              ? null // Disable button when on home screen
              : () {
                  if (context.canPop()) {
                    context.pop();
                  } else {
                    context.go('/');
                  }
                },
        ),
      ),
      actions: [
        Padding(
          padding: const EdgeInsets.only(right: 16),
          child: InkWell(
            borderRadius:
                BorderRadius.circular(40), // makes the ripple circular
            onTap: () {
              // Navigate to settings screen
              ref.read(selectedIndexProvider.notifier).setIndex(4);
              context.go('/settings');
            },
            child: CircleAvatar(
              backgroundColor: theme.brightness == Brightness.dark
                  ? Colors.white10
                  : Colors.black12,
              child: Icon(
                Icons.person_rounded,
                color: theme.appBarTheme.foregroundColor,
                size: 24,
              ),
            ),
          ),
        ),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
