import 'package:flutter/material.dart';
import 'package:frontend/l10n/generated/l10n.dart';

class SearchBarWidget extends StatefulWidget {
  final TextEditingController controller;
  final bool isDarkMode;

  const SearchBarWidget({
    super.key,
    required this.controller,
    required this.isDarkMode,
  });

  @override
  State<SearchBarWidget> createState() => _SearchBarWidgetState();
}

class _SearchBarWidgetState extends State<SearchBarWidget> {
  late String query;

  @override
  void initState() {
    super.initState();
    query = widget.controller.text;
    widget.controller.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onSearchChanged);
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      query = widget.controller.text;
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = widget.isDarkMode;

    return Container(
      height: 50,
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF333333) : Colors.white,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: widget.controller,
        style: TextStyle(
          color: isDarkMode ? Colors.white : const Color(0xFF626262),
        ),
        decoration: InputDecoration(
          hintText: S.of(context).search,
          hintStyle: TextStyle(
            color: isDarkMode ? Colors.white60 : Colors.black45,
          ),
          prefixIcon: Icon(
            Icons.search,
            color: isDarkMode ? Colors.white60 : Colors.black45,
          ),
          suffixIcon: query.isNotEmpty
              ? GestureDetector(
                  onTap: () {
                    widget.controller.clear();
                  },
                  child: Icon(
                    Icons.close,
                    color: isDarkMode ? Colors.white54 : Colors.black45,
                  ),
                )
              : null,
          border: InputBorder.none,
          contentPadding:
              const EdgeInsets.symmetric(vertical: 15, horizontal: 15),
        ),
      ),
    );
  }
}
