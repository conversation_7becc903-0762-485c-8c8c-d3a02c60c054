// Sensor Reading Model
class SensorReadingModel {
  final DateTime? date;
  final String? valueRange;
  final RangeEventConfig? rangeEventConfig;
  final int sensorIndex;
  final double? reading;

  SensorReadingModel({
    this.date,
    this.valueRange,
    this.rangeEventConfig,
    required this.sensorIndex,
    this.reading,
  });

  factory SensorReadingModel.fromJson(Map<String, dynamic> json) {
    return SensorReadingModel(
      date: json['date'] != null ? DateTime.tryParse(json['date']) : null,
      valueRange: json['valueRange'],
      rangeEventConfig: json['rangeEventConfig'] != null
          ? RangeEventConfig.fromJson(json['rangeEventConfig'])
          : null,
      sensorIndex: json['sensorIndex'] ?? 0,
      reading: json['reading']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date?.toIso8601String(),
      'valueRange': valueRange,
      'rangeEventConfig': rangeEventConfig?.toJson(),
      'sensorIndex': sensorIndex,
      'reading': reading,
    };
  }

  // Helper method to get sensor type from valueRange
  String get sensorType {
    if (valueRange == null) return 'unknown';

    if (valueRange!.contains('gammaRadiation')) return 'radiation';
    if (valueRange!.contains('moisture')) return 'humidity';
    if (valueRange!.contains('temperature')) return 'temperature';
    if (valueRange!.contains('barometricPressure')) return 'pressure';
    if (valueRange!.contains('airMicroGrams')) {
      // Check sensor index or name to determine PM2.5 vs PM10
      if (rangeEventConfig?.name.contains('2.5') == true || sensorIndex == 11) {
        return 'pm25';
      } else if (rangeEventConfig?.name.contains('10') == true ||
          sensorIndex == 13) {
        return 'pm10';
      }
      return 'pm';
    }

    return 'unknown';
  }

  // Helper method to get display value with units
  String get displayValue {
    if (reading == null) return '--';

    switch (sensorType) {
      case 'radiation':
        return '${reading!.toStringAsFixed(1)} μSv/h';
      case 'humidity':
        return '${reading!.toStringAsFixed(2)} RH(%)';
      case 'temperature':
        return '${reading!.toStringAsFixed(2)} °C';
      case 'pressure':
        return '${reading!.toStringAsFixed(2)} hPa';
      case 'pm25':
      case 'pm10':
      case 'pm':
        return '${reading!.toStringAsFixed(1)} µg/m³';
      default:
        return reading!.toStringAsFixed(2);
    }
  }

  // Removed the hardcoded displayName getter - translations are now handled in the UI

  @override
  String toString() {
    return 'SensorReadingModel(sensorIndex: $sensorIndex, reading: $reading, type: $sensorType)';
  }
}

// Range Event Configuration Model
class RangeEventConfig {
  final String? deviceNumber;
  final int sensorIndex;
  final String name;
  final double min;
  final double max;
  final Period period;
  final List<Style> styles;
  final String function;
  final List<String> eventNotificationTypes;
  final bool eventsEnabled;
  final String? description;
  final int priority;
  final String? severity;

  RangeEventConfig({
    this.deviceNumber,
    required this.sensorIndex,
    required this.name,
    required this.min,
    required this.max,
    required this.period,
    required this.styles,
    required this.function,
    required this.eventNotificationTypes,
    required this.eventsEnabled,
    this.description,
    required this.priority,
    this.severity,
  });

  factory RangeEventConfig.fromJson(Map<String, dynamic> json) {
    return RangeEventConfig(
      deviceNumber: json['deviceNumber'],
      sensorIndex: json['sensorIndex'] ?? 0,
      name: json['name'] ?? '',
      min: (json['min'] ?? 0).toDouble(),
      max: (json['max'] ?? 0).toDouble(),
      period: Period.fromJson(json['period'] ?? {}),
      styles:
          (json['styles'] as List?)?.map((e) => Style.fromJson(e)).toList() ??
              [],
      function: json['function'] ?? '',
      eventNotificationTypes:
          (json['eventNotificationTypes'] as List?)?.cast<String>() ?? [],
      eventsEnabled: json['eventsEnabled'] ?? false,
      description: json['description'],
      priority: json['priority'] ?? 0,
      severity: json['severity'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'deviceNumber': deviceNumber,
      'sensorIndex': sensorIndex,
      'name': name,
      'min': min,
      'max': max,
      'period': period.toJson(),
      'styles': styles.map((e) => e.toJson()).toList(),
      'function': function,
      'eventNotificationTypes': eventNotificationTypes,
      'eventsEnabled': eventsEnabled,
      'description': description,
      'priority': priority,
      'severity': severity,
    };
  }
}

// Period Model
class Period {
  final int? startMonth;
  final int? endMonth;
  final int? startDay;
  final int? endDay;
  final int? startHour;
  final int? endHour;
  final int? startMinute;
  final int? endMinute;

  Period({
    this.startMonth,
    this.endMonth,
    this.startDay,
    this.endDay,
    this.startHour,
    this.endHour,
    this.startMinute,
    this.endMinute,
  });

  factory Period.fromJson(Map<String, dynamic> json) {
    return Period(
      startMonth: json['startMonth'],
      endMonth: json['endMonth'],
      startDay: json['startDay'],
      endDay: json['endDay'],
      startHour: json['startHour'],
      endHour: json['endHour'],
      startMinute: json['startMinute'],
      endMinute: json['endMinute'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'startMonth': startMonth,
      'endMonth': endMonth,
      'startDay': startDay,
      'endDay': endDay,
      'startHour': startHour,
      'endHour': endHour,
      'startMinute': startMinute,
      'endMinute': endMinute,
    };
  }
}

// Style Model
class Style {
  final String type;
  final String? lineColor;
  final String? nodeColor;
  final int? iconId;
  final String? backgroundColor;
  final String? foregroundColor;

  Style({
    required this.type,
    this.lineColor,
    this.nodeColor,
    this.iconId,
    this.backgroundColor,
    this.foregroundColor,
  });

  factory Style.fromJson(Map<String, dynamic> json) {
    return Style(
      type: json['@type'] ?? json['type'] ?? '',
      lineColor: json['lineColor'],
      nodeColor: json['nodeColor'],
      iconId: json['iconId'],
      backgroundColor: json['backgroundColor'],
      foregroundColor: json['foregroundColor'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '@type': type,
      'lineColor': lineColor,
      'nodeColor': nodeColor,
      'iconId': iconId,
      'backgroundColor': backgroundColor,
      'foregroundColor': foregroundColor,
    };
  }
}
