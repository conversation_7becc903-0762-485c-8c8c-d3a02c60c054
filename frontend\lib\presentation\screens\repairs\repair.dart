import 'package:flutter/material.dart';
import 'package:frontend/models/get_repairs_command.dart';
import 'package:frontend/presentation/screens/repairs/repair_details.dart';
import 'package:frontend/providers/repair_provider.dart';
import 'package:frontend/providers/theme_provider.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart' as gmaps;
import 'package:flutter/services.dart' show rootBundle;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:ui' as ui;
import 'dart:typed_data';

class RepairScreen extends ConsumerStatefulWidget {
  const RepairScreen({super.key});

  @override
  _RepairScreenState createState() => _RepairScreenState();
}

class _RepairScreenState extends ConsumerState<RepairScreen> {
  gmaps.GoogleMapController? _mapController;
  final gmaps.LatLng _myCurrentLocation = const gmaps.LatLng(42.4842, 26.5035);
  Set<gmaps.Marker> _markers = {};
  Set<gmaps.Polygon> _polygons = {};
  String? _mapStyle;
  bool _isMapStyleLoaded = false;
  gmaps.BitmapDescriptor? _repairIcon;
  double _currentZoom = 14.0;
  List<RepairModel> _currentRepairs = [];

  // Zoom threshold for switching between markers and polygons
  static const double _zoomThreshold = 16.0;

  @override
  void initState() {
    super.initState();
    _loadCustomRepairIcon();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (mounted) {
      _loadMapStyle();
    }
  }

  Future<void> _loadMapStyle() async {
    if (!mounted) return;

    final themeMode = ref.read(themeNotifierProvider);
    final isDarkMode = themeMode == ThemeMode.dark;

    try {
      // Load the style based on dark or light mode
      if (isDarkMode) {
        _mapStyle =
            await rootBundle.loadString('assets/map_styles/dark_map.json');
      } else {
        _mapStyle =
            await rootBundle.loadString('assets/map_styles/light_map.json');
      }

      // Set the map style and update the state
      if (mounted) {
        setState(() {
          _isMapStyleLoaded = true;
        });
      }
    } catch (e) {
      print("Error loading map style: $e");
      if (mounted) {
        setState(() {
          _isMapStyleLoaded = true; // Continue without custom style
        });
      }
    }
  }

  Future<void> _loadCustomRepairIcon() async {
    try {
      // Load your custom repair SVG icon
      final svgString = await rootBundle.loadString('assets/icons/repair.svg');
      final pictureInfo =
          await vg.loadPicture(SvgStringLoader(svgString), null);

      final ui.PictureRecorder recorder = ui.PictureRecorder();
      final Canvas canvas = Canvas(recorder);

      final double logicalSize = 45.0; // Size of the marker
      final double pixelRatio = MediaQuery.of(context).devicePixelRatio;
      final double bitmapWidth = logicalSize * pixelRatio;
      final double bitmapHeight = logicalSize * pixelRatio;

      // Calculate scale factor to fit the SVG in the desired size
      final double scaleX = bitmapWidth / pictureInfo.size.width;
      final double scaleY = bitmapHeight / pictureInfo.size.height;
      final double scale = scaleX < scaleY ? scaleX : scaleY;
      final double adjustedScale = scale / pixelRatio;

      final double translateX =
          (logicalSize - (pictureInfo.size.width * adjustedScale)) /
              2 *
              pixelRatio;
      final double translateY =
          (logicalSize - (pictureInfo.size.height * adjustedScale)) /
              2 *
              pixelRatio;

      // Transform and draw
      canvas.translate(translateX, translateY);
      canvas.scale(adjustedScale * pixelRatio);
      canvas.drawPicture(pictureInfo.picture);

      // Convert to image
      final ui.Image image = await recorder.endRecording().toImage(
            bitmapWidth.toInt(),
            bitmapHeight.toInt(),
          );

      final ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);
      if (byteData != null) {
        final Uint8List uint8List = byteData.buffer.asUint8List();
        final gmaps.BitmapDescriptor icon =
            gmaps.BitmapDescriptor.fromBytes(uint8List);

        if (mounted) {
          setState(() {
            _repairIcon = icon;
          });
        }
      }
    } catch (e) {
      print("Error loading custom repair icon: $e");
      // Fallback to default marker if SVG loading fails
      _repairIcon = gmaps.BitmapDescriptor.defaultMarkerWithHue(
          gmaps.BitmapDescriptor.hueOrange);
    }
  }

  // Helper method to calculate the centroid of a polygon
  gmaps.LatLng _calculatePolygonCentroid(List<gmaps.LatLng> points) {
    double centroidLat = 0;
    double centroidLng = 0;

    for (gmaps.LatLng point in points) {
      centroidLat += point.latitude;
      centroidLng += point.longitude;
    }

    return gmaps.LatLng(
      centroidLat / points.length,
      centroidLng / points.length,
    );
  }

  void _updateRepairDisplay(List<RepairModel> repairs) {
    if (!mounted || _repairIcon == null) {
      return;
    }

    print(
        "🔧 REPAIR DEBUG: Updating display with ${repairs.length} repairs, zoom: $_currentZoom");

    Set<gmaps.Marker> loadedMarkers = {};
    Set<gmaps.Polygon> loadedPolygons = {};

    for (var repair in repairs) {
      print("🔧 Processing repair ID: ${repair.id}");
      print("   - Has ${repair.areaPoints.length} area points");
      print("   - Coordinates: ${repair.latitude}, ${repair.longitude}");

      // If repair has area points, handle polygon/marker logic
      if (repair.areaPoints.isNotEmpty) {
        List<gmaps.LatLng> polygonPoints = repair.areaPoints.map((point) {
          return gmaps.LatLng(point.latitude, point.longitude);
        }).toList();

        print("   - Polygon points: ${polygonPoints.length}");

        if (polygonPoints.length >= 3) {
          // Show polygon when zoomed in
          if (_currentZoom >= _zoomThreshold) {
            print("   - ✅ Creating polygon (zoomed in)");
            gmaps.Polygon polygon = gmaps.Polygon(
              polygonId: gmaps.PolygonId('repair_zone_${repair.id}'),
              points: polygonPoints,
              strokeColor: Colors.orange,
              strokeWidth: 3,
              fillColor: Colors.orange.withOpacity(0.2),
              consumeTapEvents: true,
              onTap: () {
                print("🔧 Polygon tapped for repair ${repair.id}");
                showRepairDetailsModal(context, repair.id);
              },
            );
            loadedPolygons.add(polygon);
          }

          // Calculate centroid for marker placement when zoomed out
          if (_currentZoom < _zoomThreshold) {
            print("   - ✅ Creating marker at centroid (zoomed out)");
            gmaps.LatLng centroid = _calculatePolygonCentroid(polygonPoints);

            gmaps.Marker iconMarker = gmaps.Marker(
              markerId: gmaps.MarkerId('repair_zone_icon_${repair.id}'),
              position: centroid,
              icon: _repairIcon!,
              onTap: () {
                print("🔧 Marker tapped for repair ${repair.id}");
                showRepairDetailsModal(context, repair.id);
              },
            );
            loadedMarkers.add(iconMarker);
          }
        } else {
          print("   - ❌ Not enough points for polygon (need 3+)");
        }
      }
      // If repair has no area points but has coordinates, create a regular marker
      else if (repair.latitude != 0.0 || repair.longitude != 0.0) {
        print("   - ✅ Creating regular marker at coordinates");
        gmaps.Marker marker = gmaps.Marker(
          markerId: gmaps.MarkerId('repair_${repair.id}'),
          position: gmaps.LatLng(repair.latitude, repair.longitude),
          icon: _repairIcon!,
          onTap: () {
            print("🔧 Regular marker tapped for repair ${repair.id}");
            showRepairDetailsModal(context, repair.id);
          },
        );
        loadedMarkers.add(marker);
      } else {
        print("   - ❌ No valid coordinates and no area points");
      }
    }

    print(
        "🔧 FINAL RESULT: ${loadedMarkers.length} markers, ${loadedPolygons.length} polygons");

    if (mounted) {
      setState(() {
        _markers = loadedMarkers;
        _polygons = loadedPolygons;
      });
    }
  }

  void _onMapCreated(gmaps.GoogleMapController controller) {
    _mapController = controller;
    print("🔧 Map created, controller ready");
  }

  void _onCameraMove(gmaps.CameraPosition position) {
    final newZoom = position.zoom;

    // Only update if zoom level crosses the threshold (like in parking screen)
    if ((_currentZoom < _zoomThreshold && newZoom >= _zoomThreshold) ||
        (_currentZoom >= _zoomThreshold && newZoom < _zoomThreshold)) {
      print("🔧 Zoom threshold crossed: $_currentZoom -> $newZoom");
      setState(() {
        _currentZoom = newZoom;
      });

      // Refresh repair display with new zoom level
      if (_currentRepairs.isNotEmpty) {
        _updateRepairDisplay(_currentRepairs);
      }
    } else {
      _currentZoom = newZoom;
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeMode = ref.watch(themeNotifierProvider);
    final isDarkMode = themeMode == ThemeMode.dark;

    // Watch for repair data changes
    final repairs = ref.watch(fetchAllRepairsProvider);

    if (_isMapStyleLoaded && _mapController != null) {
      final currentThemeMode = ref.read(themeNotifierProvider);
      final currentIsDarkMode = currentThemeMode == ThemeMode.dark;
      if (currentIsDarkMode != isDarkMode) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            _loadMapStyle();
          }
        });
      }
    }

    return Scaffold(
      body: repairs.when(
        data: (repairList) {
          print("🔧 REPAIR DATA: Received ${repairList.length} repairs");

          // Debug: Print each repair details
          for (int i = 0; i < repairList.length; i++) {
            final r = repairList[i];

            if (r.areaPoints.isNotEmpty) {
              for (int j = 0; j < r.areaPoints.length; j++) {
                final point = r.areaPoints[j];
                print("   Point $j: (${point.latitude}, ${point.longitude})");
              }
            }
          }

          _currentRepairs = repairList;

          // Update display when data changes
          if (mounted) {
            _updateRepairDisplay(_currentRepairs);
          }

          return Stack(
            children: [
              gmaps.GoogleMap(
                onMapCreated: _onMapCreated,
                onCameraMove: _onCameraMove,
                initialCameraPosition: gmaps.CameraPosition(
                  target: _myCurrentLocation,
                  zoom: _currentZoom,
                ),
                markers: _markers,
                polygons: _polygons,
                mapType: gmaps.MapType.normal,
                myLocationEnabled: true,
                compassEnabled: true,
                zoomControlsEnabled: true,
                tiltGesturesEnabled: true,
                mapToolbarEnabled: false,
                style: _mapStyle,
              ),
              // Zoom indicator (optional)
            ],
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (err, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.build_circle_outlined,
                size: 48,
                color: isDarkMode ? Colors.orange[300] : Colors.orange[600],
              ),
              const SizedBox(height: 16),
              Text(
                'Error loading repair data',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: isDarkMode ? Colors.white : const Color(0xFF424242),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                err.toString(),
                style: TextStyle(
                  fontSize: 14,
                  color: isDarkMode ? Colors.white70 : Colors.black54,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }
}
