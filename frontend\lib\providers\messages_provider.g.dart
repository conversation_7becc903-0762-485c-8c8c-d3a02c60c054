// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'messages_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchAllAnnouncementsHash() =>
    r'06733b454c2a6a7266f50d8a9f2180e2cc5d131b';

/// See also [fetchAllAnnouncements].
@ProviderFor(fetchAllAnnouncements)
final fetchAllAnnouncementsProvider =
    AutoDisposeFutureProvider<List<MessageModel>>.internal(
  fetchAllAnnouncements,
  name: r'fetchAllAnnouncementsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchAllAnnouncementsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchAllAnnouncementsRef
    = AutoDisposeFutureProviderRef<List<MessageModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
