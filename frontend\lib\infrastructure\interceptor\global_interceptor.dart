// infrastructure/http/global_http_client.dart
import 'package:dio/dio.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class GlobalHttpClient {
  static final GlobalHttpClient _instance = GlobalHttpClient._internal();
  factory GlobalHttpClient() => _instance;
  GlobalHttpClient._internal();

  late final Dio _dio;
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage();
  static bool _isRefreshing = false;

  // Only routes that require authentication - everything else is public by default
  static const Set<String> _authRequiredRoutes = {
    'Reports',
    'Users/RefreshToken',
    'Enums/ReportTypes',
    // Add more routes that need auth here
  };

  void initialize() {
    _dio = Dio(BaseOptions(
      baseUrl: dotenv.env['BASE_URL']!,
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 10),
    ));

    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // Only add token if route requires authentication
        if (_requiresAuth(options.path)) {
          final token = await _secureStorage.read(key: 'token');
          if (token != null && token.isNotEmpty) {
            options.headers['Authorization'] = 'Bearer $token';
          }
        }
        handler.next(options);
      },
      onError: (error, handler) async {
        // Only handle 401 for authenticated routes
        if (error.response?.statusCode == 401 &&
            _requiresAuth(error.requestOptions.path)) {
          final originalRequest = error.requestOptions;

          // Don't retry login/register requests
          if (_isAuthEndpoint(originalRequest.path)) {
            handler.next(error);
            return;
          }

          // Try to refresh token if not already refreshing
          if (!_isRefreshing) {
            final refreshResult = await _refreshToken();

            if (refreshResult != null &&
                refreshResult['isAuthSuccessful'] == true) {
              // Retry the original request with new token
              try {
                final newToken = await _secureStorage.read(key: 'token');
                originalRequest.headers['Authorization'] = 'Bearer $newToken';

                final response = await _dio.fetch(originalRequest);
                handler.resolve(response);
                return;
              } catch (e) {
                // If retry fails, continue with original error
              }
            }
          }
        }

        handler.next(error);
      },
    ));
  }

  // Check if route requires authentication
  bool _requiresAuth(String path) {
    // Only add token if route is explicitly marked as requiring auth
    return _authRequiredRoutes.any((route) => path.contains(route));
  }

  // Check if it's an auth-related endpoint
  bool _isAuthEndpoint(String path) {
    return path.contains('Login') ||
        path.contains('Register') ||
        path.contains('RefreshToken');
  }

  /// Refresh token method - moved from AuthenticationService
  Future<Map<String, dynamic>?> _refreshToken() async {
    if (_isRefreshing) return null;

    _isRefreshing = true;

    try {
      final refreshToken = await _secureStorage.read(key: 'refreshToken');
      final accessToken = await _secureStorage.read(key: 'token');

      if (refreshToken == null || refreshToken.isEmpty) {
        print('❌ No refresh token available');
        await _clearAuthData();
        return null;
      }

      print('🔄 Global HTTP Client: Refresh attempt');

      // Create a separate Dio instance for refresh to avoid interceptor loops
      final tempDio = Dio(BaseOptions(
        baseUrl: dotenv.env['BASE_URL']!,
        connectTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(seconds: 30),
      ));

      // Handle null access token properly
      final headers = <String, String>{};
      if (accessToken != null && accessToken.isNotEmpty) {
        headers['Authorization'] = 'Bearer $accessToken';
      } else {
        headers['Authorization'] = 'Bearer ';
      }

      final response = await tempDio.post(
        'Users/RefreshToken',
        options: Options(headers: headers),
        data: {'refreshToken': refreshToken},
      );

      if (response.statusCode == 200 && response.data != null) {
        final data = response.data;

        if (data['isAuthSuccessful'] == true) {
          final newAccessToken = data['accessToken'];
          final newRefreshToken = data['refreshToken'];

          if (newAccessToken != null) {
            await _secureStorage.write(key: 'token', value: newAccessToken);
            if (newRefreshToken != null) {
              await _secureStorage.write(
                  key: 'refreshToken', value: newRefreshToken);
            }

            print('✅ Global HTTP Client: Token refresh successful');
            return {
              'isAuthSuccessful': true,
              'accessToken': newAccessToken,
              'refreshToken': newRefreshToken,
            };
          }
        }
      }

      // If we get here, refresh failed
      print('❌ Global HTTP Client: Refresh failed');
      await _clearAuthData();
      return null;
    } catch (e) {
      print('❌ Global HTTP Client: Token refresh error: $e');

      // If 401 with empty bearer, try without Authorization header
      if (e is DioException && e.response?.statusCode == 401) {
        try {
          final refreshTokenRetry =
              await _secureStorage.read(key: 'refreshToken');
          if (refreshTokenRetry != null) {
            final tempDio = Dio(BaseOptions(
              baseUrl: dotenv.env['BASE_URL']!,
              connectTimeout: const Duration(seconds: 30),
              receiveTimeout: const Duration(seconds: 30),
            ));

            final response = await tempDio.post(
              'Users/RefreshToken',
              data: {'refreshToken': refreshTokenRetry},
            );

            if (response.statusCode == 200 && response.data != null) {
              final data = response.data;
              if (data['isAuthSuccessful'] == true) {
                final newAccessToken = data['accessToken'];
                final newRefreshToken = data['refreshToken'];

                if (newAccessToken != null) {
                  await _secureStorage.write(
                      key: 'token', value: newAccessToken);
                  if (newRefreshToken != null) {
                    await _secureStorage.write(
                        key: 'refreshToken', value: newRefreshToken);
                  }

                  print(
                      '✅ Global HTTP Client: Token refresh successful (retry)');
                  return {
                    'isAuthSuccessful': true,
                    'accessToken': newAccessToken,
                    'refreshToken': newRefreshToken,
                  };
                }
              }
            }
          }
        } catch (retryError) {
          print('❌ Global HTTP Client: Retry also failed: $retryError');
        }
      }

      await _clearAuthData();
      return null;
    } finally {
      _isRefreshing = false;
    }
  }

  // Clear authentication data
  Future<void> _clearAuthData() async {
    await _secureStorage.delete(key: 'token');
    await _secureStorage.delete(key: 'refreshToken');
    await _secureStorage.delete(key: 'username');
    await _secureStorage.delete(key: 'email');
    await _secureStorage.delete(key: 'userId');
    await _secureStorage.delete(key: 'role');
    await _secureStorage.delete(key: 'userFullName');
  }

  // Helper method to expose refresh token functionality
  Future<Map<String, dynamic>?> refreshTokenPublic() async {
    return await _refreshToken();
  }

  Dio get dio => _dio;
}
