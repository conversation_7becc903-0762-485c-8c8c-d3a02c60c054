import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/l10n/generated/l10n.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:frontend/providers/theme_provider.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'dart:async';

class LiveCameraScreen extends ConsumerStatefulWidget {
  const LiveCameraScreen({super.key});

  @override
  ConsumerState<LiveCameraScreen> createState() => _LiveCameraScreenState();
}

class _LiveCameraScreenState extends ConsumerState<LiveCameraScreen>
    with WidgetsBindingObserver {
  WebViewController? _webViewController;
  bool _isLoading = true;
  bool _hasError = false;
  bool _isFullscreen = false;
  Timer? _refreshTimer;
  int _webViewKey = 0; // Key to force WebView recreation

  // The live camera stream URL
  final String _streamUrl = 'https://x.optinet.bg/yambol/';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    _initializeWebView();
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    WidgetsBinding.instance.removeObserver(this);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
        overlays: SystemUiOverlay.values);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.resumed) {
      _handleAppResume();
    } else if (state == AppLifecycleState.paused) {
      _handleAppPause();
    }
  }

  void _handleAppPause() {
    _refreshTimer?.cancel();
  }

  void _handleAppResume() {
    if (!mounted) return;

    // Always recreate WebView when resuming from background
    // This handles the fullscreen exit issue
    _recreateWebView();
  }

  void _recreateWebView() {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _webViewKey++; // Force new WebView widget
    });

    // Small delay to ensure clean state
    Future.delayed(Duration(milliseconds: 200), () {
      if (mounted) {
        _initializeWebView();
      }
    });
  }

  void _initializeWebView() {
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.transparent)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            if (mounted) {
              setState(() {
                _isLoading = true;
                _hasError = false;
              });
            }
          },
          onPageFinished: (String url) {
            if (mounted) {
              setState(() {
                _isLoading = false;
              });
              _setupVideoHandling();
            }
          },
          onWebResourceError: (WebResourceError error) {
            if (mounted) {
              setState(() {
                _isLoading = false;
                _hasError = true;
              });
            }
          },
          onNavigationRequest: (NavigationRequest request) {
            return NavigationDecision.navigate;
          },
        ),
      )
      ..addJavaScriptChannel(
        'FlutterChannel',
        onMessageReceived: (JavaScriptMessage message) {
          _handleWebViewMessage(message.message);
        },
      )
      ..loadRequest(Uri.parse(_streamUrl));
  }

  void _setupVideoHandling() {
    if (_webViewController == null) return;

    _webViewController!.runJavaScript('''
      (function() {
        // Remove any existing listeners
        window.flutterHandlersSetup = window.flutterHandlersSetup || false;
        if (window.flutterHandlersSetup) return;
        window.flutterHandlersSetup = true;

        // Function to handle fullscreen changes
        function handleFullscreenChange() {
          var isFullscreen = !!(document.fullscreenElement || 
                                document.webkitFullscreenElement || 
                                document.mozFullScreenElement || 
                                document.msFullscreenElement);
          
          FlutterChannel.postMessage(JSON.stringify({
            type: 'fullscreen',
            isFullscreen: isFullscreen
          }));
        }
        

        // Add fullscreen event listeners
        document.addEventListener('fullscreenchange', handleFullscreenChange);
        document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
        document.addEventListener('mozfullscreenchange', handleFullscreenChange);
        document.addEventListener('MSFullscreenChange', handleFullscreenChange);

        // Monitor for video elements
        function setupVideoMonitoring() {
          var videos = document.querySelectorAll('video');
          videos.forEach(function(video) {
            video.addEventListener('loadstart', function() {
              console.log('Video loading started');
            });
            
            video.addEventListener('error', function() {
              console.log('Video error occurred');
              FlutterChannel.postMessage(JSON.stringify({
                type: 'video_error'
              }));
            });

            video.addEventListener('ended', function() {
              console.log('Video ended, attempting reload');
              setTimeout(function() {
                video.load();
              }, 1000);
            });
          });
        }

        // Setup video monitoring immediately and periodically
        setupVideoMonitoring();
        setInterval(setupVideoMonitoring, 5000);

        // Visibility change handler
        document.addEventListener('visibilitychange', function() {
          if (document.visibilityState === 'visible') {
            FlutterChannel.postMessage(JSON.stringify({
              type: 'visibility_change',
              visible: true
            }));
          }
        });
      })();
    ''');
  }

  void _handleWebViewMessage(String message) {
    try {
      // Simple JSON parsing for the message
      if (message.contains('fullscreen')) {
        if (message.contains('true')) {
          _isFullscreen = true;
        } else {
          _isFullscreen = false;
          _webViewController?.reload();
          // When exiting fullscreen, schedule a WebView recreation
          Future.delayed(Duration(milliseconds: 500), () {
            if (mounted && !_isFullscreen) {
              _recreateWebView();
            }
          });
        }
      } else if (message.contains('video_error')) {
        if (mounted) {
          setState(() {
            _hasError = true;
          });
        }
      } else if (message.contains('visibility_change')) {
        // Handle visibility change if needed
        Future.delayed(Duration(milliseconds: 300), () {
          if (mounted) {
            _refreshStream();
          }
        });
      }
    } catch (e) {
      print('Error parsing WebView message: $e');
    }
  }

  Future<void> _refreshStream() async {
    if (!mounted) return;

    // For refresh, also recreate the WebView to ensure clean state
    _recreateWebView();
  }

  Widget _buildStatusCard(bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? Color(0xFF333333) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 6,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: Row(
        children: [
          // Live indicator
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _hasError ? Colors.red : Color(0xFF22D400),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 6,
                  height: 6,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                ),
                SizedBox(width: 6),
                Text(
                  _hasError ? S.of(context).offline : S.of(context).live,
                  style: GoogleFonts.roboto(
                    fontSize: 12,
                    fontWeight: FontWeight.w700,
                    color: Colors.white,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _hasError
                      ? S.of(context).connection_failed
                      : S.of(context).stream_active,
                  style: GoogleFonts.roboto(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: isDarkMode ? Colors.white : Color(0xFF626262),
                  ),
                ),
                SizedBox(height: 2),
                Text(
                  _hasError
                      ? S.of(context).unable_to_connect
                      : S.of(context).real_time_video_feed,
                  style: GoogleFonts.roboto(
                    fontSize: 13,
                    fontWeight: FontWeight.w400,
                    color: isDarkMode ? Colors.white70 : Color(0xFF888888),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControlCard(bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDarkMode ? Color(0xFF333333) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 6,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.settings,
                color: Color(0xFF22D400),
                size: 24,
              ),
              SizedBox(width: 8),
              Text(
                S.of(context).camera_controls,
                style: GoogleFonts.roboto(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: isDarkMode ? Colors.white : Color(0xFF626262),
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildControlButton(
                  context: context,
                  isDarkMode: isDarkMode,
                  icon: Icons.refresh_rounded,
                  label: S.of(context).refresh,
                  onTap: _refreshStream,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required BuildContext context,
    required bool isDarkMode,
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: Color(0xFF22D400).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Color(0xFF22D400).withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: Color(0xFF22D400),
              size: 18,
            ),
            SizedBox(width: 8),
            Text(
              label,
              style: GoogleFonts.roboto(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: isDarkMode ? Colors.white : Color(0xFF626262),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStreamContainer(bool isDarkMode) {
    return Container(
      width: double.infinity,
      height: 300,
      decoration: BoxDecoration(
        color: isDarkMode ? Color(0xFF333333) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 6,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: _isLoading
            ? _buildLoadingContent()
            : _hasError
                ? _buildErrorContent(isDarkMode)
                : _webViewController != null
                    ? WebViewWidget(
                        controller: _webViewController!,
                        key:
                            ValueKey(_webViewKey), // Force rebuild with new key
                      )
                    : _buildLoadingContent(),
      ),
    );
  }

  Widget _buildLoadingContent() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFF22D400).withValues(alpha: 0.1),
            Color(0xFF22D400).withValues(alpha: 0.05),
          ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: Color(0xFF22D400),
              strokeWidth: 2,
            ),
            SizedBox(height: 16),
            Text(
              S.of(context).loading_stream,
              style: GoogleFonts.roboto(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Color(0xFF626262),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorContent(bool isDarkMode) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.red.withValues(alpha: 0.1),
            Colors.red.withValues(alpha: 0.05),
          ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.signal_wifi_connected_no_internet_4_rounded,
              size: 48,
              color: Colors.red.withValues(alpha: 0.8),
            ),
            SizedBox(height: 16),
            Text(
              S.of(context).connection_failed_title,
              style: GoogleFonts.roboto(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: isDarkMode ? Colors.white : Color(0xFF626262),
              ),
            ),
            SizedBox(height: 8),
            Text(
              S.of(context).tap_refresh_to_try_again,
              style: GoogleFonts.roboto(
                fontSize: 14,
                color: isDarkMode ? Colors.white70 : Color(0xFF888888),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHelpSection(bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Color(0xFF22D400).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Color(0xFF22D400).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.help_outline,
            color: Color(0xFF22D400),
            size: 20,
          ),
          SizedBox(width: 8),
          Expanded(
            child: Text(
              S.of(context).help_text,
              style: GoogleFonts.roboto(
                fontSize: 13,
                fontWeight: FontWeight.w400,
                color: isDarkMode ? Colors.white : Color(0xFF626262),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final themeMode = ref.watch(themeNotifierProvider);
    final isDarkMode = themeMode == ThemeMode.dark;
    final theme = Theme.of(context);

    return PopScope(
      onPopInvokedWithResult: (bool didPop, Object? result) {},
      child: Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        body: SafeArea(
          child: SingleChildScrollView(
            child: Column(
              children: [
                // Content Section
                Padding(
                  padding: MediaQuery.of(context).orientation ==
                          Orientation.landscape
                      ? const EdgeInsets.all(16.0)
                      : const EdgeInsets.all(20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Status Card
                      _buildStatusCard(isDarkMode),

                      SizedBox(height: 16),

                      // Stream Container
                      _buildStreamContainer(isDarkMode),

                      SizedBox(height: 16),

                      // Control Card
                      _buildControlCard(isDarkMode),

                      SizedBox(height: 16),

                      // Help Section
                      _buildHelpSection(isDarkMode),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
