import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/providers/theme_provider.dart';
import 'package:webview_flutter/webview_flutter.dart';

class WebViewPage extends ConsumerStatefulWidget {
  const WebViewPage({super.key});

  @override
  ConsumerState<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends ConsumerState<WebViewPage> {
  late final WebViewController controller;
  bool isLoading = true;
  String? errorMessage;
  ThemeMode? originalTheme;
  ThemeNotifier? themeNotifier; // Make nullable for safety
  bool hasChangedTheme = false;

  @override
  void initState() {
    super.initState();

    // Store the current theme and set to light mode
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Store the notifier reference first
      themeNotifier = ref.read(themeNotifierProvider.notifier);
      originalTheme = ref.read(themeNotifierProvider);

      if (originalTheme == ThemeMode.dark) {
        themeNotifier?.toggleTheme();
        hasChangedTheme = true; // Mark that we changed the theme
      }
    });

    controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.white)
      ..setUserAgent(
          'Mozilla/5.0 (Linux; Android 10) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36')
      ..enableZoom(true)
      ..setNavigationDelegate(_createNavigationDelegate())
      ..addJavaScriptChannel(
        'FlutterChannel',
        onMessageReceived: (JavaScriptMessage message) {
          debugPrint('Message from JS: ${message.message}');
        },
      )
      ..loadRequest(Uri.parse('https://svatbi.yambol.bg'));
  }

  NavigationDelegate _createNavigationDelegate() {
    return NavigationDelegate(
      onProgress: (int progress) {
        debugPrint('Loading progress: $progress%');
      },
      onPageStarted: (String url) {
        if (mounted) {
          setState(() {
            isLoading = true;
            errorMessage = null;
          });
        }
        debugPrint('Page started loading: $url');
      },
      onPageFinished: (String url) {
        if (mounted) {
          setState(() {
            isLoading = false;
          });
        }
        debugPrint('Page finished loading: $url');
      },
      onHttpError: (HttpResponseError error) {
        if (mounted) {
          setState(() {
            errorMessage = 'HTTP Error: ${error.response?.statusCode}';
          });
        }
        debugPrint('HTTP error: ${error.response?.statusCode}');
      },
      onWebResourceError: (WebResourceError error) {
        if (mounted) {
          setState(() {
            isLoading = false;
            errorMessage = 'Error: ${error.description}';
          });
        }
        debugPrint('WebView error: ${error.description}');
      },
      onNavigationRequest: (NavigationRequest request) {
        if (request.url.startsWith('https://svatbi.yambol.bg')) {
          return NavigationDecision.navigate;
        }
        return NavigationDecision.navigate;
      },
    );
  }

  @override
  void dispose() {
    _restoreTheme();
    super.dispose();
  }

  void _restoreTheme() {
    if (hasChangedTheme && themeNotifier != null) {
      try {
        themeNotifier!.toggleTheme();
        hasChangedTheme = false;
      } catch (e) {
        debugPrint('Error restoring theme: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          WebViewWidget(controller: controller),
          if (isLoading)
            const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                ],
              ),
            ),
          if (errorMessage != null)
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    errorMessage!,
                    style: const TextStyle(fontSize: 16),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      if (mounted) {
                        setState(() {
                          errorMessage = null;
                        });
                        controller.reload();
                      }
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }
}
