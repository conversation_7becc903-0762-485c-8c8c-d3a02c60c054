import 'package:dio/dio.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:frontend/models/get_repairs_by_id_command.dart';
import 'package:frontend/models/get_repairs_command.dart';

class RepairService {
  static final Dio _dio = Dio(BaseOptions(
    baseUrl: dotenv.env['BASE_URL']!, // Load BASE_URL from .env
  ));

  /// **Fetch All Repairs**
  static Future<List<RepairModel>> fetchRepairs() async {
    const String repairEndpoint = 'Repairs/Mobile';

    try {
      print('REPAIR REQUEST: Fetching all repairs');

      final response = await _dio.get(repairEndpoint);

      if (response.statusCode == 200) {
        print('REPAIR RESPONSE: ${response.data}');

        List<dynamic> jsonData = response.data;
        return jsonData.map((item) => RepairModel.fromJson(item)).toList();
      } else {
        throw Exception(
            'Failed to load repairs with status ${response.statusCode}');
      }
    } on DioException catch (e) {
      print("❌ Repair Service Dio Error: ${e.response?.statusCode}");
      print("📩 Response Data: ${e.response?.data}");
      print("📝 Error Message: ${e.message}");
      return [];
    } catch (e) {
      print("⚠ Repair Service Unexpected Error: $e");
      return [];
    }
  }

  /// **Fetch Repair by ID**
  static Future<RepairDetailsModel?> fetchRepairById(int id,
      {String? languageCode}) async {
    final String repairEndpoint = 'Repairs/Mobile/$id';

    // Build query parameters for language code if provided
    final Map<String, dynamic> queryParams = {};
    if (languageCode != null && languageCode.isNotEmpty) {
      queryParams['languageCode'] = languageCode;
    }

    print(
        'REPAIR REQUEST: Fetching repair with ID $id with language: $languageCode');

    try {
      final response = await _dio.get(repairEndpoint,
          queryParameters: queryParams.isNotEmpty ? queryParams : null);

      if (response.statusCode == 200) {
        print('REPAIR RESPONSE: ${response.data}');
        return RepairDetailsModel.fromJson(response.data);
      } else {
        throw Exception(
            'Failed to load repair with status ${response.statusCode}');
      }
    } on DioException catch (e) {
      print("❌ Repair Service Dio Error: ${e.response?.statusCode}");
      print("📩 Response Data: ${e.response?.data}");
      print("📝 Error Message: ${e.message}");
      return null;
    } catch (e) {
      print("⚠ Repair Service Unexpected Error: $e");
      return null;
    }
  }
}
