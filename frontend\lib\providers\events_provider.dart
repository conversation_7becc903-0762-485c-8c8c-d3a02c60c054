import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/infrastructure/events/events_service.dart';
import 'package:frontend/models/get_events_by_id_command.dart';
import 'package:frontend/providers/calendar_provider.dart';
import 'package:frontend/providers/filter_provider.dart';
import 'package:frontend/providers/view_mode_provider.dart';
import 'package:intl/intl.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:frontend/models/get_events_command.dart';

part 'events_provider.g.dart';

// Simple search state provider
@riverpod
class SearchState extends _$SearchState {
  @override
  String build() => ''; // Default empty search

  void setSearch(String search) {
    state = search.trim();
  }

  void clearSearch() {
    state = '';
  }
}

// Updated provider that includes search functionality
@riverpod
Future<List<EventsGetModel>> eventsForCurrentState(Ref ref) async {
  // Watch all the UI state providers
  final viewMode = ref.watch(viewModeStateProvider);
  final filterState = ref.watch(filterStateProvider);
  final calendarState = ref.watch(calendarNotifierProvider);
  final searchQuery = ref.watch(searchStateProvider); // Watch search state

  final isCalendarView = viewMode == ViewMode.calendar;

  // Determine filters based on current view
  List<String>? categories;
  String? fromDate;
  String? toDate;
  String? search =
      searchQuery.isEmpty ? null : searchQuery; // Only pass search if not empty

  if (isCalendarView) {
    // Calendar view: filter by selected date
    categories = null; // Show all categories
    if (calendarState.selectedDate != null) {
      final dateStr =
          DateFormat('yyyy-MM-dd').format(calendarState.selectedDate!);
      fromDate = dateStr;
      toDate = dateStr;
    }
  } else {
    // List view: filter by categories
    switch (filterState) {
      case FilterType.all:
        categories = null;
        break;
      case FilterType.sportEvents:
        categories = ['Sports'];
        break;
      case FilterType.cultureEvents:
        categories = ['Culture'];
        break;
      case FilterType.celebrationEvents:
        categories = ['Celebration'];
        break;
    }
    fromDate = null;
    toDate = null;
  }

  // Fetch events with current filters including search
  return EventsMobileService.fetchEvents(
    categories: categories,
    fromDate: fromDate,
    toDate: toDate,
    search: search, // Add search parameter
    languageCode: 'bg',
  );
}

// Provider to fetch a specific event by ID with language code
@riverpod
Future<EventsDetailsModel?> fetchEventById(
    Ref ref, ({int id, String languageCode}) params) async {
  return EventsMobileService.fetchEventById(params.id,
      languageCode: params.languageCode);
}
