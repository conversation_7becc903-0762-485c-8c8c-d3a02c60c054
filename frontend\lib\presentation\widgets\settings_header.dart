import 'package:flutter/material.dart';
import '../../l10n/generated/l10n.dart';
import '../../providers/auth_providers.dart';

class SettingsHeader extends StatelessWidget {
  // ✅ Changed to StatelessWidget
  final UserAuthState userState; // ✅ Accept userState as parameter

  const SettingsHeader({
    super.key,
    required this.userState,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // ✅ Profile Section with Background Image
        Container(
          width: double.infinity,
          height: 200,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            image: const DecorationImage(
              image: AssetImage('assets/images/settings_image.png'),
              fit: BoxFit.cover,
            ),
            color: Colors.green.withOpacity(0.4),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircleAvatar(
                radius: 40,
                backgroundColor: Colors.white,
                child: userState.isAuthenticated
                    ? Text(
                        userState.username?.substring(0, 1).toUpperCase() ??
                            'U',
                        style: const TextStyle(
                          fontSize: 30,
                          color: Colors.black45,
                          fontWeight: FontWeight.bold,
                        ),
                      )
                    : const Icon(Icons.person, size: 50, color: Colors.black45),
              ),
              const SizedBox(height: 8),
              Text(
                userState.isAuthenticated
                    ? (userState.username ?? S.of(context).guest)
                    : S.of(context).guest,
                style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white),
              ),
              if (userState.isAuthenticated && userState.email != null) ...[
                const SizedBox(height: 4),
                Text(
                  userState.email!,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.white70,
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }
}
