import 'dart:async';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../infrastructure/authentifiaction/authentification_service.dart';
import '../models/register_command.dart';
import '../models/login_command.dart';
import '../core/exceptions/app_exceptions.dart';

part 'auth_providers.g.dart';

// User Auth State Model
class UserAuthState {
  final bool isAuthenticated;
  final String? username;
  final String? email;
  final String? userId;
  final String? role;
  final String? userFullName;

  const UserAuthState({
    this.isAuthenticated = false,
    this.username,
    this.email,
    this.userId,
    this.role,
    this.userFullName,
  });

  UserAuthState copyWith({
    bool? isAuthenticated,
    String? username,
    String? email,
    String? userId,
    String? role,
    String? userFullName,
  }) {
    return UserAuthState(
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      username: username ?? this.username,
      email: email ?? this.email,
      userId: userId ?? this.userId,
      role: role ?? this.role,
      userFullName: userFullName ?? this.userFullName,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserAuthState &&
        other.isAuthenticated == isAuthenticated &&
        other.username == username &&
        other.email == email &&
        other.userId == userId &&
        other.role == role &&
        other.userFullName == userFullName;
  }

  @override
  int get hashCode {
    return isAuthenticated.hashCode ^
        username.hashCode ^
        email.hashCode ^
        userId.hashCode ^
        role.hashCode ^
        userFullName.hashCode;
  }
}

@riverpod
class AuthNotifier extends _$AuthNotifier {
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage();
  bool _hasCheckedInitialAuth = false;

  @override
  Future<void> build() async {
    // REMOVED: AuthenticationService.initializeInterceptors()
    // This is now handled by the global HTTP client in main.dart

    // Only check auth state once during initialization
    if (!_hasCheckedInitialAuth) {
      await _checkAuthState();
      _hasCheckedInitialAuth = true;
    }
  }

  /// **CLEAN: Check auth state - no timer logic**
  Future<void> _checkAuthState() async {
    try {
      print('🔐 AUTH PROVIDER: Checking auth state...');

      final token = await _secureStorage.read(key: 'token');
      final refreshToken = await _secureStorage.read(key: 'refreshToken');

      print('🔑 Token check: ${token != null ? "EXISTS" : "NULL"}');
      print(
          '🔄 Refresh token check: ${refreshToken != null ? "EXISTS" : "NULL"}');

      if (token != null && token.isNotEmpty) {
        // Check if token is expired
        final isExpired = await AuthenticationService.isTokenExpired();

        if (isExpired) {
          print('🔐 Stored token is expired, attempting refresh...');
          final refreshResult = await AuthenticationService.refreshToken();

          if (refreshResult == null ||
              refreshResult['isAuthSuccessful'] != true) {
            print('🔐 Token refresh failed, logging out user');
            await _clearUserData();
            return;
          }

          print('🔐 Token refreshed successfully');
        }

        // Get user info and set authenticated state
        final userInfoFromJWT =
            await AuthenticationService.getUserInfoFromToken();

        final username = userInfoFromJWT?['username'] ??
            await _secureStorage.read(key: 'username');
        final email = userInfoFromJWT?['email'] ??
            await _secureStorage.read(key: 'email');
        final userId = userInfoFromJWT?['userId'] ??
            await _secureStorage.read(key: 'userId');
        final role =
            userInfoFromJWT?['role'] ?? await _secureStorage.read(key: 'role');
        final userFullName = userInfoFromJWT?['fullName'] ??
            userInfoFromJWT?['name'] ??
            await _secureStorage.read(key: 'userFullName');

        print('🔐 Found valid auth data, setting user as authenticated');
        print('👤 User info: username=$username, email=$email, role=$role');

        // Update user state - THIS WILL TRIGGER MAIN APP TIMER
        ref.read(userAuthNotifierProvider.notifier).setUser(
              username: username,
              email: email,
              userId: userId,
              role: role,
              userFullName: userFullName,
            );
      } else {
        print('🔐 No stored auth data found, setting as not authenticated');
        ref.read(userAuthNotifierProvider.notifier).setNotAuthenticated();
      }
    } catch (e) {
      print('🔐 Error checking auth state: $e');
      await _clearUserData();
    }
  }

  /// **Clear user data helper**
  Future<void> _clearUserData() async {
    ref.read(userAuthNotifierProvider.notifier).setNotAuthenticated();
  }

  /// **Login User - no timer setup, main app handles it**
  Future<void> loginUser(LoginCommand command) async {
    state = const AsyncLoading();
    try {
      final response = await AuthenticationService.login(command);

      if (response.statusCode == 200) {
        final responseData = response.data;
        final accessToken = responseData['accessToken'];

        if (accessToken != null) {
          print('🔐 Login successful, extracting user info');

          final userInfoFromJWT =
              await AuthenticationService.getUserInfoFromToken();

          // Extract user data
          final email = userInfoFromJWT?['email'] ??
              responseData['user']?['email'] ??
              responseData['email'] ??
              responseData['userEmail'];

          final username = userInfoFromJWT?['username'] ??
              userInfoFromJWT?['preferred_username'] ??
              responseData['user']?['username'] ??
              responseData['username'] ??
              responseData['user']?['name'] ??
              responseData['name'] ??
              command.username;

          final userId = userInfoFromJWT?['userId'] ??
              userInfoFromJWT?['sub'] ??
              responseData['user']?['id'] ??
              responseData['userId'] ??
              responseData['user']?['userId'];

          final role = userInfoFromJWT?['role'] ??
              responseData['user']?['role'] ??
              responseData['role'];

          final userFullName = userInfoFromJWT?['fullName'] ??
              userInfoFromJWT?['name'] ??
              responseData['user']?['fullName'] ??
              responseData['userFullName'] ??
              responseData['fullName'];

          // Store user info
          if (username != null)
            await _secureStorage.write(key: 'username', value: username);
          if (email != null)
            await _secureStorage.write(key: 'email', value: email);
          if (userId != null)
            await _secureStorage.write(key: 'userId', value: userId.toString());
          if (role != null)
            await _secureStorage.write(key: 'role', value: role);
          if (userFullName != null)
            await _secureStorage.write(
                key: 'userFullName', value: userFullName);

          print('🔐 Login successful, updating user state');
          print('👤 User: $username, Email: $email, Role: $role');

          // Update user state - THIS WILL TRIGGER MAIN APP TIMER
          ref.read(userAuthNotifierProvider.notifier).setUser(
                username: username,
                email: email,
                userId: userId?.toString(),
                role: role,
                userFullName: userFullName,
              );

          state = const AsyncData(null);
        } else {
          state = AsyncError(
              const InvalidCredentialsException(), StackTrace.current);
        }
      } else {
        state = AsyncError(
            ServerException(
                "Login failed with status code ${response.statusCode}"),
            StackTrace.current);
      }
    } on AppException catch (e) {
      state = AsyncError(e, StackTrace.current);
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
    }
  }

  /// **Register User**
  Future<void> registerUser(RegisterCommand command) async {
    state = const AsyncLoading();
    try {
      final response = await AuthenticationService.register(command);

      if (response.statusCode == 200 || response.statusCode == 201) {
        state = const AsyncData(null);
      } else {
        state = AsyncError(
            ServerException(
                "Registration failed with status code ${response.statusCode}"),
            StackTrace.current);
      }
    } on AppException catch (e) {
      state = AsyncError(e, StackTrace.current);
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
    }
  }

  /// **CLEAN: Logout - just clear data, main app handles timers**
  Future<void> logoutUser() async {
    try {
      print('🔐 AUTH PROVIDER: Logging out user...');

      // Clear secure storage
      await _secureStorage.delete(key: 'token');
      await _secureStorage.delete(key: 'refreshToken');
      await _secureStorage.delete(key: 'username');
      await _secureStorage.delete(key: 'email');
      await _secureStorage.delete(key: 'userId');
      await _secureStorage.delete(key: 'role');
      await _secureStorage.delete(key: 'userFullName');

      // Clear user state - THIS WILL TRIGGER MAIN APP TO CLEAR TIMERS
      ref.read(userAuthNotifierProvider.notifier).logout();

      state = const AsyncData(null);
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
    }
  }

  /// **Resend Verification Email API Call**
  Future<void> resendVerificationEmail(String email) async {
    state = const AsyncLoading();
    try {
      await AuthenticationService.resendVerification(email);
      state = const AsyncData(null);
    } on AppException catch (e) {
      state = AsyncError(e, StackTrace.current);
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
    }
  }

  /// **Verify Email API Call**
  Future<void> verifyEmail(String userId, String confirmationToken) async {
    state = const AsyncLoading();
    try {
      await AuthenticationService.verifyEmail(userId, confirmationToken);
      state = const AsyncData(null);
    } on AppException catch (e) {
      state = AsyncError(e, StackTrace.current);
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
    }
  }
}

// User Auth Notifier Provider - enhanced with JWT info
@riverpod
class UserAuthNotifier extends _$UserAuthNotifier {
  @override
  UserAuthState build() {
    return const UserAuthState(); // Start with default state (not authenticated)
  }

  void setUser({
    String? username,
    String? email,
    String? userId,
    String? role,
    String? userFullName,
  }) {
    final newState = state.copyWith(
      isAuthenticated: true,
      username: username,
      email: email,
      userId: userId,
      role: role,
      userFullName: userFullName,
    );

    // Only update if state actually changed
    if (newState != state) {
      state = newState;
      print('🔐 UserAuthNotifier.setUser called:');
      print('  isAuthenticated: ${state.isAuthenticated}');
      print('  username: ${state.username}');
      print('  email: ${state.email}');
      print('  role: ${state.role}');
      print('  userId: ${state.userId}');
    }
  }

  void setNotAuthenticated() {
    final newState = state.copyWith(
      isAuthenticated: false,
      username: null,
      email: null,
      userId: null,
      role: null,
      userFullName: null,
    );

    // Only update if state actually changed
    if (newState != state) {
      state = newState;
      print('🔐 UserAuthNotifier.setNotAuthenticated called:');
      print('  isAuthenticated: ${state.isAuthenticated}');
    }
  }

  void logout() {
    const newState = UserAuthState(isAuthenticated: false);

    // Only update if state actually changed
    if (newState != state) {
      state = newState;
      print('🔐 UserAuthNotifier.logout called:');
      print('  isAuthenticated: ${state.isAuthenticated}');
    }
  }

  // Getter methods
  String? get role => state.role;
  String? get userFullName => state.userFullName;
  String? get username => state.username;
  String? get email => state.email;
  String? get userId => state.userId;
  bool get isAuthenticated => state.isAuthenticated;

  bool hasRole(String roleToCheck) {
    return state.role?.toLowerCase() == roleToCheck.toLowerCase();
  }

  bool hasAnyRole(List<String> rolesToCheck) {
    if (state.role == null) return false;
    return rolesToCheck
        .any((role) => state.role!.toLowerCase() == role.toLowerCase());
  }
}
