import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/domain/enums/favorite_filter_type.dart';
import 'package:frontend/providers/theme_provider.dart';
import 'package:frontend/l10n/generated/l10n.dart';
import '../../../providers/favorites/favorites_filter_provider.dart';

class FavoritesFilterChips extends ConsumerWidget {
  const FavoritesFilterChips({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isDarkMode = ref.watch(themeNotifierProvider) == ThemeMode.dark;
    final selectedFilter = ref.watch(favouriteStateProvider);
    final filterNotifier = ref.read(favouriteStateProvider.notifier);
    final screenSize = MediaQuery.of(context).size;
    final isLandscape = screenSize.width > screenSize.height;

    final filters = [
      _FilterData(S.of(context).all, FavoriteFilterType.all),
      _FilterData(S.of(context).tourism, FavoriteFilterType.tourism),
      _FilterData(S.of(context).generalInfo, FavoriteFilterType.generalInfo),
    ];

    // In landscape mode, show fewer chips or adapt layout
    if (isLandscape) {
      return _buildLandscapeLayout(
        filters,
        selectedFilter,
        filterNotifier,
        isDarkMode,
        context,
      );
    }

    // Portrait layout (original)
    return _buildPortraitLayout(
      filters,
      selectedFilter,
      filterNotifier,
      isDarkMode,
      context,
    );
  }

  Widget _buildLandscapeLayout(
    List<_FilterData> filters,
    FavoriteFilterType selectedFilter,
    dynamic filterNotifier,
    bool isDarkMode,
    BuildContext context,
  ) {
    // In landscape, show only 2 chips + more button to save space
    final sortedFilters = [
      ...filters.where((f) => f.type == selectedFilter),
      ...filters.where((f) => f.type != selectedFilter),
    ];

    final visibleFilters = sortedFilters.take(2).toList();

    return SizedBox(
      height: 28, // Reduced height for landscape
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            ...visibleFilters.map(
              (filter) => _FilterChip(
                filter: filter,
                isSelected: selectedFilter == filter.type,
                isDarkMode: isDarkMode,
                isCompact: true, // Compact mode for landscape
                onTap: () => filterNotifier.setFilter(filter.type),
              ),
            ),
            _MoreChip(
              isCompact: true,
              onTap: () => _showFilterBottomSheet(
                context,
                filters,
                selectedFilter,
                filterNotifier,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPortraitLayout(
    List<_FilterData> filters,
    FavoriteFilterType selectedFilter,
    dynamic filterNotifier,
    bool isDarkMode,
    BuildContext context,
  ) {
    // Move selected filter to the front
    final sortedFilters = [
      ...filters.where((f) => f.type == selectedFilter),
      ...filters.where((f) => f.type != selectedFilter),
    ];

    final visibleFilters = sortedFilters.take(3).toList();

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.only(bottom: 10),
      child: Row(
        children: [
          ...visibleFilters.map(
            (filter) => _FilterChip(
              filter: filter,
              isSelected: selectedFilter == filter.type,
              isDarkMode: isDarkMode,
              isCompact: false,
              onTap: () => filterNotifier.setFilter(filter.type),
            ),
          ),
          _MoreChip(
            isCompact: false,
            onTap: () => _showFilterBottomSheet(
              context,
              filters,
              selectedFilter,
              filterNotifier,
            ),
          ),
        ],
      ),
    );
  }

  void _showFilterBottomSheet(
    BuildContext context,
    List<_FilterData> filters,
    FavoriteFilterType selectedFilter,
    dynamic filterNotifier,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (_) => _FilterBottomSheet(
        filters: filters,
        selectedType: selectedFilter,
        onSelected: (type) {
          filterNotifier.setFilter(type);
          Navigator.of(context).pop();
        },
      ),
    );
  }
}

class _FilterChip extends StatelessWidget {
  final _FilterData filter;
  final bool isSelected;
  final bool isDarkMode;
  final bool isCompact;
  final VoidCallback onTap;

  const _FilterChip({
    required this.filter,
    required this.isSelected,
    required this.isDarkMode,
    required this.isCompact,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final chipHeight = isCompact ? 28.0 : 32.0;
    final fontSize = isCompact ? 12.0 : 14.0;
    final horizontalPadding = isCompact ? 8.0 : 12.0;
    final rightMargin = isCompact ? 6.0 : 10.0;

    return Padding(
      padding: EdgeInsets.only(right: rightMargin),
      child: GestureDetector(
        onTap: onTap,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          constraints: BoxConstraints(minWidth: isCompact ? 70 : 85),
          height: chipHeight,
          padding: EdgeInsets.symmetric(
            horizontal: horizontalPadding,
            vertical: 3,
          ),
          decoration: BoxDecoration(
            color: isSelected
                ? const Color(0xFF22D400)
                : (isDarkMode ? const Color(0xFF333333) : Colors.white),
            borderRadius: BorderRadius.circular(chipHeight / 2),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Center(
            child: Text(
              filter.label,
              style: TextStyle(
                fontFamily: 'Roboto',
                fontWeight: FontWeight.w500,
                fontSize: fontSize,
                letterSpacing: 0.25,
                color: isSelected
                    ? const Color(0xDE000000)
                    : (isDarkMode ? Colors.white : const Color(0xDE000000)),
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
        ),
      ),
    );
  }
}

class _MoreChip extends StatelessWidget {
  final bool isCompact;
  final VoidCallback onTap;

  const _MoreChip({
    required this.isCompact,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final chipHeight = isCompact ? 28.0 : 32.0;
    final fontSize = isCompact ? 12.0 : 14.0;
    final iconSize = isCompact ? 16.0 : 18.0;
    final horizontalPadding = isCompact ? 8.0 : 12.0;

    return Padding(
      padding: EdgeInsets.only(right: isCompact ? 6.0 : 10.0),
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          height: chipHeight,
          padding: EdgeInsets.symmetric(
            horizontal: horizontalPadding,
            vertical: 3,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(chipHeight / 2),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Center(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.expand_more,
                  size: iconSize,
                  color: Colors.black87,
                ),
                if (!isCompact) ...[
                  const SizedBox(width: 4),
                  Text(
                    S.of(context).more,
                    style: TextStyle(
                      fontFamily: 'Roboto',
                      fontWeight: FontWeight.w500,
                      fontSize: fontSize,
                      letterSpacing: 0.25,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _FilterBottomSheet extends StatelessWidget {
  final List<_FilterData> filters;
  final FavoriteFilterType selectedType;
  final void Function(FavoriteFilterType) onSelected;

  const _FilterBottomSheet({
    required this.filters,
    required this.selectedType,
    required this.onSelected,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final screenSize = MediaQuery.of(context).size;
    final isLandscape = screenSize.width > screenSize.height;

    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header with Close Button
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  S.of(context).selectCategory,
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
            const SizedBox(height: 12),
            // Responsive grid layout
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: filters.length,
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount:
                    isLandscape ? 3 : 2, // More columns in landscape
                mainAxisSpacing: 12,
                crossAxisSpacing: 12,
                childAspectRatio:
                    isLandscape ? 2.5 : 3.5, // Adjust aspect ratio
              ),
              itemBuilder: (context, index) {
                final filter = filters[index];
                final isSelected = filter.type == selectedType;

                return _FilterChip(
                  filter: filter,
                  isSelected: isSelected,
                  isDarkMode: isDarkMode,
                  isCompact: isLandscape, // Use compact mode in landscape
                  onTap: () => onSelected(filter.type),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

class _FilterData {
  final String label;
  final FavoriteFilterType type;

  _FilterData(this.label, this.type);
}
