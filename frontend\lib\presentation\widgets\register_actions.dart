import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/core/exceptions/utils/error_translator.dart';
import 'package:go_router/go_router.dart';
import '../../l10n/generated/l10n.dart';
import '../../models/register_command.dart';
import '../../providers/auth_providers.dart';
import 'dart:typed_data';

class RegisterActions extends ConsumerWidget {
  final GlobalKey<FormState> formKey;
  final TextEditingController emailController;
  final TextEditingController passwordController;
  final TextEditingController confirmPasswordController;
  final TextEditingController firstNameController;
  final TextEditingController lastNameController;
  final Uint8List? profilePicture;

  const RegisterActions({
    // Remove Key? key parameter completely
    required this.formKey,
    required this.emailController,
    required this.passwordController,
    required this.confirmPasswordController,
    required this.firstNameController,
    required this.lastNameController,
    this.profilePicture,
  });

  // Password validation helper - ONLY FOR VISUAL FEEDBACK
  Map<String, bool> _validatePassword(String password) {
    return {
      'minLength': password.length >= 6,
      'hasUppercase': password.contains(RegExp(r'[A-Z]')),
      'hasNumber': password.contains(RegExp(r'[0-9]')),
      'hasSpecialChar': password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]')),
    };
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final registerState = ref.watch(authNotifierProvider);
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    // Listen to registration state changes - UNCHANGED
    ref.listen<AsyncValue<void>>(authNotifierProvider, (previous, state) {
      if (state is AsyncData) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(S.of(context).registrationSuccessful),
            backgroundColor: Colors.green,
          ),
        );

        final email = emailController.text.trim();
        if (email.isNotEmpty && previous is! AsyncData) {
          context.go('/confirm-email/$email'); // Changed from push to go
        }
      } else if (state is AsyncError) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(ErrorTranslator.translate(context, state.error)),
            backgroundColor: Colors.red,
          ),
        );
      }
    });

    return Column(
      children: [
        // PASSWORD REQUIREMENTS - VISUAL ONLY, DOESN'T AFFECT FORM VALIDATION
        ValueListenableBuilder<TextEditingValue>(
          valueListenable: passwordController,
          builder: (context, value, child) {
            // Only show when password field has content
            if (value.text.isEmpty) return const SizedBox.shrink();

            final requirements = _validatePassword(value.text);

            return Container(
              margin: const EdgeInsets.only(bottom: 16),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Password Requirements:', // You can translate this later
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: isSmallScreen ? 13.0 : 14.0,
                      color: Colors.grey.shade700,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildRequirementItem(
                    'At least 6 characters', // You can translate this later
                    requirements['minLength'] ?? false,
                    isSmallScreen,
                  ),
                  _buildRequirementItem(
                    'At least one uppercase letter (A-Z)', // You can translate this later
                    requirements['hasUppercase'] ?? false,
                    isSmallScreen,
                  ),
                  _buildRequirementItem(
                    'At least one number (0-9)', // You can translate this later
                    requirements['hasNumber'] ?? false,
                    isSmallScreen,
                  ),
                  _buildRequirementItem(
                    'At least one special character (!@#\$%^&*)', // You can translate this later
                    requirements['hasSpecialChar'] ?? false,
                    isSmallScreen,
                  ),
                ],
              ),
            );
          },
        ),

        // ORIGINAL CODE BELOW - COMPLETELY UNCHANGED
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          child: Text.rich(
            TextSpan(
              text: S.of(context).agreeToTerms,
              children: [
                TextSpan(
                  text: " ${S.of(context).registerTerms}",
                  style: const TextStyle(
                    color: Colors.green,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 10),
        SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton(
            onPressed: registerState is AsyncLoading
                ? null
                : () async {
                    // ORIGINAL VALIDATION LOGIC - COMPLETELY UNCHANGED
                    if (formKey.currentState!.validate()) {
                      final registerCommand = RegisterCommand(
                        email: emailController.text.trim(),
                        password: passwordController.text.trim(),
                        confirmPassword: confirmPasswordController.text.trim(),
                        firstName: firstNameController.text.trim(),
                        lastName: lastNameController.text.trim(),
                        profilePicture: profilePicture,
                      );

                      await ref
                          .read(authNotifierProvider.notifier)
                          .registerUser(registerCommand);
                    }
                  },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: registerState is AsyncLoading
                ? const CircularProgressIndicator(color: Colors.white)
                : Text(
                    S.of(context).registerButton,
                    style: const TextStyle(fontSize: 16, color: Colors.white),
                  ),
          ),
        ),
        const SizedBox(height: 20),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildSocialButton("assets/icons/google.png"),
            const SizedBox(width: 16),
            _buildSocialButton("assets/icons/facebook.png"),
          ],
        ),
        const SizedBox(height: 20),
        Wrap(
          alignment: WrapAlignment.center,
          crossAxisAlignment: WrapCrossAlignment.center,
          children: [
            Text(S.of(context).alreadyHaveAccount),
            TextButton(
              onPressed: () {
                context.pushReplacement('/login'); // Changed from push to go
              },
              child: Text(
                S.of(context).loginHere,
                style: const TextStyle(color: Colors.green),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Helper method for requirement items
  Widget _buildRequirementItem(String text, bool isValid, bool isSmallScreen) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(
            isValid ? Icons.check_circle : Icons.cancel,
            size: isSmallScreen ? 14.0 : 16.0,
            color: isValid ? Colors.green : Colors.red.shade400,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: isSmallScreen ? 11.0 : 12.0,
                color: isValid ? Colors.green : Colors.red.shade400,
                fontWeight: isValid ? FontWeight.w500 : FontWeight.normal,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // SOCIAL BUTTON MATCHING LOGIN SCREEN STYLE
  Widget _buildSocialButton(String assetPath) {
    return Builder(
      builder: (context) {
        final isDarkMode = Theme.of(context).brightness == Brightness.dark;

        return Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: isDarkMode ? Colors.grey.shade600 : Colors.black12,
            ),
            color: isDarkMode
                ? Colors.grey.shade800.withOpacity(0.5)
                : Colors.white.withOpacity(0.8),
          ),
          child: Image.asset(
            assetPath,
            height: 30,
            width: 30,
            errorBuilder: (context, error, stackTrace) {
              // Fallback icons with same styling as login
              if (assetPath.contains('google')) {
                return const Icon(
                  Icons.g_mobiledata,
                  size: 30,
                  color: Color(0xFF4285F4), // Google blue
                );
              } else if (assetPath.contains('facebook')) {
                return const Icon(
                  Icons.facebook,
                  size: 30,
                  color: Color(0xFF1877F2), // Facebook blue
                );
              }
              return Icon(
                Icons.image_not_supported,
                size: 30,
                color: isDarkMode ? Colors.grey.shade500 : Colors.grey.shade400,
              );
            },
          ),
        );
      },
    );
  }
}
