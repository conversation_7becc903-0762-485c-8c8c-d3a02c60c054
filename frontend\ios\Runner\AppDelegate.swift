import Flutter
import UIKit
import GoogleMaps
// ADD THESE IMPORTS ONLY
import firebase_core
import Firebase

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    // KEEP YOUR EXISTING GOOGLE MAPS LINE
    GMSServices.provideAPIKey("AIzaSyBfvpyBMYxnv7-ahS1cqXd3oAcSaSc7ioQ")
    
    // ADD FIREBASE INITIALIZATION (BEFORE GeneratedPluginRegistrant)
    FirebaseApp.configure()
    
    // ADD iOS NOTIFICATION PERMISSION REQUEST
    if #available(iOS 10.0, *) {
      UNUserNotificationCenter.current().delegate = self as UNUserNotificationCenterDelegate
      
      let authOptions: UNAuthorizationOptions = [.alert, .badge, .sound]
      UNUserNotificationCenter.current().requestAuthorization(
        options: authOptions,
        completionHandler: { granted, error in
          print("📱 iOS Notification permission granted: \(granted)")
        }
      )
    }
    
    // ADD REMOTE NOTIFICATION REGISTRATION
    application.registerForRemoteNotifications()
    
    // KEEP YOUR EXISTING LINES
    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
  
  // ADD THESE NEW METHODS (won't break existing functionality)
  override func application(_ application: UIApplication, 
                           didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
    print("📱 APNs token registered")
    Messaging.messaging().apnsToken = deviceToken
  }
  
  override func application(_ application: UIApplication, 
                           didFailToRegisterForRemoteNotificationsWithError error: Error) {
    print("❌ APNs registration failed: \(error)")
  }
}

// ADD THIS EXTENSION (completely separate from existing code)
@available(iOS 10, *)
extension AppDelegate: UNUserNotificationCenterDelegate {
  func userNotificationCenter(_ center: UNUserNotificationCenter,
                              willPresent notification: UNNotification,
                              withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
    if #available(iOS 14.0, *) {
      completionHandler([[.banner, .badge, .sound]])
    } else {
      completionHandler([[.alert, .badge, .sound]])
    }
  }
  
  func userNotificationCenter(_ center: UNUserNotificationCenter,
                              didReceive response: UNNotificationResponse,
                              withCompletionHandler completionHandler: @escaping () -> Void) {
    completionHandler()
  }
}