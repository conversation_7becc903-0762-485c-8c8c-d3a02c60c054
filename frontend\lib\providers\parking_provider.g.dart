// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'parking_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchAllParkingLotsHash() =>
    r'225b0d3b7163aacb6f933c9de21f5dcdee3ed350';

/// See also [fetchAllParkingLots].
@ProviderFor(fetchAllParkingLots)
final fetchAllParkingLotsProvider =
    AutoDisposeFutureProvider<List<ParkingModel>>.internal(
  fetchAllParkingLots,
  name: r'fetchAllParkingLotsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchAllParkingLotsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchAllParkingLotsRef
    = AutoDisposeFutureProviderRef<List<ParkingModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
