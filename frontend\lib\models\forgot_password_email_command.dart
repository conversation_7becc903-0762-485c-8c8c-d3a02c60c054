class ForgotPasswordCommand {
  final String email;
  final int timezoneOffset;
  final String languageCode;

  ForgotPasswordCommand({
    required this.email,
    required this.timezoneOffset,
    required this.languageCode,
  });

  // Factory constructor to create a ForgotPasswordCommand from a JSON map
  factory ForgotPasswordCommand.fromJson(Map<String, dynamic> json) {
    return ForgotPasswordCommand(
      email: json['email'],
      timezoneOffset: json['timezoneOffset'],
      languageCode: json['languageCode'],
    );
  }

  // Method to convert a ForgotPasswordCommand to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'timezoneOffset': timezoneOffset,
      'languageCode': languageCode,
    };
  }

  @override
  String toString() {
    return 'ForgotPasswordCommand(email: $email, timezoneOffset: $timezoneOffset, languageCode: $languageCode)';
  }
}
