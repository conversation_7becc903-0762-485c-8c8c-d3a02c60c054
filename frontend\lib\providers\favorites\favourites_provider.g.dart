// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'favourites_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$favouritesItemsHash() => r'c285d89f8271f49887dbbec43098d76107a7d314';

/// See also [FavouritesItems].
@ProviderFor(FavouritesItems)
final favouritesItemsProvider =
    AutoDisposeAsyncNotifierProvider<FavouritesItems, Set<String>>.internal(
  FavouritesItems.new,
  name: r'favouritesItemsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$favouritesItemsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FavouritesItems = AutoDisposeAsyncNotifier<Set<String>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
