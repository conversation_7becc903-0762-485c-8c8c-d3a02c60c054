import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../l10n/generated/l10n.dart';

class RegisterForm extends StatelessWidget {
  final GlobalKey<FormState> formKey;
  final TextEditingController emailController;
  final TextEditingController passwordController;
  final TextEditingController confirmPasswordController;
  final TextEditingController firstNameController;
  final TextEditingController lastNameController;

  const RegisterForm({
    super.key,
    required this.formKey,
    required this.emailController,
    required this.passwordController,
    required this.confirmPasswordController,
    required this.firstNameController,
    required this.lastNameController,
  });

  @override
  Widget build(BuildContext context) {
    return Form(
      key: formKey, // ✅ Attach form key
      child: Column(
        children: [
          // ✅ First Name Field
          _buildTextField(
            context,
            controller: firstNameController,
            label: S.of(context).firstName,
            icon: Icons.person,
            textCapitalization: TextCapitalization.words,
            validator: (value) =>
                value!.isEmpty ? S.of(context).firstNameRequired : null,
          ),
          const SizedBox(height: 16),

          // ✅ Last Name Field
          _buildTextField(
            context,
            controller: lastNameController,
            label: S.of(context).lastName,
            icon: Icons.person_outline,
            textCapitalization: TextCapitalization.words,
            validator: (value) =>
                value!.isEmpty ? S.of(context).lastNameRequired : null,
          ),
          const SizedBox(height: 16),

          // ✅ Email Field
          _buildTextField(
            context,
            controller: emailController,
            label: S.of(context).email,
            icon: Icons.email,
            keyboardType: TextInputType.emailAddress,
            textCapitalization: TextCapitalization.none,
            autocorrect: false,
            enableSuggestions: false,
            validator: (value) {
              if (value!.isEmpty) {
                return S.of(context).emailRequired;
              }
              final emailRegex =
                  RegExp(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$");
              if (!emailRegex.hasMatch(value)) {
                return S.of(context).invalidEmail;
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // ✅ Password Field - Enhanced validation
          _buildTextField(
            context,
            controller: passwordController,
            label: S.of(context).password,
            icon: Icons.lock,
            isPassword: true,
            textCapitalization: TextCapitalization.none,
            autocorrect: false,
            enableSuggestions: false,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return S.of(context).passwordRequired;
              }

              final password = value.trim();

              // Check minimum length
              if (password.length < 6) {
                return S.of(context).passwordTooShort;
              }

              // Check for uppercase letter
              if (!password.contains(RegExp(r'[A-Z]'))) {
                return S.of(context).passwordMustContainUppercase;
              }

              // Check for number
              if (!password.contains(RegExp(r'[0-9]'))) {
                return S.of(context).passwordMustContainNumber;
              }

              // Check for special character
              if (!password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
                return S.of(context).passwordMustContainSpecialChar;
              }

              return null;
            },
          ),
          const SizedBox(height: 16),

          // ✅ Confirm Password Field - FIXED VALIDATION
          _buildTextField(
            context,
            controller: confirmPasswordController,
            label: S.of(context).confirmPassword,
            icon: Icons.lock_outline,
            isPassword: true,
            textCapitalization: TextCapitalization.none,
            autocorrect: false,
            enableSuggestions: false,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return S.of(context).confirmPasswordRequired;
              }

              // 🔧 THE FIX: Use .trim() on both values for comparison
              if (value.trim() != passwordController.text.trim()) {
                return S.of(context).passwordsDoNotMatch;
              }

              return null;
            },
          ),
        ],
      ),
    );
  }

  /// **iOS-Optimized Input Field Widget**
  Widget _buildTextField(
    BuildContext context, {
    required TextEditingController controller,
    required String label,
    required IconData icon,
    bool isPassword = false,
    TextInputType keyboardType = TextInputType.text,
    TextCapitalization textCapitalization = TextCapitalization.none,
    bool autocorrect = true,
    bool enableSuggestions = true,
    String? Function(String?)? validator,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return TextFormField(
      controller: controller,
      obscureText: isPassword,
      keyboardType: keyboardType,
      textCapitalization: textCapitalization,
      autocorrect: autocorrect,
      enableSuggestions: enableSuggestions,
      validator: validator,

      // 🔧 iOS-specific fixes
      enableInteractiveSelection: true,
      toolbarOptions: const ToolbarOptions(
        copy: true,
        cut: true,
        paste: true,
        selectAll: true,
      ),

      // 🔧 Prevent iOS keyboard dismissal issues
      onTapOutside: (event) {
        // Don't dismiss keyboard when tapping outside
        FocusScope.of(context).unfocus();
      },

      style: TextStyle(
        color: isDarkMode ? Colors.white : Colors.black87,
        fontSize: 16,
        // 🔧 iOS font weight fix
        fontWeight: FontWeight.w400,
      ),

      decoration: InputDecoration(
        labelText: label,
        labelStyle: TextStyle(
          color: isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
          fontSize: 16,
        ),
        floatingLabelStyle: TextStyle(
          color: Colors.green,
          fontSize: 16,
        ),
        prefixIcon: Icon(
          icon,
          color: isDarkMode ? Colors.grey.shade300 : Colors.black54,
        ),
        filled: true,
        fillColor: isDarkMode
            ? Colors.grey.shade800.withOpacity(0.8)
            : Colors.white.withOpacity(0.9),

        // 🔧 iOS-optimized content padding
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16, // Increased from 14 to 16 for better iOS touch targets
        ),

        // 🔧 Consistent border styling to prevent iOS rendering issues
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: isDarkMode ? Colors.grey.shade600 : Colors.grey.shade300,
            width: 1.0, // Explicit width
          ),
        ),

        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: isDarkMode ? Colors.grey.shade600 : Colors.grey.shade300,
            width: 1.0,
          ),
        ),

        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: Colors.green,
            width: 2.0,
          ),
        ),

        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: Colors.red,
            width: 1.0,
          ),
        ),

        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: Colors.red,
            width: 2.0,
          ),
        ),

        errorStyle: const TextStyle(
          color: Colors.red,
          fontSize: 12,
        ),

        // 🔧 iOS keyboard appearance
        counterStyle: const TextStyle(height: double.minPositive),
        errorMaxLines: 2,
      ),
    );
  }
}
