import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:frontend/infrastructure/uuid-helper/device_id_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:frontend/providers/notifications_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/router.dart';
import 'package:frontend/presentation/screens/repairs/repair_details.dart';

class RegisterDeviceService {
  static final Dio _dio = Dio(BaseOptions(
    baseUrl: dotenv.env['BASE_URL']!,
  ));

  /// Register device for push notifications
  static Future<void> registerDevice() async {
    try {
      // Check if this is the first launch
      final prefs = await SharedPreferences.getInstance();
      final isFirstLaunch = !prefs.containsKey('notification_permissions_set');

      if (isFirstLaunch) {
        print("📱 First launch - setting notification permissions...");
        // Request permissions for BOTH iOS and Android
        final permissionGranted = await _requestNotificationPermissions();

        // Use provider method to set notification preferences and subscribe to topics
        final container = ProviderContainer();
        await container
            .read(notificationNotifierProvider.notifier)
            .handleInitialPermission(permissionGranted);
        container.dispose();

        // Mark that we've set permissions
        await prefs.setBool('notification_permissions_set', true);
      } else {
        print(
            "📱 Not first launch - syncing Firebase topics with saved preferences");
        // Sync Firebase topics with saved preferences on subsequent launches
        await _syncFirebaseTopics();
      }

      final deviceId = await DeviceIdService.getOrCreateDeviceId();
      final deviceToken = await FirebaseMessaging.instance.getToken();
      final platform = Platform.isAndroid ? 'android' : 'ios';

      if (deviceToken == null) {
        print("⚠️ No FCM token found, skipping device registration.");
        return;
      }

      final payload = {
        "clientId": deviceId,
        "deviceToken": deviceToken,
        "platform": platform,
      };

      print("📦 Registering device with payload: $payload");
      print("🔑 Full FCM Token: $deviceToken");

      const endpoint = 'UserDevices';
      final response = await _dio.post(endpoint, data: payload);
      print("✅ Device registered: ${response.statusCode}");
    } catch (e) {
      print("⚠️ Error in registerDevice: $e");
    }
  }

  /// Sync Firebase topics with saved preferences (for subsequent app launches)
  static Future<void> _syncFirebaseTopics() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final newsEnabled = prefs.getBool('notification_news') ?? false;
      final eventsEnabled = prefs.getBool('notification_events') ?? false;
      final repairsEnabled = prefs.getBool('notification_repairs') ?? false;
      final announcementsEnabled =
          prefs.getBool('notification_announcements') ?? false;

      // Sync news topic
      if (newsEnabled) {
        await FirebaseMessaging.instance.subscribeToTopic('news');
        print('✅ Synced: Subscribed to news notifications');
      } else {
        await FirebaseMessaging.instance.unsubscribeFromTopic('news');
        print('✅ Synced: Unsubscribed from news notifications');
      }

      // Sync events topic
      if (eventsEnabled) {
        await FirebaseMessaging.instance.subscribeToTopic('events');
        print('✅ Synced: Subscribed to events notifications');
      } else {
        await FirebaseMessaging.instance.unsubscribeFromTopic('events');
        print('✅ Synced: Unsubscribed from events notifications');
      }

      // Sync repairs topic
      if (repairsEnabled) {
        await FirebaseMessaging.instance.subscribeToTopic('repairs');
        print('✅ Synced: Subscribed to repairs notifications');
      } else {
        await FirebaseMessaging.instance.unsubscribeFromTopic('repairs');
        print('✅ Synced: Unsubscribed from repairs notifications');
      }

      // Sync announcements topic
      if (announcementsEnabled) {
        await FirebaseMessaging.instance.subscribeToTopic('announcements');
        print('✅ Synced: Subscribed to announcements notifications');
      } else {
        await FirebaseMessaging.instance.unsubscribeFromTopic('announcements');
        print('✅ Synced: Unsubscribed from announcements notifications');
      }

      print('✅ Firebase topics synced with saved preferences');
    } catch (e) {
      print('❌ Error syncing Firebase topics: $e');
    }
  }

  /// Request notification permissions for both platforms
  static Future<bool> _requestNotificationPermissions() async {
    final messaging = FirebaseMessaging.instance;

    if (Platform.isIOS) {
      print("🔔 Requesting iOS notification permissions...");
      final settings = await messaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
      );
      print('📱 iOS permission status: ${settings.authorizationStatus}');
      return settings.authorizationStatus == AuthorizationStatus.authorized ||
          settings.authorizationStatus == AuthorizationStatus.provisional;
    } else if (Platform.isAndroid) {
      print("🔔 Requesting Android notification permissions...");
      final settings = await messaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
      );
      print('📱 Android permission status: ${settings.authorizationStatus}');
      return settings.authorizationStatus == AuthorizationStatus.authorized ||
          settings.authorizationStatus == AuthorizationStatus.provisional;
    }

    return false;
  }

  /// Setup notification listeners for when user taps notifications
  static void setupNotificationHandlers() {
    print("🔔 Setting up notification handlers...");

    // Handle notification tap when app is in background or terminated
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      print(
          '📱 User tapped notification (background): ${message.notification?.title}');
      print('📱 Notification data: ${message.data}');
      _handleNotificationTap(message);
    });

    // Handle notification when app is in foreground
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      print(
          '📱 Received notification (foreground): ${message.notification?.title}');
      print('📱 Notification body: ${message.notification?.body}');
      print('📱 Notification data: ${message.data}');
    });

    print("✅ Notification handlers setup complete");
  }

  /// Handle notification tap and navigate to appropriate screen
  static void _handleNotificationTap(RemoteMessage message) {
    final data = message.data;
    print('🔄 Handling notification tap with data: $data');

    if (data.containsKey('type')) {
      final type = data['type'];
      print('📱 Notification type: $type');

      switch (type) {
        case 'events':
          if (data.containsKey('eventId')) {
            final eventId = data['eventId'];
            print('📱 Navigate to event details: $eventId');
            _navigateToRoute('/events/event-details/$eventId');
          } else {
            print('📱 Navigate to events list');
            _navigateToRoute('/events');
          }
          break;

        case 'repairs':
          if (data.containsKey('repairId')) {
            final repairId = data['repairId'];
            print('📱 Navigate to repairs with modal for repair: $repairId');
            _navigateToRepairsWithModal(repairId);
          } else {
            print('📱 Navigate to repairs list');
            _navigateToRoute('/repairs');
          }
          break;

        case 'news':
          print('📱 Navigate to news screen');
          _navigateToRoute('/news');
          break;

        case 'announcements':
          print('📱 Navigate to messages screen');
          _navigateToRoute('/messages');
          break;

        case 'home':
        default:
          print('📱 Navigate to home screen');
          _navigateToRoute('/home');
          break;
      }
    } else {
      print('📱 No notification type specified, going to home');
      _navigateToRoute('/home');
    }
  }

  static void _navigateToRepairsWithModal(String repairId) {
    try {
      print(
          '📱 Setting up navigation to repairs with modal for repair: $repairId');

      // First navigate to home, then to repairs, then open the modal
      router.go('/home');

      Future.delayed(const Duration(milliseconds: 100), () {
        router.push('/repairs');

        // Wait a bit more for the repairs screen to load, then open the modal
        Future.delayed(const Duration(milliseconds: 300), () {
          final context = router.routerDelegate.navigatorKey.currentContext;
          if (context != null) {
            final repairIdInt = int.tryParse(repairId);
            if (repairIdInt != null) {
              // Import your repair details modal function
              showRepairDetailsModal(context, repairIdInt);
              print('✅ Successfully opened repair modal for ID: $repairIdInt');
            } else {
              print('❌ Invalid repair ID: $repairId');
            }
          } else {
            print('❌ No context available for modal');
          }
        });
      });
    } catch (e) {
      print('❌ Error navigating to repairs with modal: $e');
      // Fallback to just opening repairs screen
      _navigateToRoute('/repairs');
    }
  }

  /// Navigate to a specific route
  static void _navigateToRoute(String route) {
    try {
      if (route.startsWith('/events/event-details/')) {
        print('📱 Setting up proper navigation stack for event details');
        router.go('/home');
        Future.delayed(const Duration(milliseconds: 100), () {
          router.push('/events');
          Future.delayed(const Duration(milliseconds: 100), () {
            router.push(route);
            print('✅ Successfully navigated to: $route (with proper stack)');
          });
        });
      } else if (route == '/home') {
        router.go('/home');
        print('✅ Successfully navigated to home (reset stack)');
      } else {
        router.go('/home');
        Future.delayed(const Duration(milliseconds: 100), () {
          router.push(route);
          print('✅ Successfully navigated to: $route (with home in stack)');
        });
      }
    } catch (e) {
      print('❌ Navigation error: $e');
      Future.delayed(const Duration(milliseconds: 1000), () {
        try {
          router.push(route);
          print('✅ Successfully navigated to: $route (fallback)');
        } catch (retryError) {
          print('❌ Retry navigation error: $retryError');
          try {
            router.go('/home');
            print('✅ Fallback: navigated to home');
          } catch (homeError) {
            print('❌ Even home navigation failed: $homeError');
          }
        }
      });
    }
  }

  /// Handle initial notification (when app is launched from notification)
  static Future<void> handleInitialNotification() async {
    print("🔍 Checking for initial notification...");

    try {
      RemoteMessage? initialMessage =
          await FirebaseMessaging.instance.getInitialMessage();

      if (initialMessage != null) {
        print(
            '📱 App launched from notification: ${initialMessage.notification?.title}');
        print('📱 Initial message data: ${initialMessage.data}');

        Future.delayed(const Duration(milliseconds: 500), () {
          _handleNotificationTap(initialMessage);
        });
      } else {
        print('📱 No initial notification found');
      }
    } catch (e) {
      print('❌ Error checking initial notification: $e');
    }
  }

  /// Get current FCM token (useful for debugging)
  static Future<String?> getCurrentToken() async {
    try {
      final token = await FirebaseMessaging.instance.getToken();
      print('🔑 Current FCM Token: ${token?.substring(0, 20)}...');
      return token;
    } catch (e) {
      print('❌ Error getting FCM token: $e');
      return null;
    }
  }

  /// Subscribe to a topic (useful for broadcast notifications)
  static Future<void> subscribeToTopic(String topic) async {
    try {
      await FirebaseMessaging.instance.subscribeToTopic(topic);
      print('✅ Subscribed to topic: $topic');
    } catch (e) {
      print('❌ Error subscribing to topic $topic: $e');
    }
  }

  /// Unsubscribe from a topic
  static Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await FirebaseMessaging.instance.unsubscribeFromTopic(topic);
      print('✅ Unsubscribed from topic: $topic');
    } catch (e) {
      print('❌ Error unsubscribing from topic $topic: $e');
    }
  }
}
