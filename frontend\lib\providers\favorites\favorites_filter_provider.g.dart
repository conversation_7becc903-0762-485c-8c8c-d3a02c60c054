// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'favorites_filter_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$favouriteStateHash() => r'83501eb9a3d9f548f6fba561737a54da08a2693c';

/// See also [FavouriteState].
@ProviderFor(FavouriteState)
final favouriteStateProvider =
    AutoDisposeNotifierProvider<FavouriteState, FavoriteFilterType>.internal(
  FavouriteState.new,
  name: r'favouriteStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$favouriteStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FavouriteState = AutoDisposeNotifier<FavoriteFilterType>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
