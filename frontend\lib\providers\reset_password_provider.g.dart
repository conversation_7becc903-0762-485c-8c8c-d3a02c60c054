// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reset_password_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$resetPasswordStateHash() =>
    r'8960dd70569ea8c77af1fc8adde58201350095df';

/// See also [ResetPasswordState].
@ProviderFor(ResetPasswordState)
final resetPasswordStateProvider =
    AutoDisposeNotifierProvider<ResetPasswordState, AsyncValue<bool>>.internal(
  ResetPasswordState.new,
  name: r'resetPasswordStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$resetPasswordStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ResetPasswordState = AutoDisposeNotifier<AsyncValue<bool>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
