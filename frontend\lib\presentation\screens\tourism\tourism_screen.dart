import 'package:flutter/material.dart';
import 'package:frontend/domain/enums/favorite_filter_type.dart';
import 'package:frontend/l10n/generated/l10n.dart';
import 'package:frontend/models/favourites_base_menu_item_command.dart';
import 'package:frontend/providers/favorites/favourites_provider.dart';
import 'package:frontend/providers/theme_provider.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class TourismMenuItem implements BaseMenuItem {
  @override
  final String id;
  @override
  final String Function(BuildContext context) label;
  @override
  final String? route;
  @override
  final FavoriteFilterType type;

  const TourismMenuItem({
    required this.id,
    required this.label,
    this.route,
    this.type = FavoriteFilterType.tourism,
  });
}

class TourismScreen extends ConsumerWidget {
  const TourismScreen({Key? key}) : super(key: key);

  static final List<TourismMenuItem> tourismMenuItems = [
    TourismMenuItem(
      id: 'tourism_screen',
      label: (context) => S.of(context).tourismScreenTitle,
      route: '/tourism',
    ),
    TourismMenuItem(
      id: 'map',
      label: (context) => S.of(context).map,
      route: '/tourism/tourism-map',
    ),
    TourismMenuItem(
      id: 'legends-and-myths',
      label: (context) => S.of(context).tourismLegendsAndMyths,
      route: '/tourism/legends-and-myths',
    ),
    TourismMenuItem(
      id: 'sights',
      label: (context) => S.of(context).tourismSights,
      route: '/tourism/landmarks',
    ),
    TourismMenuItem(
      id: 'cultural-and-artistic-sites',
      label: (context) => S.of(context).tourismCulturalAndArtisticSites,
      route: '/tourism/cultural-and-artistic-sites',
    ),
    TourismMenuItem(
      id: 'routes-and-activities',
      label: (context) => S.of(context).tourismRoutesAndActivities,
      route: '/tourism/routes-and-activities',
    ),
    TourismMenuItem(
      id: 'family-entertainment',
      label: (context) => S.of(context).tourismFamilyEntertainment,
      route: '/tourism/family-entertainment',
    ),
    TourismMenuItem(
      id: 'night-life',
      label: (context) => S.of(context).tourismNightlife,
      route: '/tourism/night-life',
    ),
    TourismMenuItem(
      id: 'transport',
      label: (context) => S.of(context).tourismTransport,
      route: '/tourism/transport',
    ),
    TourismMenuItem(
      id: 'travel-agencies',
      label: (context) => S.of(context).tourismTravelAgencies,
      route: '/tourism/travel-agencies',
    ),
  ];

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeMode = ref.watch(themeNotifierProvider);
    final isDarkMode = themeMode == ThemeMode.dark;

    return Scaffold(
      backgroundColor: isDarkMode ? Colors.black : Colors.white,
      body: Padding(
        padding: const EdgeInsets.all(0),
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Stack(
                  children: [
                    Container(
                      width: MediaQuery.of(context).size.width,
                      height: 200,
                      decoration: BoxDecoration(
                        image: DecorationImage(
                          image: const AssetImage('assets/images/tourism.png'),
                          fit: BoxFit.cover,
                          colorFilter: ColorFilter.mode(
                            isDarkMode
                                ? Colors.white.withOpacity(0.3)
                                : const Color(0x0F22D400),
                            BlendMode.darken,
                          ),
                        ),
                      ),
                    ),
                    Positioned.fill(
                      child: Align(
                        alignment: Alignment.center,
                        child: Padding(
                          padding: const EdgeInsets.only(top: 0),
                          child: Text(
                            S.of(context).tourismWelcomeMessage,
                            textAlign: TextAlign.center,
                            style: GoogleFonts.roboto(
                              fontWeight: FontWeight.w700,
                              fontSize: 17,
                              height: 15 / 14,
                              letterSpacing: 0.0,
                              color: isDarkMode
                                  ? Colors.white
                                  : const Color(0xFF626262),
                            ),
                          ),
                        ),
                      ),
                    ),
                    // Top right global screen favourite star
                    Positioned(
                      top: 16,
                      right: 16,
                      child: Consumer(
                        builder: (context, ref, _) {
                          final favAsync = ref.watch(favouritesItemsProvider);

                          return favAsync.when(
                            data: (favs) {
                              final isFav = favs.contains("tourism_screen");

                              return GestureDetector(
                                onTap: () {
                                  ref
                                      .read(favouritesItemsProvider.notifier)
                                      .toggle("tourism_screen");
                                },
                                child: Icon(
                                  isFav ? Icons.star : Icons.star_border,
                                  color: isFav
                                      ? Colors.green
                                      : (isDarkMode
                                          ? Colors.white
                                          : const Color(0xFF626262)),
                                  size: 30,
                                ),
                              );
                            },
                            loading: () =>
                                const Icon(Icons.star_border, size: 30),
                            error: (err, _) =>
                                const Icon(Icons.error, size: 30),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
            Positioned(
              top: 165,
              left: 0,
              right: 13,
              bottom: 0,
              child: ListView.builder(
                itemCount: tourismMenuItems.length -
                    1, //made so tourism does not appear as submenu
                itemBuilder: (context, index) {
                  final item = tourismMenuItems[
                      index + 1]; //made so tourism does not appear as submenu
                  final id = item.id;
                  final label = item.label(context);

                  return GestureDetector(
                    onTap: () {
                      if (item.route != null) {
                        context.push(item.route!);
                      }
                    },
                    child: Container(
                      margin: const EdgeInsets.only(bottom: 16),
                      width: 400,
                      height: 76,
                      decoration: BoxDecoration(
                        color:
                            isDarkMode ? const Color(0xFF333333) : Colors.white,
                        borderRadius: const BorderRadius.only(
                          topRight: Radius.circular(10),
                          bottomRight: Radius.circular(10),
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: isDarkMode
                                ? Colors.black.withOpacity(0.2)
                                : Colors.black.withOpacity(0.1),
                            offset: const Offset(4, 4),
                            blurRadius: 6,
                          ),
                        ],
                      ),
                      child: ListTile(
                        leading: Padding(
                          padding: const EdgeInsets.only(top: 8.0, left: 3.5),
                          child: Consumer(
                            builder: (context, ref, _) {
                              final favAsync =
                                  ref.watch(favouritesItemsProvider);

                              return favAsync.when(
                                data: (favs) {
                                  final isFav = favs.contains(id);
                                  return GestureDetector(
                                    onTap: () {
                                      ref
                                          .read(
                                              favouritesItemsProvider.notifier)
                                          .toggle(id);
                                    },
                                    child: Icon(
                                      isFav ? Icons.star : Icons.star_border,
                                      color: isFav
                                          ? Colors.green
                                          : (isDarkMode
                                              ? Colors.white
                                              : const Color(0xFFBABBBA)),
                                    ),
                                  );
                                },
                                loading: () => const Icon(Icons.star_border),
                                error: (_, __) => const Icon(Icons.error),
                              );
                            },
                          ),
                        ),
                        title: Padding(
                          padding: const EdgeInsets.only(top: 14.0),
                          child: Text(
                            label,
                            style: GoogleFonts.roboto(
                              fontWeight: FontWeight.w400,
                              fontSize: 17,
                              height: 15 / 14,
                              letterSpacing: 0.0,
                              color: isDarkMode
                                  ? Colors.white
                                  : const Color(0xFF626262),
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
