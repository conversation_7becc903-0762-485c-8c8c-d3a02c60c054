import 'package:flutter/material.dart';
import 'package:frontend/domain/enums/favorite_filter_type.dart';
import 'package:frontend/l10n/generated/l10n.dart';
import 'package:frontend/models/favourites_base_menu_item_command.dart';
import 'package:frontend/providers/favorites/favourites_provider.dart';
import 'package:frontend/providers/theme_provider.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class GeneralInformationMenuItem implements BaseMenuItem {
  @override
  final String id;
  @override
  final String Function(BuildContext context) label;
  @override
  final String? route;
  @override
  final FavoriteFilterType type;

  const GeneralInformationMenuItem({
    required this.id,
    required this.label,
    this.route,
    this.type = FavoriteFilterType.generalInfo,
  });
}

class GeneralInformationScreen extends ConsumerWidget {
  const GeneralInformationScreen({super.key});

  static final List<GeneralInformationMenuItem> generalInformationMenuItems = [
    GeneralInformationMenuItem(
      id: 'general_info_screen', // FIXED: Changed from 'screen' to unique ID
      label: (context) => S
          .of(context)
          .generalInformationFull, // FIXED: Changed to proper screen title instead of welcome message
      route: '/general',
    ),
    GeneralInformationMenuItem(
      id: 'establishments',
      label: (context) => S.of(context).establishments,
      route: '/general/establishments',
    ),
    GeneralInformationMenuItem(
      id: 'gas-stations',
      label: (context) => S.of(context).gasStations,
      route: '/general/gas-stations',
    ),
    GeneralInformationMenuItem(
      id: 'shop',
      label: (context) => S.of(context).shop,
      route: '/general/shops',
    ),
    GeneralInformationMenuItem(
      id: 'accommodation',
      label: (context) => S.of(context).accommodation,
      route: '/general/accommodations',
    ),
    GeneralInformationMenuItem(
      id: 'finance',
      label: (context) => S.of(context).finance,
      route: '/general/finance',
    ),
    GeneralInformationMenuItem(
      id: 'ecology',
      label: (context) => S.of(context).ecology,
      route: '/general/ecology',
    ),
    GeneralInformationMenuItem(
      id: 'culture',
      label: (context) => S.of(context).culture,
      route: '/general/culture',
    ),
    GeneralInformationMenuItem(
      id: 'education',
      label: (context) => S.of(context).education,
      route: '/general/education',
    ),
    GeneralInformationMenuItem(
      id: 'health',
      label: (context) => S.of(context).health,
      route: '/general/health',
    ),
    GeneralInformationMenuItem(
      id: 'sport',
      label: (context) => S.of(context).sport,
      route: '/general/sport',
    ),
  ];

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeMode = ref.watch(themeNotifierProvider);
    final isDarkMode = themeMode == ThemeMode.dark;

    return Scaffold(
      backgroundColor: isDarkMode ? Colors.black : Colors.white,
      body: Padding(
        padding: const EdgeInsets.all(0),
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Stack(
                  children: [
                    Container(
                      width: MediaQuery.of(context).size.width,
                      height: 200,
                      decoration: BoxDecoration(
                        image: DecorationImage(
                          image: const AssetImage(
                              'assets/images/general-info.png'),
                          fit: BoxFit.cover,
                          colorFilter: ColorFilter.mode(
                            isDarkMode
                                ? Colors.white.withValues(alpha: 0.3)
                                : const Color(0x0F22D400),
                            BlendMode.darken,
                          ),
                        ),
                      ),
                    ),
                    Positioned.fill(
                      child: Align(
                        alignment: Alignment.center,
                        child: Padding(
                          padding: const EdgeInsets.only(top: 0),
                          child: Text(
                            S.of(context).generalInfoWelcomeMessage,
                            textAlign: TextAlign.center,
                            style: GoogleFonts.roboto(
                              fontWeight: FontWeight.w700,
                              fontSize: 17,
                              height: 15 / 14,
                              letterSpacing: 0.0,
                              color: isDarkMode
                                  ? Colors.white
                                  : const Color(0xFF626262),
                            ),
                          ),
                        ),
                      ),
                    ),
                    // Top right global screen favourite star
                    Positioned(
                      top: 16,
                      right: 16,
                      child: Consumer(
                        builder: (context, ref, _) {
                          final favAsync = ref.watch(favouritesItemsProvider);

                          return favAsync.when(
                            data: (favs) {
                              final isFav =
                                  favs.contains("general_info_screen");

                              return GestureDetector(
                                onTap: () {
                                  ref
                                      .read(favouritesItemsProvider.notifier)
                                      .toggle("general_info_screen");
                                },
                                child: Icon(
                                  isFav ? Icons.star : Icons.star_border,
                                  color: isFav
                                      ? Colors.green
                                      : (isDarkMode
                                          ? Colors.white
                                          : const Color(0xFF626262)),
                                  size: 30,
                                ),
                              );
                            },
                            loading: () =>
                                const Icon(Icons.star_border, size: 30),
                            error: (err, _) =>
                                const Icon(Icons.error, size: 30),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
            Positioned(
              top: 165,
              left: 0,
              right: 13,
              bottom: 0,
              child: ListView.builder(
                itemCount: generalInformationMenuItems.length -
                    1, // Exclude the first 'screen' item from the list
                itemBuilder: (context, index) {
                  final item = generalInformationMenuItems[
                      index + 1]; // Skip the first 'screen' item
                  final id = item.id;
                  final label = item.label(context);

                  return GestureDetector(
                    onTap: () {
                      if (item.route != null) {
                        context.push(item.route!);
                      }
                    },
                    child: Container(
                      margin: const EdgeInsets.only(bottom: 16),
                      width: 400,
                      height: 76,
                      decoration: BoxDecoration(
                        color:
                            isDarkMode ? const Color(0xFF333333) : Colors.white,
                        borderRadius: const BorderRadius.only(
                          topRight: Radius.circular(10),
                          bottomRight: Radius.circular(10),
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: isDarkMode
                                ? Colors.black.withValues(alpha: 0.2)
                                : Colors.black.withValues(alpha: 0.1),
                            offset: const Offset(4, 4),
                            blurRadius: 6,
                          ),
                        ],
                      ),
                      child: ListTile(
                        leading: Padding(
                          padding: const EdgeInsets.only(top: 8.0, left: 3.5),
                          child: Consumer(
                            builder: (context, ref, _) {
                              final favAsync =
                                  ref.watch(favouritesItemsProvider);

                              return favAsync.when(
                                data: (favs) {
                                  final isFav = favs.contains(id);
                                  return GestureDetector(
                                    onTap: () {
                                      ref
                                          .read(
                                              favouritesItemsProvider.notifier)
                                          .toggle(id);
                                    },
                                    child: Icon(
                                      isFav ? Icons.star : Icons.star_border,
                                      color: isFav
                                          ? Colors.green
                                          : (isDarkMode
                                              ? Colors.white
                                              : const Color(0xFFBABBBA)),
                                    ),
                                  );
                                },
                                loading: () => const Icon(Icons.star_border),
                                error: (_, __) => const Icon(Icons.error),
                              );
                            },
                          ),
                        ),
                        title: Padding(
                          padding: const EdgeInsets.only(top: 14.0),
                          child: Text(
                            label,
                            style: GoogleFonts.roboto(
                              fontWeight: FontWeight.w400,
                              fontSize: 17,
                              height: 15 / 14,
                              letterSpacing: 0.0,
                              color: isDarkMode
                                  ? Colors.white
                                  : const Color(0xFF626262),
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
