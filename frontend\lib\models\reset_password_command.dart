class ResetPasswordCommand {
  final String newPassword;
  final String confirmPassword;
  final String signature;
  final String email;
  final String id;
  final int timezoneOffset;
  final String expires;

  ResetPasswordCommand({
    required this.newPassword,
    required this.confirmPassword,
    required this.signature,
    required this.email,
    required this.id,
    required this.timezoneOffset,
    required this.expires,
  });

  // Factory constructor to create a ResetPasswordCommand from a JSON map
  factory ResetPasswordCommand.fromJson(Map<String, dynamic> json) {
    return ResetPasswordCommand(
      newPassword: json['newPassword'],
      confirmPassword: json['confirmPassword'],
      signature: json['signature'],
      email: json['email'],
      id: json['id'],
      timezoneOffset: json['timezoneOffset'],
      expires: json['expires'],
    );
  }

  // Method to convert a ResetPasswordCommand to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'newPassword': newPassword,
      'confirmPassword': confirmPassword,
      'signature': signature,
      'email': email,
      'id': id,
      'timezoneOffset': timezoneOffset,
      'expires': expires,
    };
  }

  @override
  String toString() {
    return 'ResetPasswordCommand(newPassword: [HIDDEN], confirmPassword: [HIDDEN], signature: $signature, email: $email, id: $id, timezoneOffset: $timezoneOffset, expires: $expires)';
  }
}
