import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:frontend/models/transport_panel_command.dart';
import 'package:frontend/models/transport_stop_command.dart';

class TransportService {
  static const String _baseUrl = 'https://transport.yambol.bg/api';
  static const String _tokenEndpoint = '/.well-known/token';
  static const String _clientId =
      'W44UENQG3TTISFCH2LCSDZW5ZETHKIOVNSYVALLJ65424PPEML4HSIZTEP5FDX376EGNQFK3ALG72VPBKGZLTZ3U325E7MQ4QDGYCFLO';
  static const String _clientSecret =
      '43LLCM2ZW6JEAVEP7OHZNF7NLDFWORIAON2SN327U54PTWIWFVFX3ANKFDKCCSSH3WYLQQYLWOQMEGGDRFYLGHAJDUVE4BMRF6LF6SLP';

  static const FlutterSecureStorage _storage = FlutterSecureStorage();

  static final Dio _dio = Dio(BaseOptions(
    baseUrl: _baseUrl,
    connectTimeout: const Duration(seconds: 10),
    receiveTimeout: const Duration(seconds: 10),
  ));

  static final Dio _authDio = Dio(BaseOptions(
    connectTimeout: const Duration(seconds: 10),
    receiveTimeout: const Duration(seconds: 10),
  ));

  /// Get or request access token
  static Future<String?> _getAccessToken() async {
    try {
      // Check if we have a valid token stored
      final storedToken = await _storage.read(key: 'transport_access_token');
      final tokenExpiry = await _storage.read(key: 'transport_token_expiry');

      if (storedToken != null && tokenExpiry != null) {
        final expiryTime = DateTime.parse(tokenExpiry);
        final now = DateTime.now();

        // Check if token is still valid (with 200 seconds buffer before expiry)
        if (expiryTime.isAfter(now.add(const Duration(seconds: 200)))) {
          return storedToken;
        }
      }

      // Request new token
      print('🔐 Requesting new access token...');

      final response = await _authDio.post(
        '$_baseUrl$_tokenEndpoint',
        data: {
          'client_id': _clientId,
          'client_secret': _clientSecret,
          'grant_type': 'client_credentials',
          'scope': 'stop panel',
          'resource': 'https://transport.yambol.bg/api/public',
        },
        options: Options(
          contentType: Headers.formUrlEncodedContentType,
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        ),
      );

      if (response.statusCode == 200) {
        final accessToken = response.data['access_token'];

        // Store token and expiry time (using 3400 seconds as you mentioned)
        await _storage.write(key: 'transport_access_token', value: accessToken);
        await _storage.write(
          key: 'transport_token_expiry',
          value: DateTime.now().add(Duration(seconds: 3400)).toIso8601String(),
        );

        print('✅ Access token obtained successfully');
        return accessToken;
      } else {
        print('❌ Failed to get access token: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      print('❌ Error getting access token: $e');
      return null;
    }
  }

  /// Add authentication header to Dio instance
  static Future<void> _ensureAuthenticated() async {
    final token = await _getAccessToken();
    if (token != null) {
      _dio.options.headers['Authorization'] = 'Bearer $token';
    } else {
      print('⚠️ Warning: No access token available');
    }
  }

  /// Fetch all stops
  static Future<List<StopModel>> fetchAllStops() async {
    const String endpoint = '/public/stops';

    try {
      // Ensure we have a valid token
      await _ensureAuthenticated();

      print('TRANSPORT REQUEST: Fetching all stops');

      final response = await _dio.get(endpoint);

      if (response.statusCode == 200) {
        print('TRANSPORT RESPONSE: Success');

        List<dynamic> jsonData = response.data['data'];
        return jsonData.map((item) => StopModel.fromJson(item)).toList();
      } else {
        throw Exception(
            'Failed to load stops with status ${response.statusCode}');
      }
    } on DioException catch (e) {
      print("❌ Transport Service Dio Error: ${e.response?.statusCode}");
      print("📩 Response Data: ${e.response?.data}");
      print("📝 Error Message: ${e.message}");

      // If we get 401, try to refresh token and retry once
      if (e.response?.statusCode == 401) {
        print("🔄 Token might be expired, attempting to refresh...");
        await clearToken();
        await _ensureAuthenticated();

        try {
          final response = await _dio.get(endpoint);
          if (response.statusCode == 200) {
            List<dynamic> jsonData = response.data['data'];
            return jsonData.map((item) => StopModel.fromJson(item)).toList();
          }
        } catch (retryError) {
          print("❌ Retry failed: $retryError");
        }
      }

      return [];
    } catch (e) {
      print("⚠ Transport Service Unexpected Error: $e");
      return [];
    }
  }

  /// Fetch panels (departures) for specific posts
  static Future<List<PanelModel>> fetchPanels({
    required List<int> posts,
  }) async {
    // Format the posts array as a JSON string for the URL
    final String postsParam = '[${posts.join(',')}]';
    final String endpoint = '/public/panels?posts=$postsParam';

    try {
      await _ensureAuthenticated();

      print('TRANSPORT REQUEST: Fetching panels for posts: $posts');
      print('URL: $_baseUrl$endpoint');

      final response = await _dio.get(endpoint);

      if (response.statusCode == 200) {
        print('TRANSPORT RESPONSE: Success');

        List<dynamic> jsonData = response.data['data'];
        return jsonData.map((item) => PanelModel.fromJson(item)).toList();
      } else {
        throw Exception(
            'Failed to load panels with status ${response.statusCode}');
      }
    } on DioException catch (e) {
      print("❌ Transport Service Dio Error: ${e.response?.statusCode}");
      print("📩 Response Data: ${e.response?.data}");
      print("📝 Error Message: ${e.message}");

      if (e.response?.statusCode == 401) {
        print("🔄 Token might be expired, attempting to refresh...");
        await clearToken();
        await _ensureAuthenticated();

        try {
          final response = await _dio.get(endpoint);
          if (response.statusCode == 200) {
            List<dynamic> jsonData = response.data['data'];
            return jsonData.map((item) => PanelModel.fromJson(item)).toList();
          }
        } catch (retryError) {
          print("❌ Retry failed: $retryError");
        }
      }

      return [];
    } catch (e) {
      print("⚠ Transport Service Unexpected Error: $e");
      return [];
    }
  }

  /// Clear stored token (useful for logout or error recovery)
  static Future<void> clearToken() async {
    await _storage.delete(key: 'transport_access_token');
    await _storage.delete(key: 'transport_token_expiry');
    _dio.options.headers.remove('Authorization');
  }
}
