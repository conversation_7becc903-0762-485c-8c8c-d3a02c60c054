// Main Air Quality Configuration Model
import 'package:frontend/models/air_quality_sensor_config_command.dart';

class AirQualityConfig {
  final bool canAddRemoveSensors;
  final int iconId;
  final String iconName;
  final bool isReceiveDataBin;
  final bool isSendDataBin;
  final Map<String, PortConfig> ports;
  final TransmissionSettings transmissionSettings;

  AirQualityConfig({
    required this.canAddRemoveSensors,
    required this.iconId,
    required this.iconName,
    required this.isReceiveDataBin,
    required this.isSendDataBin,
    required this.ports,
    required this.transmissionSettings,
  });

  factory AirQualityConfig.fromJson(Map<String, dynamic> json) {
    Map<String, PortConfig> parsedPorts = {};
    if (json['ports'] != null) {
      (json['ports'] as Map<String, dynamic>).forEach((key, value) {
        parsedPorts[key] = PortConfig.fromJson(value);
      });
    }

    return AirQualityConfig(
      canAddRemoveSensors: json['canAddRemoveSensors'] ?? false,
      iconId: json['iconId'] ?? 0,
      iconName: json['iconName'] ?? '',
      isReceiveDataBin: json['isReceiveDataBin'] ?? false,
      isSendDataBin: json['isSendDataBin'] ?? false,
      ports: parsedPorts,
      transmissionSettings:
          TransmissionSettings.fromJson(json['transmissionSettings'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'canAddRemoveSensors': canAddRemoveSensors,
      'iconId': iconId,
      'iconName': iconName,
      'isReceiveDataBin': isReceiveDataBin,
      'isSendDataBin': isSendDataBin,
      'ports': ports.map((key, value) => MapEntry(key, value.toJson())),
      'transmissionSettings': transmissionSettings.toJson(),
    };
  }
}

// Port Configuration Model
class PortConfig {
  final String direction;
  final bool enabled;
  final SensorConfig sensor;

  PortConfig({
    required this.direction,
    required this.enabled,
    required this.sensor,
  });

  factory PortConfig.fromJson(Map<String, dynamic> json) {
    return PortConfig(
      direction: json['direction'] ?? '',
      enabled: json['enabled'] ?? false,
      sensor: SensorConfig.fromJson(json['sensor'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'direction': direction,
      'enabled': enabled,
      'sensor': sensor.toJson(),
    };
  }
}

// Transmission Settings Model
class TransmissionSettings {
  final int countsThreshold;
  final int every;
  final String recordPeriod;

  TransmissionSettings({
    required this.countsThreshold,
    required this.every,
    required this.recordPeriod,
  });

  factory TransmissionSettings.fromJson(Map<String, dynamic> json) {
    return TransmissionSettings(
      countsThreshold: json['countsThreshold'] ?? 0,
      every: json['every'] ?? 0,
      recordPeriod: json['recordPeriod'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'countsThreshold': countsThreshold,
      'every': every,
      'recordPeriod': recordPeriod,
    };
  }
}
