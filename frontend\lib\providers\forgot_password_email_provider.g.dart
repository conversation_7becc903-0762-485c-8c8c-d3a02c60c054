// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'forgot_password_email_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$forgotPasswordStateHash() =>
    r'2634da2d7edafec94bf008dbee0fbbaf9fb94b99';

/// See also [ForgotPasswordState].
@ProviderFor(ForgotPasswordState)
final forgotPasswordStateProvider =
    AutoDisposeNotifierProvider<ForgotPasswordState, AsyncValue<bool>>.internal(
  ForgotPasswordState.new,
  name: r'forgotPasswordStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$forgotPasswordStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ForgotPasswordState = AutoDisposeNotifier<AsyncValue<bool>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
