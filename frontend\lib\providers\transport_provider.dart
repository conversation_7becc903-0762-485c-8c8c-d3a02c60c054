import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/infrastructure/transport/transport_service.dart';
import 'package:frontend/models/transport_panel_command.dart';
import 'package:frontend/models/transport_stop_command.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'transport_provider.g.dart';

// Provider to fetch all stops
@riverpod
Future<List<StopModel>> fetchAllStops(Ref ref) async {
  print("Fetching all stops");
  return TransportService.fetchAllStops();
}

// Provider to fetch panels for specific posts
@riverpod
Future<List<PanelModel>> fetchPanels(
  Ref ref, {
  required List<int> posts,
}) async {
  print("Fetching panels for posts: $posts");
  return TransportService.fetchPanels(posts: posts);
}

// Provider for selected stop
@riverpod
class SelectedStop extends _$SelectedStop {
  @override
  StopModel? build() => null;

  void selectStop(StopModel? stop) {
    state = stop;
  }
}
