import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/l10n/generated/l10n.dart';
import 'package:frontend/models/air_quality_sensor_reading_command.dart';
import 'package:frontend/providers/air_quality_provider.dart';
import 'package:frontend/models/air_quality_zone_command.dart';
import 'package:google_fonts/google_fonts.dart';

class AirQualityDetailsModal extends ConsumerWidget {
  final String deviceId;

  const AirQualityDetailsModal({
    super.key,
    required this.deviceId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final zoneDetailsAsync =
        ref.watch(fetchZoneDetailsProvider(deviceNumber: deviceId));
    final readingsAsync =
        ref.watch(fetchCurrentReadingsProvider(deviceNumber: deviceId));

    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.all(20),
      child: Container(
        width: double.infinity,
        constraints: const BoxConstraints(maxWidth: 400, maxHeight: 600),
        decoration: BoxDecoration(
          color: isDarkMode ? Colors.grey.shade900 : Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildModalHeader(context, isDarkMode, zoneDetailsAsync),
            Flexible(
              child: _buildModalContent(
                  context, ref, zoneDetailsAsync, readingsAsync, isDarkMode),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModalHeader(
    BuildContext context,
    bool isDarkMode,
    AsyncValue<AirQualityZoneModel?> zoneDetailsAsync,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Color(0xFF22D400),
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: const BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.air,
              color: Color(0xFF22D400),
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  S.of(context).air_quality_zone,
                  style: GoogleFonts.roboto(
                    fontSize: 14,
                    color: Colors.white70,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                zoneDetailsAsync.when(
                  data: (zone) => Text(
                    zone?.name.isNotEmpty == true ? zone!.name : deviceId,
                    style: GoogleFonts.roboto(
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                      color: Colors.white,
                    ),
                  ),
                  loading: () => Text(
                    S.of(context).loading_details,
                    style: GoogleFonts.roboto(
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                      color: Colors.white,
                    ),
                  ),
                  error: (_, __) => Text(
                    deviceId,
                    style: GoogleFonts.roboto(
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
          _buildCloseButton(context),
        ],
      ),
    );
  }

  Widget _buildCloseButton(BuildContext context) {
    return GestureDetector(
      onTap: () => Navigator.pop(context),
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.2),
          shape: BoxShape.circle,
        ),
        child: const Icon(
          Icons.close,
          color: Colors.white,
          size: 18,
        ),
      ),
    );
  }

  Widget _buildModalContent(
    BuildContext context,
    WidgetRef ref,
    AsyncValue<AirQualityZoneModel?> zoneDetailsAsync,
    AsyncValue<List<SensorReadingModel>> readingsAsync,
    bool isDarkMode,
  ) {
    // Handle different loading states
    if (zoneDetailsAsync.isLoading && readingsAsync.isLoading) {
      return _buildLoadingContent(context, isDarkMode);
    }

    if (zoneDetailsAsync.hasError && readingsAsync.hasError) {
      return _buildErrorContent(
          context, ref, zoneDetailsAsync.error!, isDarkMode);
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Zone overview - use zone details if available
          zoneDetailsAsync.when(
            data: (zone) => zone != null
                ? _buildZoneOverview(context, zone, isDarkMode)
                : _buildBasicZoneHeader(context, isDarkMode),
            loading: () => _buildBasicZoneHeader(context, isDarkMode),
            error: (_, __) => _buildBasicZoneHeader(context, isDarkMode),
          ),
          const SizedBox(height: 16),
          // Sensor measurements - use real readings
          readingsAsync.when(
            data: (readings) =>
                _buildRealSensorMeasurements(context, readings, isDarkMode),
            loading: () => _buildLoadingMeasurements(context, isDarkMode),
            error: (error, stack) =>
                _buildErrorMeasurements(context, ref, error, isDarkMode),
          ),
        ],
      ),
    );
  }

  Widget _buildZoneOverview(
      BuildContext context, AirQualityZoneModel zone, bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF22D400).withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF22D400).withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          if (zone.name.isNotEmpty) ...[
            Text(
              zone.name,
              style: GoogleFonts.roboto(
                fontSize: 18,
                fontWeight: FontWeight.w700,
                color: isDarkMode ? Colors.white : Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
          ],
          const SizedBox(height: 8),
          Text(
            S.of(context).weather_station,
            style: GoogleFonts.roboto(
              fontSize: 14,
              color: isDarkMode ? Colors.grey.shade300 : Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBasicZoneHeader(BuildContext context, bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF22D400).withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF22D400).withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Text(
            deviceId,
            style: GoogleFonts.roboto(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: isDarkMode ? Colors.grey.shade300 : Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            S.of(context).weather_station,
            style: GoogleFonts.roboto(
              fontSize: 14,
              color: isDarkMode ? Colors.grey.shade300 : Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRealSensorMeasurements(BuildContext context,
      List<SensorReadingModel> readings, bool isDarkMode) {
    // Filter readings to only include the desired sensor types
    final filteredReadings = readings.where((reading) {
      final sensorType = reading.sensorType;
      return sensorType == 'humidity' ||
          sensorType == 'temperature' ||
          sensorType == 'pressure' ||
          sensorType == 'pm25' ||
          sensorType == 'pm10';
    }).toList();

    if (filteredReadings.isEmpty) {
      return _buildNoMeasurementsWidget(context, isDarkMode);
    }

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: isDarkMode
            ? Colors.grey.shade800.withOpacity(0.5)
            : Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDarkMode ? Colors.grey.shade600 : Colors.grey.shade300,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: filteredReadings
            .map((reading) =>
                _buildRealMeasurementRow(context, reading, isDarkMode))
            .toList(),
      ),
    );
  }

  Widget _buildRealMeasurementRow(
      BuildContext context, SensorReadingModel reading, bool isDarkMode) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Icon(
            _getIconForSensorType(reading.sensorType),
            color: _getColorForSensorType(reading.sensorType),
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getDisplayName(context, reading.sensorType, reading),
                  style: GoogleFonts.roboto(
                    fontSize: 14,
                    color: isDarkMode ? Colors.grey.shade300 : Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  reading.displayValue,
                  style: GoogleFonts.roboto(
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                    color: isDarkMode ? Colors.white : const Color(0xFF1A1A1A),
                  ),
                ),
              ],
            ),
          ),
          // Optional: Add status indicator based on valueRange
          if (reading.valueRange != null)
            _buildStatusIndicator(context, reading.valueRange!, isDarkMode),
        ],
      ),
    );
  }

  String _getDisplayName(
      BuildContext context, String sensorType, SensorReadingModel reading) {
    switch (sensorType) {
      case 'radiation':
        return S.of(context).sensor_radiation;
      case 'humidity':
        return S.of(context).sensor_humidity;
      case 'temperature':
        return S.of(context).sensor_temperature;
      case 'pressure':
        return S.of(context).sensor_pressure;
      case 'pm25':
        return S.of(context).sensor_pm25;
      case 'pm10':
        return S.of(context).sensor_pm10;
      case 'pm':
        return S.of(context).sensor_pm;
      default:
        return reading.rangeEventConfig?.name ??
            'Сензор ${reading.sensorIndex}';
    }
  }

  Widget _buildStatusIndicator(
      BuildContext context, String valueRange, bool isDarkMode) {
    Color statusColor;
    String statusText;

    if (valueRange.contains('normal')) {
      statusColor = Colors.green;
      statusText = S.of(context).status_normal;
    } else if (valueRange.contains('moderate')) {
      statusColor = Colors.orange;
      statusText = S.of(context).status_moderate;
    } else if (valueRange.contains('dangerous')) {
      statusColor = Colors.red;
      statusText = S.of(context).status_dangerous;
    } else {
      statusColor = Colors.grey;
      statusText = 'N/A';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        statusText,
        style: GoogleFonts.roboto(
          fontSize: 10,
          fontWeight: FontWeight.w600,
          color: statusColor,
        ),
      ),
    );
  }

  IconData _getIconForSensorType(String sensorType) {
    switch (sensorType) {
      case 'humidity':
        return Icons.water_drop;
      case 'temperature':
        return Icons.thermostat;
      case 'pressure':
        return Icons.speed;
      case 'pm25':
        return Icons.grain;
      case 'pm10':
        return Icons.blur_on;
      default:
        return Icons.sensors; // This shouldn't be reached due to filtering
    }
  }

  Color _getColorForSensorType(String sensorType) {
    switch (sensorType) {
      case 'humidity':
        return Colors.blue;
      case 'temperature':
        return Colors.orange;
      case 'pressure':
        return Colors.purple;
      case 'pm25':
        return Colors.red;
      case 'pm10':
        return Colors.deepOrange;
      default:
        return Colors.grey; // This shouldn't be reached due to filtering
    }
  }

  Widget _buildLoadingMeasurements(BuildContext context, bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: isDarkMode
            ? Colors.grey.shade800.withOpacity(0.5)
            : Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDarkMode ? Colors.grey.shade600 : Colors.grey.shade300,
          width: 1,
        ),
      ),
      child: Center(
        child: Column(
          children: [
            CircularProgressIndicator(
              valueColor:
                  AlwaysStoppedAnimation<Color>(const Color(0xFF22D400)),
            ),
            const SizedBox(height: 12),
            Text(
              S.of(context).loading_sensor_data,
              style: GoogleFonts.roboto(
                fontSize: 12,
                color: isDarkMode ? Colors.grey.shade300 : Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorMeasurements(
      BuildContext context, WidgetRef ref, Object error, bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDarkMode
            ? Colors.grey.shade800.withOpacity(0.5)
            : Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.red.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Center(
        child: Column(
          children: [
            Icon(
              Icons.error_outline,
              size: 32,
              color: Colors.red,
            ),
            const SizedBox(height: 12),
            Text(
              S.of(context).error_loading_sensor_data,
              style: GoogleFonts.roboto(
                fontSize: 12,
                color: isDarkMode ? Colors.grey.shade300 : Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            TextButton(
              onPressed: () {
                ref.invalidate(
                    fetchCurrentReadingsProvider(deviceNumber: deviceId));
              },
              child: Text(
                S.of(context).try_again,
                style: GoogleFonts.roboto(
                  fontSize: 12,
                  color: Colors.red,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoMeasurementsWidget(BuildContext context, bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: isDarkMode
            ? Colors.grey.shade800.withOpacity(0.5)
            : Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDarkMode ? Colors.grey.shade600 : Colors.grey.shade300,
          width: 1,
        ),
      ),
      child: Center(
        child: Column(
          children: [
            Icon(
              Icons.sensors_off,
              size: 32,
              color: isDarkMode ? Colors.grey.shade400 : Colors.grey[400],
            ),
            const SizedBox(height: 12),
            Text(
              S.of(context).no_sensor_data_available,
              style: GoogleFonts.roboto(
                fontSize: 12,
                color: isDarkMode ? Colors.grey.shade300 : Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingContent(BuildContext context, bool isDarkMode) {
    return Container(
      height: 200,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor:
                  AlwaysStoppedAnimation<Color>(const Color(0xFF22D400)),
            ),
            const SizedBox(height: 16),
            Text(
              S.of(context).loading_details,
              style: GoogleFonts.roboto(
                fontSize: 14,
                color: isDarkMode ? Colors.grey.shade300 : Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorContent(
      BuildContext context, WidgetRef ref, Object error, bool isDarkMode) {
    return Container(
      height: 200,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              S.of(context).error_loading_data,
              style: GoogleFonts.roboto(
                fontSize: 14,
                color: isDarkMode ? Colors.grey.shade300 : Colors.grey[600],
              ),
            ),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: () {
                ref.invalidate(
                    fetchZoneDetailsProvider(deviceNumber: deviceId));
                ref.invalidate(
                    fetchCurrentReadingsProvider(deviceNumber: deviceId));
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF22D400),
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              child: Text(
                S.of(context).try_again,
                style: GoogleFonts.roboto(fontSize: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
