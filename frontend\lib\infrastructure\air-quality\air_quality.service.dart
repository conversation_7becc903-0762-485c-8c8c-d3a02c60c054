import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:frontend/models/air_quality_locations_command.dart';
import 'package:frontend/models/air_quality_sensor_reading_command.dart';
import 'package:frontend/models/air_quality_zone_command.dart';

class AirQualityService {
  // Single base URL for all endpoints
  static const String _baseUrl = 'https://iot.thingslog.com:4443';
  static const String _loginEndpoint = '/api/login';
  static const String _devicesEndpoint = '/public-api/v2/devices';
  static const String _locationsEndpoint = '/public-api/devices/locations';
  static const String _companyId = '520';
  static const String _username = '<EMAIL>';
  static const String _password = 'pass';

  static const FlutterSecureStorage _storage = FlutterSecureStorage();

  // Single Dio instance for all requests
  static final Dio _dio = Dio(BaseOptions(
    baseUrl: _baseUrl,
    connectTimeout: const Duration(seconds: 10),
    receiveTimeout: const Duration(seconds: 10),
  ));

  /// Extract Bearer token from authorization header
  static String? _extractBearerToken(String? authHeader) {
    if (authHeader == null) return null;

    // Use regex to extract the token from "Bearer <token>" format
    final RegExp bearerRegex =
        RegExp(r'Bearer\s+([A-Za-z0-9\-_]+\.[A-Za-z0-9\-_]+\.[A-Za-z0-9\-_]+)');
    final match = bearerRegex.firstMatch(authHeader);

    return match?.group(1);
  }

  /// Get or request access token
  static Future<String?> _getAccessToken() async {
    try {
      // Check if we have a valid token stored
      final storedToken = await _storage.read(key: 'air_quality_access_token');
      final tokenExpiry = await _storage.read(key: 'air_quality_token_expiry');

      if (storedToken != null && tokenExpiry != null) {
        final expiryTime = DateTime.parse(tokenExpiry);
        final now = DateTime.now();

        // Check if token is still valid (with 5 minutes buffer before expiry)
        if (expiryTime.isAfter(now.add(const Duration(minutes: 5)))) {
          return storedToken;
        }
      }

      // Request new token
      print('🔐 Requesting new air quality access token...');

      final response = await _dio.post(
        _loginEndpoint,
        data: {
          'username': _username,
          'password': _password,
        },
        options: Options(
          contentType: Headers.jsonContentType,
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        // Extract token from authorization header
        final authHeader = response.headers.value('authorization');
        final accessToken = _extractBearerToken(authHeader);

        if (accessToken != null) {
          // Store token with a reasonable expiry time (24 hours as default)
          await _storage.write(
              key: 'air_quality_access_token', value: accessToken);
          await _storage.write(
            key: 'air_quality_token_expiry',
            value:
                DateTime.now().add(const Duration(hours: 23)).toIso8601String(),
          );

          print('✅ Air quality access token obtained successfully');
          return accessToken;
        } else {
          print('❌ Failed to extract bearer token from response header');
          return null;
        }
      } else {
        print('❌ Failed to get access token: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      print('❌ Error getting air quality access token: $e');
      return null;
    }
  }

  /// Add authentication header to Dio instance
  static Future<void> _ensureAuthenticated() async {
    final token = await _getAccessToken();
    print('🔑 Using token: ${token?.substring(0, 20)}...');

    if (token != null) {
      _dio.options.headers['Authorization'] = 'Bearer $token';
    } else {
      print('⚠️ Warning: No air quality access token available');
    }
  }

  /// Fetch all air quality locations (lightweight for map)
  static Future<List<AirQualityLocationModel>> fetchAllLocations() async {
    try {
      // Ensure we have a valid token
      await _ensureAuthenticated();

      print('🌐 AIR QUALITY REQUEST: Fetching all locations');
      print('🌐 URL: $_baseUrl$_locationsEndpoint?companyId=$_companyId');

      final response = await _dio.get(
        _locationsEndpoint,
        queryParameters: {'companyId': _companyId},
      );

      if (response.statusCode == 200) {
        print('✅ AIR QUALITY LOCATIONS RESPONSE: Success');
        print('📊 Response data type: ${response.data.runtimeType}');

        if (response.data is Map<String, dynamic>) {
          final locationsMap = response.data as Map<String, dynamic>;
          print('📊 Found ${locationsMap.length} locations');

          return locationsMap.entries
              .map((entry) => AirQualityLocationModel.fromMapEntry(entry))
              .toList();
        } else {
          print('❌ Unexpected response format: ${response.data}');
          return [];
        }
      } else {
        throw Exception(
            'Failed to load air quality locations with status ${response.statusCode}');
      }
    } on DioException catch (e) {
      print(
          "❌ Air Quality Locations Service Dio Error: ${e.response?.statusCode}");
      print("📩 Response Data: ${e.response?.data}");
      print("📝 Error Message: ${e.message}");
      print("🔗 Request URL: ${e.requestOptions.uri}");

      // If we get 401 or 403, try to refresh token and retry once
      if (e.response?.statusCode == 401 || e.response?.statusCode == 403) {
        print("🔄 Token might be expired, attempting to refresh...");
        await clearToken();
        await _ensureAuthenticated();

        try {
          print("🔄 Retrying locations request...");
          final response = await _dio.get(
            _locationsEndpoint,
            queryParameters: {'companyId': _companyId},
          );

          if (response.statusCode == 200 &&
              response.data is Map<String, dynamic>) {
            final locationsMap = response.data as Map<String, dynamic>;
            return locationsMap.entries
                .map((entry) => AirQualityLocationModel.fromMapEntry(entry))
                .toList();
          }
        } catch (retryError) {
          print("❌ Retry failed: $retryError");
        }
      }

      return [];
    } catch (e) {
      print("⚠ Air Quality Locations Service Unexpected Error: $e");
      return [];
    }
  }

  /// Fetch all air quality zones (devices) - for getting names and details
  static Future<List<AirQualityZoneModel>> fetchAllZones() async {
    try {
      // Ensure we have a valid token
      await _ensureAuthenticated();

      print('🌐 AIR QUALITY REQUEST: Fetching all zones');
      print('🌐 URL: $_baseUrl$_devicesEndpoint');

      final response = await _dio.get(_devicesEndpoint);

      if (response.statusCode == 200) {
        print('✅ AIR QUALITY ZONES RESPONSE: Success');
        print('📊 Response data type: ${response.data.runtimeType}');

        // Handle different response structures
        List<dynamic> jsonData;
        if (response.data is Map && response.data.containsKey('content')) {
          // If response has pagination structure
          jsonData = response.data['content'];
          print('📊 Found ${jsonData.length} zones in content');
        } else if (response.data is List) {
          // If response is directly a list
          jsonData = response.data;
          print('📊 Found ${jsonData.length} zones in list');
        } else {
          // If response is a single object, wrap it in a list
          jsonData = [response.data];
          print('📊 Single zone wrapped in list');
        }

        return jsonData
            .map((item) => AirQualityZoneModel.fromJson(item))
            .toList();
      } else {
        throw Exception(
            'Failed to load air quality zones with status ${response.statusCode}');
      }
    } on DioException catch (e) {
      print("❌ Air Quality Zones Service Dio Error: ${e.response?.statusCode}");
      print("📩 Response Data: ${e.response?.data}");
      print("📝 Error Message: ${e.message}");
      print("🔗 Request URL: ${e.requestOptions.uri}");

      // If we get 401 or 403, try to refresh token and retry once
      if (e.response?.statusCode == 401 || e.response?.statusCode == 403) {
        print("🔄 Token might be expired, attempting to refresh...");
        await clearToken();
        await _ensureAuthenticated();

        try {
          print("🔄 Retrying zones request...");
          final response = await _dio.get(_devicesEndpoint);
          if (response.statusCode == 200) {
            List<dynamic> jsonData;
            if (response.data is Map && response.data.containsKey('content')) {
              jsonData = response.data['content'];
            } else if (response.data is List) {
              jsonData = response.data;
            } else {
              jsonData = [response.data];
            }
            return jsonData
                .map((item) => AirQualityZoneModel.fromJson(item))
                .toList();
          }
        } catch (retryError) {
          print("❌ Retry failed: $retryError");
        }
      }

      return [];
    } catch (e) {
      print("⚠ Air Quality Zones Service Unexpected Error: $e");
      return [];
    }
  }

  /// Fetch details for a specific air quality zone by device number
  static Future<AirQualityZoneModel?> fetchZoneDetails({
    required String deviceNumber,
  }) async {
    final String endpoint = '$_devicesEndpoint/$deviceNumber';

    try {
      await _ensureAuthenticated();

      print(
          '🌐 AIR QUALITY REQUEST: Fetching zone details for device: $deviceNumber');
      print('🌐 URL: $_baseUrl$endpoint');

      final response = await _dio.get(endpoint);

      if (response.statusCode == 200) {
        print('✅ AIR QUALITY RESPONSE: Success');
        return AirQualityZoneModel.fromJson(response.data);
      } else {
        throw Exception(
            'Failed to load zone details with status ${response.statusCode}');
      }
    } on DioException catch (e) {
      print("❌ Air Quality Service Dio Error: ${e.response?.statusCode}");
      print("📩 Response Data: ${e.response?.data}");
      print("📝 Error Message: ${e.message}");
      print("🔗 Request URL: ${e.requestOptions.uri}");

      if (e.response?.statusCode == 401 || e.response?.statusCode == 403) {
        print("🔄 Token might be expired, attempting to refresh...");
        await clearToken();
        await _ensureAuthenticated();

        try {
          print("🔄 Retrying request...");
          final response = await _dio.get(endpoint);
          if (response.statusCode == 200) {
            return AirQualityZoneModel.fromJson(response.data);
          }
        } catch (retryError) {
          print("❌ Retry failed: $retryError");
        }
      }

      return null;
    } catch (e) {
      print("⚠ Air Quality Service Unexpected Error: $e");
      return null;
    }
  }

  static Future<List<SensorReadingModel>> fetchCurrentReadings({
    required String deviceNumber,
    int every = 1,
  }) async {
    final String endpoint = '$_devicesEndpoint/$deviceNumber/readings/current';

    try {
      await _ensureAuthenticated();

      print(
          '🌐 AIR QUALITY REQUEST: Fetching current readings for device: $deviceNumber');
      print('🌐 URL: $_baseUrl$endpoint?every=$every');

      final response = await _dio.get(
        endpoint,
        queryParameters: {'every': every},
      );

      if (response.statusCode == 200) {
        print('✅ AIR QUALITY READINGS RESPONSE: Success');

        if (response.data is List) {
          final List<dynamic> jsonData = response.data;
          print('📊 Found ${jsonData.length} sensor readings');

          return jsonData
              .map((item) => SensorReadingModel.fromJson(item))
              .where((reading) =>
                  reading.reading != null) // Filter out null readings
              .toList();
        } else {
          print('❌ Unexpected response format: ${response.data}');
          return [];
        }
      } else {
        throw Exception(
            'Failed to load sensor readings with status ${response.statusCode}');
      }
    } on DioException catch (e) {
      print(
          "❌ Air Quality Readings Service Dio Error: ${e.response?.statusCode}");
      print("📩 Response Data: ${e.response?.data}");
      print("📝 Error Message: ${e.message}");
      print("🔗 Request URL: ${e.requestOptions.uri}");

      if (e.response?.statusCode == 401 || e.response?.statusCode == 403) {
        print("🔄 Token might be expired, attempting to refresh...");
        await clearToken();
        await _ensureAuthenticated();

        try {
          print("🔄 Retrying readings request...");
          final response = await _dio.get(
            endpoint,
            queryParameters: {'every': every},
          );

          if (response.statusCode == 200 && response.data is List) {
            final List<dynamic> jsonData = response.data;
            return jsonData
                .map((item) => SensorReadingModel.fromJson(item))
                .where((reading) => reading.reading != null)
                .toList();
          }
        } catch (retryError) {
          print("❌ Retry failed: $retryError");
        }
      }

      return [];
    } catch (e) {
      print("⚠ Air Quality Readings Service Unexpected Error: $e");
      return [];
    }
  }

  /// Clear stored token (useful for logout or error recovery)
  static Future<void> clearToken() async {
    await _storage.delete(key: 'air_quality_access_token');
    await _storage.delete(key: 'air_quality_token_expiry');
    _dio.options.headers.remove('Authorization');
    print('🧹 Tokens cleared');
  }

  /// Check if user is authenticated
  static Future<bool> isAuthenticated() async {
    final token = await _storage.read(key: 'air_quality_access_token');
    final tokenExpiry = await _storage.read(key: 'air_quality_token_expiry');

    if (token == null || tokenExpiry == null) {
      return false;
    }

    final expiryTime = DateTime.parse(tokenExpiry);
    final now = DateTime.now();

    return expiryTime.isAfter(now.add(const Duration(minutes: 5)));
  }

  /// Manual login method (if needed for explicit authentication)
  static Future<bool> login() async {
    final token = await _getAccessToken();
    return token != null;
  }

  /// Logout method
  static Future<void> logout() async {
    await clearToken();
    print('🚪 Air quality user logged out');
  }
}
