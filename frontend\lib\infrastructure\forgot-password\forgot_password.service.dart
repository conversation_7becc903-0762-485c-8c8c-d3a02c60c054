import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:frontend/infrastructure/interceptor/global_interceptor.dart';
import 'package:frontend/models/forgot_password_email_command.dart';

class ForgotPasswordService {
  // Use global HTTP client
  static final Dio _dio = GlobalHttpClient().dio;

  /// **Send Forgot Password Email**
  static Future<bool> sendForgotPasswordEmail({
    required String email,
    required String languageCode,
  }) async {
    const String forgotPasswordEndpoint = 'Users/ForgotPassword';

    try {
      print('SENDING FORGOT PASSWORD EMAIL...');

      // Get timezone offset in minutes
      final DateTime now = DateTime.now();
      final int timezoneOffset = now.timeZoneOffset.inMinutes;

      final forgotPasswordCommand = ForgotPasswordCommand(
        email: email,
        timezoneOffset: timezoneOffset,
        languageCode: languageCode,
      );

      print(
          '📤 Forgot Password Request Data: ${jsonEncode(forgotPasswordCommand.toJson())}');

      final response = await _dio.post(
        forgotPasswordEndpoint,
        data: forgotPasswordCommand.toJson(),
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200 ||
          response.statusCode == 201 ||
          response.statusCode == 204) {
        print('✅ Forgot password email sent successfully');
        return true;
      } else {
        print(
            '❌ Failed to send forgot password email with status ${response.statusCode}');
        throw Exception(
            'Failed to send forgot password email with status ${response.statusCode}');
      }
    } on DioException catch (e) {
      print("❌ Forgot Password Dio Error: ${e.response?.statusCode}");
      print("📩 Forgot Password Response Data: ${e.response?.data}");
      print("📝 Forgot Password Error Message: ${e.message}");

      // You can handle specific error codes here if needed
      if (e.response?.statusCode == 404) {
        print("User with this email not found");
      } else if (e.response?.statusCode == 400) {
        print("Bad request - invalid email format");
      }

      return false;
    } catch (e) {
      print("⚠ Forgot Password Unexpected Error: $e");
      return false;
    }
  }
}
