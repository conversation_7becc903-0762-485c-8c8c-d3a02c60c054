import 'dart:async';
import 'package:dio/dio.dart';
import 'package:frontend/infrastructure/interceptor/global_interceptor.dart';
import 'package:frontend/models/register_command.dart';
import 'package:frontend/models/login_command.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:frontend/core/exceptions/app_exceptions.dart';
import 'package:dart_jsonwebtoken/dart_jsonwebtoken.dart';

class AuthenticationService {
  // Use global HTTP client instead of separate Dio instance
  static final Dio _dio = GlobalHttpClient().dio;
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage();

  /// **Get access token from secure storage**
  static Future<String?> getAccessToken() async {
    try {
      return await _secureStorage.read(key: 'token');
    } catch (e) {
      print('Error retrieving access token: $e');
      return null;
    }
  }

  /// **Use global refresh token method**
  static Future<Map<String, dynamic>?> refreshToken() async {
    return await GlobalHttpClient().refreshTokenPublic();
  }

  // **Decode JWT token safely**
  static Map<String, dynamic>? _decodeJWT(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) {
        print('❌ Invalid JWT format');
        return null;
      }

      final jwt = JWT.decode(token);
      return jwt.payload;
    } catch (e) {
      print('❌ Error decoding JWT: $e');
      return null;
    }
  }

  // Check if token is expired using JWT decoding
  static Future<bool> isTokenExpired() async {
    final token = await _secureStorage.read(key: 'token');
    if (token == null || token.isEmpty) return true;

    try {
      final payload = _decodeJWT(token);
      if (payload == null) return true;

      final exp = payload['exp'];
      if (exp == null) {
        print('⚠️ JWT token has no expiration claim');
        return true;
      }

      final expirationDate = DateTime.fromMillisecondsSinceEpoch(exp * 1000);
      final currentDate = DateTime.now();
      final isExpired = currentDate.isAfter(expirationDate);

      if (isExpired) {
        print(
            '⚠️ Token is expired. Expired at: $expirationDate, Current: $currentDate');
      }

      return isExpired;
    } catch (e) {
      print('❌ Error checking token expiration: $e');
      return true;
    }
  }

  // Get token expiration date using JWT decoding
  static Future<DateTime?> getTokenExpirationDate() async {
    final token = await _secureStorage.read(key: 'token');
    if (token == null || token.isEmpty) return null;

    try {
      final payload = _decodeJWT(token);
      if (payload == null) return null;

      final exp = payload['exp'];
      if (exp == null) {
        print('⚠️ JWT token has no expiration claim');
        return null;
      }

      final expirationDate = DateTime.fromMillisecondsSinceEpoch(exp * 1000);
      return expirationDate;
    } catch (e) {
      print('❌ Error getting token expiration date: $e');
      return null;
    }
  }

  // Get user info from JWT token
  static Future<Map<String, dynamic>?> getUserInfoFromToken() async {
    final token = await _secureStorage.read(key: 'token');
    if (token == null || token.isEmpty) return null;

    try {
      final payload = _decodeJWT(token);
      if (payload == null) return null;

      final userInfo = <String, dynamic>{};

      // Standard JWT claims - handle the Microsoft/ASP.NET Core format
      if (payload['sub'] != null) userInfo['userId'] = payload['sub'];

      // Handle Microsoft Identity claims format
      if (payload[
              'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier'] !=
          null) {
        userInfo['userId'] = payload[
            'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier'];
      }
      if (payload[
              'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress'] !=
          null) {
        userInfo['email'] = payload[
            'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress'];
      }
      if (payload[
              'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name'] !=
          null) {
        userInfo['name'] = payload[
            'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name'];
        userInfo['fullName'] = payload[
            'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name'];
      }

      // Standard claims
      if (payload['email'] != null) userInfo['email'] = payload['email'];
      if (payload['name'] != null) userInfo['name'] = payload['name'];
      if (payload['given_name'] != null)
        userInfo['firstName'] = payload['given_name'];
      if (payload['family_name'] != null)
        userInfo['lastName'] = payload['family_name'];
      if (payload['preferred_username'] != null)
        userInfo['username'] = payload['preferred_username'];

      // Custom claims
      if (payload['role'] != null) userInfo['role'] = payload['role'];
      if (payload['roles'] != null) userInfo['roles'] = payload['roles'];
      if (payload['fullName'] != null)
        userInfo['fullName'] = payload['fullName'];
      if (payload['username'] != null)
        userInfo['username'] = payload['username'];

      return userInfo;
    } catch (e) {
      print('❌ Error extracting user info from JWT: $e');
      return null;
    }
  }

  // Check if token expires within specified minutes
  static Future<bool> willTokenExpireWithin(int minutes) async {
    final expirationDate = await getTokenExpirationDate();
    if (expirationDate == null) return true;

    final currentDate = DateTime.now();
    final thresholdDate = currentDate.add(Duration(minutes: minutes));
    return expirationDate.isBefore(thresholdDate);
  }

  // Get time until token expiration
  static Future<Duration?> getTimeUntilExpiration() async {
    final expirationDate = await getTokenExpirationDate();
    if (expirationDate == null) return null;

    final currentDate = DateTime.now();
    if (currentDate.isAfter(expirationDate)) {
      return Duration.zero;
    }

    return expirationDate.difference(currentDate);
  }

  /// Convert Dio errors to custom exceptions
  static AppException _handleDioError(DioException e) {
    final statusCode = e.response?.statusCode;
    final data = e.response?.data;
    String? apiMessage = _extractErrorMessage(data);

    if (e.type == DioExceptionType.connectionTimeout ||
        e.type == DioExceptionType.receiveTimeout) {
      return const ConnectionTimeoutException();
    }

    if (e.type == DioExceptionType.connectionError) {
      return const NoInternetException();
    }

    switch (statusCode) {
      case 400:
        if (apiMessage != null) {
          final lowerMessage = apiMessage.toLowerCase();

          if (lowerMessage.contains('username') &&
              (lowerMessage.contains('taken') ||
                  lowerMessage.contains('already'))) {
            return const UserAlreadyExistsException();
          }

          if (lowerMessage.contains('password') &&
              (lowerMessage.contains('weak') ||
                  lowerMessage.contains('requirements'))) {
            return const WeakPasswordException();
          }

          if (lowerMessage.contains('email') &&
              (lowerMessage.contains('invalid') ||
                  lowerMessage.contains('format'))) {
            return const InvalidEmailException();
          }
        }
        return const ValidationErrorException();

      case 401:
        if (apiMessage != null &&
            apiMessage.toLowerCase().contains('not verified')) {
          return const EmailNotVerifiedException();
        }
        return const InvalidCredentialsException();

      case 403:
        return const AccessDeniedException();

      case 404:
        return const UserNotFoundException();

      case 409:
        return const UserAlreadyExistsException();

      case 422:
        return const ValidationErrorException();

      case 500:
      case 502:
      case 503:
        return const ServerErrorException();

      default:
        return const NetworkErrorException();
    }
  }

  /// Extract error message from response data
  static String? _extractErrorMessage(dynamic data) {
    if (data == null) return null;

    if (data is Map<String, dynamic>) {
      if (data['errors'] != null) {
        final errors = data['errors'];
        if (errors is Map<String, dynamic>) {
          final firstError = errors.values.first;
          if (firstError is List && firstError.isNotEmpty) {
            return firstError.first.toString();
          }
          return firstError.toString();
        } else if (errors is List && errors.isNotEmpty) {
          return errors.first.toString();
        }
      }

      if (data['detail'] != null) {
        return data['detail'].toString();
      }

      final messageFields = ['message', 'error'];
      for (final field in messageFields) {
        if (data[field] != null) {
          return data[field].toString();
        }
      }

      if (data['title'] != null) {
        return data['title'].toString();
      }

      if (data['isSuccessful'] == false && data['message'] != null) {
        return data['message'].toString();
      }
    } else if (data is String) {
      return data;
    }

    return null;
  }

  /// **Register a new user**
  static Future<Response> register(RegisterCommand command) async {
    const String registerEndpoint = 'Users/Register';

    try {
      final response = await _dio.post(
        registerEndpoint,
        data: command.toJson(),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        print("✅ Registration Successful: ${response.data}");
        return response;
      } else {
        print("❌ Registration Failed: Status ${response.statusCode}");
        throw const ServerErrorException();
      }
    } on DioException catch (e) {
      print("❌ Dio Error: ${e.response?.statusCode}");
      print("📩 Response Data: ${e.response?.data}");
      throw _handleDioError(e);
    } catch (e) {
      print("⚠ Unexpected Error: $e");
      if (e is AppException) rethrow;
      throw const ServerErrorException();
    }
  }

  /// **Login a user**
  static Future<Response> login(LoginCommand command) async {
    const String loginEndpoint = 'Users/Login';

    try {
      final response = await _dio.post(
        loginEndpoint,
        data: command.toJson(),
      );

      if (response.statusCode == 200) {
        print("✅ Login Successful: ${response.data}");

        final accessToken = response.data['accessToken'];
        final refreshToken = response.data['refreshToken'];

        if (accessToken != null) {
          await _secureStorage.write(key: 'token', value: accessToken);

          final userInfo = _decodeJWT(accessToken);
          if (userInfo != null) {
            // Handle Microsoft Identity claims
            if (userInfo[
                    'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier'] !=
                null) {
              await _secureStorage.write(
                  key: 'userId',
                  value: userInfo[
                          'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier']
                      .toString());
            } else if (userInfo['sub'] != null) {
              await _secureStorage.write(
                  key: 'userId', value: userInfo['sub'].toString());
            }

            if (userInfo[
                    'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress'] !=
                null) {
              await _secureStorage.write(
                  key: 'email',
                  value: userInfo[
                      'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress']);
            } else if (userInfo['email'] != null) {
              await _secureStorage.write(
                  key: 'email', value: userInfo['email']);
            }

            if (userInfo['username'] != null ||
                userInfo['preferred_username'] != null) {
              final username =
                  userInfo['username'] ?? userInfo['preferred_username'];
              await _secureStorage.write(key: 'username', value: username);
            }

            if (userInfo['role'] != null) {
              await _secureStorage.write(key: 'role', value: userInfo['role']);
            }

            if (userInfo[
                    'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name'] !=
                null) {
              await _secureStorage.write(
                  key: 'userFullName',
                  value: userInfo[
                      'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name']);
            } else if (userInfo['fullName'] != null ||
                userInfo['name'] != null) {
              final fullName = userInfo['fullName'] ?? userInfo['name'];
              await _secureStorage.write(key: 'userFullName', value: fullName);
            }

            print('👤 User info extracted from JWT and stored');
          }
        }
        if (refreshToken != null) {
          await _secureStorage.write(key: 'refreshToken', value: refreshToken);
        }

        return response;
      } else {
        print("❌ Login Failed: Status ${response.statusCode}");
        throw const ServerErrorException();
      }
    } on DioException catch (e) {
      print("❌ Dio Error: ${e.response?.statusCode}");
      print("📩 Response Data: ${e.response?.data}");
      throw _handleDioError(e);
    } catch (e) {
      print("⚠ Unexpected Error: $e");
      if (e is AppException) rethrow;
      throw const ServerErrorException();
    }
  }

  /// **Resend Verification Email**
  static Future<void> resendVerification(String email) async {
    const String resendEndpoint = 'Users/ResendConfirmationEmail';

    try {
      final response = await _dio.post(
        resendEndpoint,
        data: {"email": email},
      );

      if (response.statusCode == 204 || response.statusCode == 200) {
        print("✅ Verification email resent successfully.");
        return;
      } else {
        print("❌ Failed to resend verification email: ${response.statusCode}");
        throw const ServerErrorException();
      }
    } on DioException catch (e) {
      print("❌ Dio Error: ${e.response?.statusCode}");
      print("📩 Response Data: ${e.response?.data}");
      throw _handleDioError(e);
    } catch (e) {
      print("⚠ Unexpected Error: $e");
      if (e is AppException) rethrow;
      throw const ServerErrorException();
    }
  }

  /// **Verify Email (Confirm Email)**
  static Future<void> verifyEmail(
      String userId, String confirmationToken) async {
    const String verifyEmailEndpoint = 'Users/ConfirmEmail';

    try {
      final response = await _dio.put(
        verifyEmailEndpoint,
        data: {
          "userId": userId,
          "confirmationToken": confirmationToken,
        },
      );

      if (response.statusCode == 204 || response.statusCode == 200) {
        print("✅ Email verified successfully");
        return;
      } else {
        print("❌ Email verification failed: Status ${response.statusCode}");
        throw const ServerErrorException();
      }
    } on DioException catch (e) {
      print("❌ Dio Error: ${e.response?.statusCode}");
      print("📩 Response Data: ${e.response?.data}");
      throw _handleDioError(e);
    } catch (e) {
      print("⚠ Unexpected Error: $e");
      if (e is AppException) rethrow;
      throw const ServerErrorException();
    }
  }
}
