import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:frontend/presentation/widgets/animated_search_bar.dart';
import 'package:frontend/providers/interest_points_provider.dart';
import 'package:frontend/providers/language_provider.dart';
import 'package:frontend/providers/theme_provider.dart';
import 'package:frontend/providers/favorites/favourites_provider.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:ui' as ui;

class MapWithSearchAndData extends ConsumerStatefulWidget {
  final List<String> categories;
  final String path;
  final String type;
  final String? subPath;
  final String? iconPath; // New parameter for SVG icon path
  final double mapHeight; // Map height can be customized

  const MapWithSearchAndData({
    super.key,
    required this.categories,
    required this.path,
    required this.type,
    this.subPath,
    this.iconPath, // Optional SVG icon path
    this.mapHeight = 250, // Default height
  });

  @override
  _MapWithSearchAndDataState createState() => _MapWithSearchAndDataState();
}

class _MapWithSearchAndDataState extends ConsumerState<MapWithSearchAndData> {
  GoogleMapController? _mapController;
  final LatLng _myCurrentLocation = const LatLng(42.4842, 26.5035);
  String? _mapStyle;
  bool _isMapStyleLoaded = false;
  BitmapDescriptor? _categoryIcon;
  Set<Marker> _markers = {};

  // Add these variables to track initialization state
  bool _isMapReady = false;
  bool _hasInitializedData = false;

  @override
  void initState() {
    super.initState();
    // Load the saved language first
    ref.read(languageNotifierProvider.notifier).loadSavedLanguage();

    // Set the categories filter when the widget initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeData();
    });

    if (widget.iconPath != null) {
      _loadSvgMarker();
    }
  }

  // Separate method to initialize data
  void _initializeData() {
    if (_hasInitializedData) return;

    final languageCode = ref.read(languageNotifierProvider).languageCode;

    ref
        .read(interestPointFiltersProvider.notifier)
        .setLanguageCode(languageCode);

    if (ref.read(interestPointFiltersProvider).categories == null) {
      ref
          .read(interestPointFiltersProvider.notifier)
          .setCategories(widget.categories);
    }

    _hasInitializedData = true;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _loadMapStyle();
  }

  Future<void> _loadSvgMarker() async {
    if (widget.iconPath == null) return;

    try {
      final svgString = await rootBundle.loadString(widget.iconPath!);
      final pictureInfo =
          await vg.loadPicture(SvgStringLoader(svgString), null);

      final ui.PictureRecorder recorder = ui.PictureRecorder();
      final Canvas canvas = Canvas(recorder);
      final double width = 45.0;
      final double height = 45.0;

      final double logicalSize =
          45.0; // Physical size you want on screen (logical pixels)
      final double pixelRatio = MediaQuery.of(context).devicePixelRatio;

      final double bitmapWidth = logicalSize * pixelRatio;
      final double bitmapHeight = logicalSize * pixelRatio;

      // Calculate scale factor to scale the SVG down so it fits the logical size
      final double scaleX = bitmapWidth / pictureInfo.size.width;
      final double scaleY = bitmapHeight / pictureInfo.size.height;
      final double scale = scaleX < scaleY ? scaleX : scaleY;

      // To keep physical size fixed, divide by pixelRatio so drawing is smaller on canvas
      final double adjustedScale = scale / pixelRatio;

      final double translateX =
          (logicalSize - (pictureInfo.size.width * adjustedScale)) /
              2 *
              pixelRatio;
      final double translateY =
          (logicalSize - (pictureInfo.size.height * adjustedScale)) /
              2 *
              pixelRatio;

      // Translate and scale canvas
      canvas.translate(translateX, translateY);
      canvas.scale(adjustedScale * pixelRatio);

      // Draw the SVG picture
      canvas.drawPicture(pictureInfo.picture);

      // Convert to image with high resolution
      final ui.Image image = await recorder.endRecording().toImage(
            bitmapWidth.toInt(),
            bitmapHeight.toInt(),
          );

      final ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);
      if (byteData != null) {
        final Uint8List uint8List = byteData.buffer.asUint8List();
        final BitmapDescriptor icon = BitmapDescriptor.fromBytes(uint8List);

        if (mounted) {
          setState(() {
            _categoryIcon = icon;
          });
          _updateMarkersFromCurrentData();
        }
      }
    } catch (e) {
      print("Error loading SVG marker: $e");
    }
  }

  void _updateMarkersFromCurrentData() {
    final asyncInterestPoints = ref.read(fetchInterestPointsProvider);
    asyncInterestPoints.whenData((interestPoints) {
      _updateMarkers(interestPoints);
    });
  }

  void _updateMarkers(List interestPoints) {
    if (_categoryIcon == null || !_isMapReady) return;

    final newMarkers = interestPoints
        .where((item) => item.latitude != null && item.longitude != null)
        .map((item) => Marker(
              markerId: MarkerId(item.id.toString()),
              position: LatLng(item.latitude!, item.longitude!),
              icon: _categoryIcon!,
              infoWindow: InfoWindow(title: item.name),
              onTap: () {
                _mapController?.animateCamera(
                  CameraUpdate.newLatLngZoom(
                    LatLng(item.latitude!, item.longitude!),
                    16.0,
                  ),
                );
              },
            ))
        .toSet();

    if (mounted) {
      setState(() {
        _markers = newMarkers;
      });
    }
  }

  Future<void> _loadMapStyle() async {
    final themeMode = ref.read(themeNotifierProvider);
    final isDarkMode = themeMode == ThemeMode.dark;

    try {
      if (isDarkMode) {
        _mapStyle =
            await rootBundle.loadString('assets/map_styles/dark_map.json');
      } else {
        _mapStyle =
            await rootBundle.loadString('assets/map_styles/light_map.json');
      }

      if (mounted) {
        setState(() {
          _isMapStyleLoaded = true;
        });
      }
    } catch (e) {
      print("Error loading map style: $e");
    }
  }

  void _onMapCreated(GoogleMapController controller) async {
    _mapController = controller;

    setState(() {
      _isMapReady = true;
    });

    // Update markers if we have data
    _updateMarkersFromCurrentData();
  }

  void _onSearchSubmitted(String searchText) {
    print("Search text $searchText");
    ref.read(interestPointFiltersProvider.notifier).setSearch(searchText);
  }

  @override
  Widget build(BuildContext context) {
    final themeMode = ref.watch(themeNotifierProvider);
    final isDarkMode = themeMode == ThemeMode.dark;
    final asyncInterestPoints = ref.watch(fetchInterestPointsProvider);

    // Update markers when data changes
    asyncInterestPoints.whenData((interestPoints) {
      if (_categoryIcon != null && _isMapReady) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _updateMarkers(interestPoints);
        });
      }
    });

    return Scaffold(
      body: Column(
        children: [
          // Google Map with improved gesture handling
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.25, // or 0.35
            child: Stack(
              children: [
                // Wrap GoogleMap with AbsorbPointer and GestureDetector for better touch handling
                GestureDetector(
                  // Allow all gestures to pass through to the map
                  behavior: HitTestBehavior.translucent,
                  child: GoogleMap(
                    onMapCreated: _onMapCreated,
                    initialCameraPosition: CameraPosition(
                      target: _myCurrentLocation,
                      zoom: 14.0,
                    ),
                    markers: _markers,
                    style: _mapStyle,
                    mapType: MapType.normal,
                    myLocationEnabled: false,
                    compassEnabled: true,
                    zoomControlsEnabled: true,
                    // Enable all gesture types for better responsiveness
                    scrollGesturesEnabled: true,
                    zoomGesturesEnabled: true,
                    tiltGesturesEnabled: true,
                    rotateGesturesEnabled: true,
                    mapToolbarEnabled: false,
                    // Add these properties for better performance and responsiveness
                    buildingsEnabled: false,
                    trafficEnabled: false,
                    // Improve rendering performance
                    liteModeEnabled: false,
                    // Add gesture recognizers for better touch handling
                    gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>{
                      Factory<PanGestureRecognizer>(
                        () => PanGestureRecognizer(),
                      ),
                      Factory<ScaleGestureRecognizer>(
                        () => ScaleGestureRecognizer(),
                      ),
                      Factory<TapGestureRecognizer>(
                        () => TapGestureRecognizer(),
                      ),
                      Factory<VerticalDragGestureRecognizer>(
                        () => VerticalDragGestureRecognizer(),
                      ),
                      Factory<HorizontalDragGestureRecognizer>(
                        () => HorizontalDragGestureRecognizer(),
                      ),
                    },
                  ),
                ),
                Positioned(
                  bottom: 16.0,
                  right: 60.0,
                  child: AnimatedSearchTextFeild(
                    onSubmitted: _onSearchSubmitted,
                  ),
                ),
              ],
            ),
          ),
          // ListView below the map - FIXED for full height
          Expanded(
            child: asyncInterestPoints.when(
              data: (interestPoints) {
                if (interestPoints.isEmpty) {
                  return const Center(child: Text('No interest points found'));
                }

                return ListView.builder(
                  padding: const EdgeInsets.only(right: 14, top: 15),
                  itemCount: interestPoints.length,
                  itemBuilder: (context, index) {
                    final item = interestPoints[index];
                    return GestureDetector(
                      onTap: () {
                        final route = widget.subPath != null
                            ? '/${widget.type}/${widget.subPath}/${widget.path}/${item.id}'
                            : '/${widget.type}/${widget.path}/${item.id}';
                        context.push(route);
                      },
                      child: Container(
                        margin: const EdgeInsets.only(bottom: 16),
                        width: double.infinity,
                        height: 76,
                        decoration: BoxDecoration(
                          color: isDarkMode
                              ? const Color(0xFF333333)
                              : Colors.white,
                          borderRadius: const BorderRadius.only(
                            topRight: Radius.circular(10),
                            bottomRight: Radius.circular(10),
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: isDarkMode
                                  ? Colors.black.withOpacity(0.2)
                                  : Colors.black.withOpacity(0.1),
                              offset: const Offset(4, 4),
                              blurRadius: 6,
                            ),
                          ],
                        ),
                        child: ListTile(
                          title: Padding(
                            padding: const EdgeInsets.only(top: 14.0),
                            child: Text(
                              item.name,
                              style: GoogleFonts.roboto(
                                fontWeight: FontWeight.w400,
                                fontSize: 17,
                                height: 15 / 14,
                                letterSpacing: 0.0,
                                color: isDarkMode
                                    ? Colors.white
                                    : const Color(0xFF626262),
                              ),
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stackTrace) =>
                  Center(child: Text('Error: $error')),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }
}
