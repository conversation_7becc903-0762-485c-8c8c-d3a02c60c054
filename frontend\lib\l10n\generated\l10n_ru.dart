import 'l10n.dart';

// ignore_for_file: type=lint

/// The translations for Russian (`ru`).
class SRu extends S {
  SRu([String locale = 'ru']) : super(locale);

  @override
  String get appTitle => 'Смарт Ямбол';

  @override
  String get guest => 'Гость';

  @override
  String get settings => 'Настройки';

  @override
  String get darkMode => 'Темная тема';

  @override
  String get language => 'Язык';

  @override
  String get terms => 'Условия использования';

  @override
  String get login => 'Войти';

  @override
  String get tourism => 'Туризм';

  @override
  String get tourismDescription =>
      'Исследуйте красоту Ямбола и создайте незабываемые воспоминания!';

  @override
  String get weather => 'Погода';

  @override
  String get weatherDescription =>
      'Будьте готовы! Посмотрите прогноз погоды и качество воздуха.';

  @override
  String get events => 'События';

  @override
  String get eventsDescription =>
      'Узнайте о культурных и спортивных мероприятиях в городе.';

  @override
  String get repairs => 'Ремонты';

  @override
  String get repairsDescription =>
      'Будьте в курсе! Узнайте где и когда проводятся ремонтные работы.';

  @override
  String get parking => 'Парковка';

  @override
  String get parkingDescription =>
      'Найдите парковочное место или оплатите парковку в синей зоне через SMS.';

  @override
  String get generalInfo => 'Общая информация';

  @override
  String get generalInfoDescription =>
      'Откройте для себя все, что нужно знать о Ямболе в одном месте.';

  @override
  String get transport => 'Транспорт';

  @override
  String get transportDescription =>
      'Актуальная информация о маршрутах и расписании общественного транспорта.';

  @override
  String get cameras => 'Камеры';

  @override
  String get camerasDescription => 'Прямые трансляции с ключевых точек города.';

  @override
  String get news => 'Новости';

  @override
  String get newsDescription =>
      'Будьте в курсе! Следите за последними новостями и событиями.';

  @override
  String get taxes => 'Налоги';

  @override
  String get taxesDescription =>
      'Просматривайте и оплачивайте муниципальные налоги быстро и легко.';

  @override
  String get weddingServices => 'Свадьбы';

  @override
  String get weddingServicesDescription =>
      'Легко выберите идеальную дату и место для вашей свадьбы!';

  @override
  String get reports => 'Жалобы';

  @override
  String get reportsDescription =>
      'Сообщите о проблеме в городе и помогите сделать его лучше.';

  @override
  String get favorites => 'Избранное';

  @override
  String get location => 'Местоположение';

  @override
  String get home => 'Главная';

  @override
  String get messages => 'Сообщения';

  @override
  String get emailOrUsername => 'Email или имя пользователя';

  @override
  String get password => 'Пароль';

  @override
  String get forgotPassword => 'Забыли пароль?';

  @override
  String get loginButton => 'Войти';

  @override
  String get orContinueWith => '- или продолжить с -';

  @override
  String get createNewAccount => 'Создать новый аккаунт';

  @override
  String get registerHere => 'здесь';

  @override
  String get register => 'Регистрация';

  @override
  String get confirmPassword => 'Подтвердить пароль';

  @override
  String get agreeToTerms => 'Нажимая кнопку Регистрация, вы соглашаетесь с';

  @override
  String get registerTerms => 'условиями использования';

  @override
  String get registerButton => 'Зарегистрироваться';

  @override
  String get alreadyHaveAccount => 'Уже есть аккаунт?';

  @override
  String get loginHere => 'Войти здесь';

  @override
  String get dontHaveAccount => 'Нет аккаунта?';

  @override
  String get firstName => 'Имя';

  @override
  String get lastName => 'Фамилия';

  @override
  String get email => 'Email';

  @override
  String get emailRequired => 'Email обязателен';

  @override
  String get invalidEmail => 'Пожалуйста, введите действительный email';

  @override
  String get registrationSuccessful => 'Регистрация успешна!';

  @override
  String get passwordRequired => 'Пароль обязателен';

  @override
  String get passwordTooShort =>
      'Пароль должен быть длиной не менее 8 символов';

  @override
  String get confirmPasswordRequired => 'Пожалуйста, подтвердите ваш пароль';

  @override
  String get passwordsDoNotMatch => 'Пароли не совпадают';

  @override
  String get firstNameRequired => 'Имя обязательно';

  @override
  String get lastNameRequired => 'Фамилия обязательна';

  @override
  String get tourismWelcomeMessage =>
      'Исследуйте Ямбол! Откройте для себя его достопримечательности и вдохновитесь его историей.';

  @override
  String get tourismLegendsAndMyths => 'Легенды и мифы';

  @override
  String get tourismSights => 'Достопримечательности';

  @override
  String get tourismCulturalAndArtisticSites =>
      'Культурные и художественные объекты';

  @override
  String get tourismRoutesAndActivities => 'Маршруты и мероприятия';

  @override
  String get tourismFamilyEntertainment => 'Семейные развлечения';

  @override
  String get tourismNightlife => 'Ночная жизнь';

  @override
  String get tourismTransport => 'Транспорт';

  @override
  String get tourismTravelAgencies => 'Туристические агентства';

  @override
  String get loginSuccess => 'Успешный вход';

  @override
  String get loginError => 'Ошибка входа';

  @override
  String get legendsAndMythsWelcomeMessage =>
      'Откройте тайны Ямбола: Легенды и мифы, которые вас пленят';

  @override
  String get weatherTitle => 'Погода';

  @override
  String get cityName => 'Ямбол';

  @override
  String get wind => 'Ветер';

  @override
  String get humidity => 'Влажность';

  @override
  String get errorLoadingWeather => 'Ошибка загрузки данных о погоде';

  @override
  String get retry => 'Повторить';

  @override
  String get monday => 'Понедельник';

  @override
  String get tuesday => 'Вторник';

  @override
  String get wednesday => 'Среда';

  @override
  String get thursday => 'Четверг';

  @override
  String get friday => 'Пятница';

  @override
  String get saturday => 'Суббота';

  @override
  String get sunday => 'Воскресенье';

  @override
  String get eventsWelcomeMessage =>
      'Не пропустите ничего! Исследуйте культурные и спортивные мероприятия в городе!';

  @override
  String get search => 'Поиск...';

  @override
  String get tourismCulturalSites => 'Культурные объекты';

  @override
  String get map => 'Карта';

  @override
  String get all => 'Все';

  @override
  String get sportEvents => 'Спорт';

  @override
  String get cultureEvents => 'Культура';

  @override
  String get celebrationEvents => 'Праздники';

  @override
  String get mon => 'Пн';

  @override
  String get tue => 'Вт';

  @override
  String get wed => 'Ср';

  @override
  String get thu => 'Чт';

  @override
  String get fri => 'Пт';

  @override
  String get sat => 'Сб';

  @override
  String get sun => 'Вс';

  @override
  String get january => 'Январь';

  @override
  String get february => 'Февраль';

  @override
  String get march => 'Март';

  @override
  String get april => 'Апрель';

  @override
  String get may => 'Май';

  @override
  String get june => 'Июнь';

  @override
  String get july => 'Июль';

  @override
  String get august => 'Август';

  @override
  String get september => 'Сентябрь';

  @override
  String get october => 'Октябрь';

  @override
  String get november => 'Ноябрь';

  @override
  String get december => 'Декабрь';

  @override
  String get today => 'Сегодня';

  @override
  String get thisWeek => 'На этой неделе';

  @override
  String get thisMonth => 'В этом месяце';

  @override
  String get noFavorites => 'Нет избранного';

  @override
  String get tourismScreenTitle => 'Туризм';

  @override
  String get more => 'Больше';

  @override
  String get selectCategory => 'Выберите категорию';

  @override
  String get emailConfirmationTitle => 'Подтверждение email';

  @override
  String get emailConfirmationMessage =>
      'Пожалуйста, проверьте свой email для подтверждения аккаунта.';

  @override
  String get didNotReceiveEmail => 'Не получили email?';

  @override
  String get resendVerificationEmail =>
      'Отправить письмо с подтверждением повторно';

  @override
  String get verificationEmailResent =>
      'Письмо с подтверждением отправлено повторно!';

  @override
  String get errorOccurred => 'Произошла ошибка';

  @override
  String get emailVerificationTitle => 'Подтверждение email';

  @override
  String get verifyEmailButton => 'Подтвердить email';

  @override
  String get emailVerifiedSuccessfully => 'Email успешно подтвержден!';

  @override
  String get generalInfoWelcomeMessage =>
      'Ямбол – бизнес, образование и важная информация о городе!';

  @override
  String get gasStations => 'Заправочные станции';

  @override
  String get generalInformationFull => 'Общая информация';

  @override
  String get shop => 'Магазины';

  @override
  String get restaurants => 'Рестораны';

  @override
  String get coffee => 'Кофе';

  @override
  String get bars => 'Бары';

  @override
  String get pastryShops => 'Кондитерские';

  @override
  String get establishments => 'Заведения';

  @override
  String get establishmentsWelcomeMessage =>
      'Уютные рестораны, традиционная кухня и современные заведения с уникальной атмосферой!';

  @override
  String get hotels => 'Отели';

  @override
  String get guestHouses => 'Гостевые дома';

  @override
  String get accommodation => 'Жилье';

  @override
  String get accommodationWelcomeMessage =>
      'Комфортабельные отели, уютные гостевые дома и отличные места для проживания на любой вкус!';

  @override
  String get finance => 'Финансы';

  @override
  String get banks => 'Банки';

  @override
  String get currencyExchanges => 'Обмен валют';

  @override
  String get insuranceCompanies => 'Страхование';

  @override
  String get atms => 'Банкоматы';

  @override
  String get financeYambolWelcomeMessage =>
      'Безопасные банковские услуги, доступные кредиты и финансовые решения для вас в Ямболе!';

  @override
  String get bioShops => 'Био-магазины';

  @override
  String get farms => 'Фермы';

  @override
  String get recycling => 'Переработка';

  @override
  String get ecoInitiatives => 'Эко-инициативы';

  @override
  String get ecology => 'Экология';

  @override
  String get ecoYambolWelcomeMessage =>
      'Чистая природа, устойчивое будущее и экологические решения для Ямбола!';

  @override
  String get culture => 'Культура';

  @override
  String get museums => 'Музеи';

  @override
  String get theaters => 'Театры';

  @override
  String get galleries => 'Галереи';

  @override
  String get cultureYambolWelcomeMessage =>
      'Богатое культурное наследие, вдохновляющее искусство и традиции в Ямболе!';

  @override
  String get education => 'Образование';

  @override
  String get kindergardens => 'Детские сады';

  @override
  String get nursery => 'Ясли';

  @override
  String get childNutritionCenter => 'Центр детского питания';

  @override
  String get schools => 'Школы';

  @override
  String get universities => 'Университеты';

  @override
  String get developmentCenters => 'Центры развития';

  @override
  String get health => 'Здоровье';

  @override
  String get pharmacies => 'Аптеки';

  @override
  String get medicalEstablishments => 'Медицинские учреждения';

  @override
  String get doctorsOffices => 'Кабинеты врачей';

  @override
  String get medicalLabs => 'Медицинские лаборатории';

  @override
  String get veterinaries => 'Ветеринарные клиники';

  @override
  String get healthYambolWelcomeMessage =>
      'Здоровье и забота для всех – качественные медицинские услуги и здоровый образ жизни в Ямболе!';

  @override
  String get educationYambolWelcomeMessage =>
      'Качественное образование для светлого будущего – инновационное обучение и развитие в Ямболе!';

  @override
  String get sport => 'Спорт';

  @override
  String get sportFacilities => 'Спортивные сооружения';

  @override
  String get sportYambolWelcomeMessage =>
      'Спорт и активный образ жизни – современные сооружения и возможности для движения в Ямболе!';

  @override
  String get newsYambolWelcomeMessage =>
      'Будьте в курсе! Следите за последними новостями и событиями.';

  @override
  String get close => 'Закрыть';

  @override
  String get description => 'Описание';

  @override
  String get municipalityTaxes => 'Муниципальные налоги';

  @override
  String get information => 'Информация';

  @override
  String get chooseWayToPay =>
      'Вы можете оплатить онлайн, используя ваш ЕГН или электронную подпись (которая должна быть установлена на вашем устройстве).';

  @override
  String get waysToPay => 'Способы оплаты';

  @override
  String get kinNumber => 'Оплата по ЕГН';

  @override
  String get kinDesc => 'Быстрая и удобная оплата по вашему номеру ЕГН';

  @override
  String get electronicSignature => 'Оплата электронной подписью';

  @override
  String get electronicSignatureDesc =>
      'Безопасная оплата с помощью вашей электронной подписи';

  @override
  String get userHelp =>
      'Нужна помощь? Свяжитесь с нами по телефону: 0875 333 844';

  @override
  String get problemElectronicSignature =>
      'Ссылка для оплаты электронной подписью не может быть открыта.';

  @override
  String get problemKin => 'Ссылка для оплаты по ЕГН не может быть открыта.';

  @override
  String get navigate => 'Навигация';

  @override
  String get business => 'Бизнес';

  @override
  String get landmarks => 'Достопримечательности';

  @override
  String get healthcare => 'Здравоохранение';

  @override
  String get sports => 'Спорт';

  @override
  String get applyFilters => 'Применить фильтры';

  @override
  String get filterCategories => 'Фильтр категорий';

  @override
  String get verifyingEmail => 'Подтверждение вашего email...';

  @override
  String get processingVerification => 'Обработка подтверждения...';

  @override
  String get retryVerification => 'Повторить подтверждение';

  @override
  String get verificationFailed => 'Подтверждение не удалось';

  @override
  String get checkSpamFolder => 'Не забудьте проверить папку спам!';

  @override
  String get userAlreadyExists => 'Аккаунт с таким email уже существует!';

  @override
  String get invalidCredentials =>
      'Неверный email или пароль. Пожалуйста, проверьте ваши данные и попробуйте снова.';

  @override
  String get emailNotVerified =>
      'Пожалуйста, подтвердите ваш email адрес перед входом.';

  @override
  String get weakPassword =>
      'Пароль должен быть длиной не менее 8 символов и содержать заглавные, строчные буквы и цифры.';

  @override
  String get invalidEmailFormat =>
      'Пожалуйста, введите действительный email адрес.';

  @override
  String get connectionTimeout =>
      'Тайм-аут соединения. Пожалуйста, проверьте ваше интернет-соединение и попробуйте снова.';

  @override
  String get noInternet =>
      'Нет интернет-соединения. Пожалуйста, проверьте настройки сети.';

  @override
  String get userNotFound =>
      'Пользователь не найден. Пожалуйста, проверьте ваши данные или зарегистрируйте новый аккаунт.';

  @override
  String get resourceNotFound => 'Запрошенный ресурс не найден.';

  @override
  String get serverError => 'Ошибка сервера. Пожалуйста, попробуйте позже.';

  @override
  String get validationError =>
      'Ошибка валидации. Пожалуйста, проверьте ваши данные.';

  @override
  String get networkError => 'Произошла неожиданная ошибка.';

  @override
  String get accessDenied => 'Доступ запрещен.';

  @override
  String get passwordRequirements =>
      'Пароль должен содержать минимум одну заглавную букву, одну строчную букву и одну цифру';

  @override
  String get requirementMinLength => 'Минимум 6 символов';

  @override
  String get requirementUppercase => 'Минимум одна заглавная буква (A-Z)';

  @override
  String get requirementNumber => 'Минимум одна цифра (0-9)';

  @override
  String get requirementSpecialChar =>
      'Минимум один специальный символ (!@#\$%^&*)';

  @override
  String get passwordMustContainUppercase =>
      'Пароль должен содержать минимум одну заглавную букву';

  @override
  String get passwordMustContainNumber =>
      'Пароль должен содержать минимум одну цифру';

  @override
  String get passwordMustContainSpecialChar =>
      'Пароль должен содержать минимум один специальный символ';

  @override
  String get dangerousBuildingOrSite => 'Опасное здание или место';

  @override
  String get streetLight => 'Уличное освещение';

  @override
  String get trash => 'Мусор';

  @override
  String get dangerousPlayground => 'Опасная детская площадка';

  @override
  String get pothole => 'Яма на дороге';

  @override
  String get brokenOrMissingSign => 'Сломанный или отсутствующий знак';

  @override
  String get illegalParking => 'Незаконная парковка';

  @override
  String get hangingCables => 'Висящие кабели';

  @override
  String get waterLeakage => 'Утечка воды';

  @override
  String get fallenTree => 'Упавшее дерево';

  @override
  String get collapsedRoad => 'Обвалившаяся дорога';

  @override
  String get illegalBuilding => 'Незаконная постройка';

  @override
  String get trafficLightNotWorking => 'Не работает светофор';

  @override
  String get strayDogs => 'Бездомные собаки';

  @override
  String get publicOrder => 'Общественный порядок';

  @override
  String get abandonedCar => 'Брошенная машина';

  @override
  String get other => 'Другое';

  @override
  String get title => 'Заголовок';

  @override
  String get typeOfSignal => 'Тип сигнала';

  @override
  String get phone => 'Телефон';

  @override
  String get address => 'Адрес';

  @override
  String get files => 'Файлы';

  @override
  String get addFiles => 'Добавить файлы';

  @override
  String get submitReport => 'Отправить жалобу';

  @override
  String get noFilesSelected => 'Файлы не выбраны';

  @override
  String get titleIsRequired => 'Заголовок обязателен';

  @override
  String get phoneIsRequired => 'Телефон обязателен';

  @override
  String get typeOfSignalIsRequired => 'Тип сигнала обязателен';

  @override
  String get reportSubmittedSuccessfully => 'Жалоба успешно отправлена!';

  @override
  String get reportSubmissionFailed =>
      'Не удалось отправить жалобу. Пожалуйста, попробуйте снова.';

  @override
  String get reportAnIssue => 'Сообщить о проблеме';

  @override
  String get loginRequired => 'Требуется авторизация для отправки жалоб';

  @override
  String get pleaseLoginToSubmitReports =>
      'Пожалуйста, войдите в систему для отправки жалоб';

  @override
  String get selectSource => 'Выберите источник';

  @override
  String get camera => 'Камера';

  @override
  String get gallery => 'Галерея';

  @override
  String get passwordResetEmailSent =>
      'Email для сброса пароля успешно отправлен!';

  @override
  String get resetYourPassword => 'Сбросить пароль';

  @override
  String get enterEmailToReset =>
      'Введите ваш email адрес, и мы отправим вам ссылку для сброса пароля.';

  @override
  String get sendResetLink => 'Отправить ссылку для сброса';

  @override
  String get rememberYourPassword => 'Помните свой пароль?';

  @override
  String get backToLogin => 'Вернуться ко входу';

  @override
  String get emailAddress => 'Email адрес';

  @override
  String get errorSendingResetEmail =>
      'Ошибка отправки email для сброса. Пожалуйста, попробуйте снова.';

  @override
  String get linkExpired => 'Ссылка истекла';

  @override
  String get resetLinkExpiredMessage =>
      'Эта ссылка для сброса пароля истекла. Пожалуйста, запросите новую.';

  @override
  String get invalidLink => 'Недействительная ссылка';

  @override
  String get invalidLinkMessage =>
      'Эта ссылка для сброса пароля недействительна. Пожалуйста, запросите новую.';

  @override
  String get requestNewLink => 'Запросить новую ссылку';

  @override
  String get passwordResetSuccess => 'Пароль успешно сброшен!';

  @override
  String get passwordResetError =>
      'Не удалось сбросить пароль. Пожалуйста, попробуйте снова.';

  @override
  String get resetPassword => 'Сбросить пароль';

  @override
  String get createNewPassword => 'Создать новый пароль';

  @override
  String get enterNewPasswordMessage =>
      'Введите ваш новый пароль ниже. Убедитесь, что он надежный и безопасный.';

  @override
  String get newPassword => 'Новый пароль';

  @override
  String get confirmNewPassword => 'Подтвердить новый пароль';

  @override
  String get resetPasswordButton => 'Сбросить пароль';

  @override
  String get repairStatusOngoing => '🔧 В процессе';

  @override
  String get repairStatusCompleted => '✅ Завершено';

  @override
  String get repairStatusScheduled => '📅 Запланировано';

  @override
  String get notSpecified => 'Не указано';

  @override
  String get unknownRepair => 'Неизвестный ремонт';

  @override
  String get start => 'Начало';

  @override
  String get end => 'Конец';

  @override
  String get noDescriptionAvailable => 'Описание недоступно.';

  @override
  String get repairNotFound => 'Ремонт не найден';

  @override
  String get loadingError => 'Ошибка загрузки';

  @override
  String get notifications => 'Уведомления';

  @override
  String get allEnabled => 'Все включены';

  @override
  String get newsOnly => 'Только новости';

  @override
  String get eventsOnly => 'Только события';

  @override
  String get allDisabled => 'Все отключены';

  @override
  String get repairsOnly => 'Только ремонты';

  @override
  String get systemPermissionRequired => 'Требуется системное разрешение';

  @override
  String get systemPermissionMessage =>
      'Включите уведомления в настройках телефона для получения оповещений.';

  @override
  String get enableNotifications => 'Включить уведомления';

  @override
  String get enableNotificationsMessage =>
      'Для получения уведомлений необходимо разрешить уведомления в настройках телефона. Хотите включить их сейчас?';

  @override
  String get enable => 'Включить';

  @override
  String get cancel => 'Отмена';

  @override
  String get selectABusStop => 'Выберите автобусную остановку';

  @override
  String get busStop => 'Автобусная остановка';

  @override
  String get busStopNotFound => 'Автобусная остановка не найдена';

  @override
  String get busStopNotSelected => 'Автобусная остановка не выбрана';

  @override
  String get busStopSelected => 'Автобусная остановка выбрана';

  @override
  String get bussArrivals => 'Прибытие автобусов';

  @override
  String get now => 'Сейчас';

  @override
  String get min => 'мин';

  @override
  String get noBusArrivals => 'Нет прибывающих автобусов';

  @override
  String get noBusArrivalsMessage =>
      'В данный момент на этой остановке нет прибывающих автобусов.';

  @override
  String get scheduled => 'По расписанию';

  @override
  String get tapForBusArrivals => 'Нажмите для прибытия автобусов';

  @override
  String get live => 'ПРЯМОЙ ЭФИР';

  @override
  String get offline => 'НЕ В СЕТИ';

  @override
  String get stream_active => 'Трансляция активна';

  @override
  String get connection_failed => 'Ошибка соединения';

  @override
  String get real_time_video_feed => 'Видеопоток в реальном времени';

  @override
  String get unable_to_connect => 'Не удается подключиться к камере';

  @override
  String get camera_controls => 'Управление камерой';

  @override
  String get refresh => 'Обновить';

  @override
  String get full_screen => 'Полный экран';

  @override
  String get exit_full_screen => 'Выйти из полного экрана';

  @override
  String get loading_stream => 'Загрузка потока...';

  @override
  String get connection_failed_title => 'Ошибка соединения';

  @override
  String get tap_refresh_to_try_again =>
      'Нажмите обновить, чтобы попробовать снова';

  @override
  String get help_text =>
      'Это прямая трансляция из центра Ямбола. Используйте элементы управления выше для обновления.';

  @override
  String get yambol_live_camera => 'Прямая камера Ямбола';

  @override
  String get city_center => 'Центр города';

  @override
  String get live_stream => 'Прямая трансляция';

  @override
  String get blueZoneParkingMessage =>
      'Это зона синей парковки. Плата за парковку может взиматься в рабочее время.';

  @override
  String get freeParkingMessage =>
      'Это бесплатное парковочное место. Плата за парковку не взимается.';

  @override
  String get parkingDetails => 'Детали парковки';

  @override
  String get blueZone => 'Синяя зона';

  @override
  String get freeParking => 'Бесплатная парковка';

  @override
  String get parkingZone => 'Парковочная зона';

  @override
  String get parkingInformations => 'Информация о парковке';

  @override
  String get recentAnnouncements => 'Недавние объявления';

  @override
  String get ago => 'назад';

  @override
  String get h => 'ч';

  @override
  String get m => 'м';

  @override
  String get day => 'день';

  @override
  String get days => 'дней';

  @override
  String get hour => 'час';

  @override
  String get hours => 'часов';

  @override
  String get minute => 'минута';

  @override
  String get minutes => 'минут';

  @override
  String get markAllAsRead => 'Отметить все как прочитанные';

  @override
  String get loadingAnnouncements => 'Загрузка объявлений...';

  @override
  String get failedToLoadAnnouncements => 'Не удалось загрузить объявления';

  @override
  String get pleaseCheckYourConnection =>
      'Пожалуйста, проверьте ваше соединение и попробуйте снова.';

  @override
  String get noAnnouncementsMessage =>
      'У вас пока нет объявлений.\nПроверьте позже для обновлений.';

  @override
  String get nouAnnouncements => 'Нет объявлений';

  @override
  String get announcementHeaderMessage =>
      'Важные объявления от муниципалитета Ямбола';
}
