class BorderPoint {
  final int index;
  final double latitude;
  final double longitude;
  final int repairId;

  BorderPoint({
    required this.index,
    required this.latitude,
    required this.longitude,
    required this.repairId,
  });

  factory BorderPoint.fromJson(Map<String, dynamic> json) {
    return BorderPoint(
      index: json['index'] ?? 0,
      latitude: (json['latitude'] ?? 0).toDouble(),
      longitude: (json['longitude'] ?? 0).toDouble(),
      repairId: json['repairId'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'index': index,
      'latitude': latitude,
      'longitude': longitude,
      'repairId': repairId,
    };
  }

  @override
  String toString() {
    return 'BorderPoint(index: $index, latitude: $latitude, longitude: $longitude, repairId: $repairId)';
  }
}

class RepairModel {
  final int id;
  final String name;
  final String description;
  final double latitude;
  final double longitude;
  final DateTime startDate;
  final DateTime endDate;
  final List<BorderPoint> areaPoints; // Fixed: Proper type annotation

  RepairModel({
    required this.id,
    required this.name,
    required this.description,
    required this.latitude,
    required this.longitude,
    required this.startDate,
    required this.endDate,
    required this.areaPoints,
  });

  factory RepairModel.fromJson(Map<String, dynamic> json) {
    return RepairModel(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      latitude: (json['latitude'] ?? 0).toDouble(),
      longitude: (json['longitude'] ?? 0).toDouble(),
      startDate: json['startDate'] != null
          ? DateTime.parse(json['startDate'])
          : DateTime.now(),
      endDate: json['endDate'] != null
          ? DateTime.parse(json['endDate'])
          : DateTime.now(),
      // Fixed: Proper handling of areaPoints parsing
      areaPoints: (json['areaPoints'] as List<dynamic>?)
              ?.map((point) =>
                  BorderPoint.fromJson(point as Map<String, dynamic>))
              .toList() ??
          <BorderPoint>[],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'latitude': latitude,
      'longitude': longitude,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'areaPoints': areaPoints.map((point) => point.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'RepairModel(id: $id, name: $name, description: $description, latitude: $latitude, longitude: $longitude, startDate: $startDate, endDate: $endDate, areaPoints: $areaPoints)';
  }
}
