class EnumModel {
  final List<String> values;

  EnumModel({
    required this.values,
  });

  // Factory constructor to create EnumModel from a JSON array
  factory EnumModel.fromJson(List<dynamic> json) {
    return EnumModel(
      values: json.map((value) => value.toString()).toList(),
    );
  }

  // Method to convert EnumModel to a JSON array
  List<String> toJson() {
    return values;
  }

  // Helper methods
  bool contains(String value) => values.contains(value);
  String get first => values.isNotEmpty ? values.first : '';
  bool get isEmpty => values.isEmpty;
  int get length => values.length;

  @override
  String toString() => 'EnumModel(values: $values)';
}
