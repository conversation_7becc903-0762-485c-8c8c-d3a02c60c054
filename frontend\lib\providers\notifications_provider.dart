import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

part 'notifications_provider.g.dart';

@immutable
class NotificationSettings {
  final bool news;
  final bool events;
  final bool repairs;
  final bool announcements;
  final bool hasSystemPermission; // ✅ Add system permission tracking

  const NotificationSettings({
    required this.news,
    required this.events,
    required this.repairs,
    required this.announcements,
    required this.hasSystemPermission,
  });

  NotificationSettings copyWith({
    bool? news,
    bool? events,
    bool? repairs,
    bool? announcements,
    bool? hasSystemPermission,
  }) {
    return NotificationSettings(
      news: news ?? this.news,
      events: events ?? this.events,
      repairs: repairs ?? this.repairs,
      announcements: announcements ?? this.announcements,
      hasSystemPermission: hasSystemPermission ?? this.hasSystemPermission,
    );
  }
}

@riverpod
class NotificationNotifier extends _$NotificationNotifier {
  @override
  NotificationSettings build() {
    loadSavedNotifications();
    return const NotificationSettings(
      news: false,
      events: false,
      repairs: false,
      announcements: false,
      hasSystemPermission: false,
    );
  }

  Future<void> loadSavedNotifications() async {
    final prefs = await SharedPreferences.getInstance();
    final newsEnabled = prefs.getBool('notification_news') ?? false;
    final eventsEnabled = prefs.getBool('notification_events') ?? false;
    final repairsEnabled = prefs.getBool('notification_repairs') ?? false;
    final announcementsEnabled =
        prefs.getBool('notification_announcements') ?? false;

    // ✅ Check current system permission status
    final hasPermission = await _checkSystemPermission();

    state = NotificationSettings(
      news: newsEnabled,
      events: eventsEnabled,
      repairs: repairsEnabled,
      announcements: announcementsEnabled,
      hasSystemPermission: hasPermission,
    );
  }

  // ✅ Check if system-level permissions are granted
  Future<bool> _checkSystemPermission() async {
    try {
      final settings =
          await FirebaseMessaging.instance.getNotificationSettings();
      final isAuthorized =
          settings.authorizationStatus == AuthorizationStatus.authorized ||
              settings.authorizationStatus == AuthorizationStatus.provisional;

      print('🔐 System notification permission: $isAuthorized');
      return isAuthorized;
    } catch (e) {
      print('❌ Error checking system permission: $e');
      return false;
    }
  }

  // ✅ Request system permission and update state
  Future<bool> requestSystemPermission() async {
    try {
      print('🔔 Requesting system notification permission...');

      final messaging = FirebaseMessaging.instance;
      final settings = await messaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
      );

      final isGranted =
          settings.authorizationStatus == AuthorizationStatus.authorized ||
              settings.authorizationStatus == AuthorizationStatus.provisional;

      print('📱 System permission result: $isGranted');

      // Update state with new permission status
      state = state.copyWith(hasSystemPermission: isGranted);

      return isGranted;
    } catch (e) {
      print('❌ Error requesting system permission: $e');
      return false;
    }
  }

  Future<void> setNewsNotification(bool enabled) async {
    // ✅ Check system permission first
    if (enabled && !state.hasSystemPermission) {
      final permissionGranted = await requestSystemPermission();
      if (!permissionGranted) {
        print('⚠️ Cannot enable news notifications - system permission denied');
        return;
      }
    }

    // Update UI immediately
    state = state.copyWith(news: enabled);

    // Handle persistence and Firebase in background
    _handleNotificationChange('notification_news', 'news', enabled);
  }

  Future<void> setEventsNotification(bool enabled) async {
    // ✅ Check system permission first
    if (enabled && !state.hasSystemPermission) {
      final permissionGranted = await requestSystemPermission();
      if (!permissionGranted) {
        print(
            '⚠️ Cannot enable events notifications - system permission denied');
        return;
      }
    }

    // Update UI immediately
    state = state.copyWith(events: enabled);

    // Handle persistence and Firebase in background
    _handleNotificationChange('notification_events', 'events', enabled);
  }

  Future<void> setRepairsNotification(bool enabled) async {
    // ✅ Check system permission first
    if (enabled && !state.hasSystemPermission) {
      final permissionGranted = await requestSystemPermission();
      if (!permissionGranted) {
        print(
            '⚠️ Cannot enable repairs notifications - system permission denied');
        return;
      }
    }

    // Update UI immediately
    state = state.copyWith(repairs: enabled);

    // Handle persistence and Firebase in background
    _handleNotificationChange('notification_repairs', 'repairs', enabled);
  }

  Future<void> setAnnouncementsNotification(bool enabled) async {
    // ✅ Check system permission first
    if (enabled && !state.hasSystemPermission) {
      final permissionGranted = await requestSystemPermission();
      if (!permissionGranted) {
        print(
            '⚠️ Cannot enable announcements notifications - system permission denied');
        return;
      }
    }
    // Update UI immediately
    state = state.copyWith(announcements: enabled);

    // Handle persistence and Firebase in background
    _handleNotificationChange(
        'notification_announcements', 'announcements', enabled);
  }

  /// Background handler for persistence and Firebase operations
  void _handleNotificationChange(
      String prefKey, String topic, bool enabled) async {
    try {
      // Save to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(prefKey, enabled);

      // Subscribe/unsubscribe from Firebase topic
      if (enabled) {
        await FirebaseMessaging.instance.subscribeToTopic(topic);
        print('✅ Subscribed to $topic notifications');
      } else {
        await FirebaseMessaging.instance.unsubscribeFromTopic(topic);
        print('✅ Unsubscribed from $topic notifications');
      }
    } catch (e) {
      print('❌ Error setting $topic notification: $e');

      // If there's an error, revert the UI state
      switch (topic) {
        case 'news':
          state = state.copyWith(news: !enabled);
          break;
        case 'events':
          state = state.copyWith(events: !enabled);
          break;
        case 'repairs':
          state = state.copyWith(repairs: !enabled);
          break;
        case 'announcements':
          state = state.copyWith(announcements: !enabled);
          break;
      }
    }
  }

  Future<void> setAllNotifications(bool enabled) async {
    // ✅ Check system permission first if enabling
    if (enabled && !state.hasSystemPermission) {
      final permissionGranted = await requestSystemPermission();
      if (!permissionGranted) {
        print('⚠️ Cannot enable notifications - system permission denied');
        return;
      }
    }

    // Update UI immediately
    state = NotificationSettings(
      news: enabled,
      events: enabled,
      repairs: enabled,
      announcements: enabled,
      hasSystemPermission: state.hasSystemPermission,
    );

    // Handle all operations in background
    _handleAllNotificationsChange(enabled);
  }

  /// Background handler for bulk notification changes
  void _handleAllNotificationsChange(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Save all preferences in parallel
      await Future.wait([
        prefs.setBool('notification_news', enabled),
        prefs.setBool('notification_events', enabled),
        prefs.setBool('notification_repairs', enabled),
        prefs.setBool('notification_announcements', enabled),
      ]);

      // Subscribe/unsubscribe from all Firebase topics in parallel
      if (enabled) {
        await Future.wait([
          FirebaseMessaging.instance.subscribeToTopic('news'),
          FirebaseMessaging.instance.subscribeToTopic('events'),
          FirebaseMessaging.instance.subscribeToTopic('repairs'),
          FirebaseMessaging.instance.subscribeToTopic('announcements'),
        ]);
        print('✅ Subscribed to all notifications');
      } else {
        await Future.wait([
          FirebaseMessaging.instance.unsubscribeFromTopic('news'),
          FirebaseMessaging.instance.unsubscribeFromTopic('events'),
          FirebaseMessaging.instance.unsubscribeFromTopic('repairs'),
          FirebaseMessaging.instance.unsubscribeFromTopic('announcements'),
        ]);
        print('✅ Unsubscribed from all notifications');
      }
    } catch (e) {
      print('❌ Error setting all notifications: $e');

      // If there's an error, revert to previous state
      await loadSavedNotifications();
    }
  }

  /// Handle initial permission modal response
  Future<void> handleInitialPermission(bool userWantsNotifications) async {
    if (userWantsNotifications) {
      // User wants notifications - enable all and subscribe to topics
      await setAllNotifications(true);
    } else {
      // User doesn't want notifications - disable all and unsubscribe from topics
      await setAllNotifications(false);
    }
  }

  /// ✅ Refresh system permission status (call when app comes to foreground)
  Future<void> refreshSystemPermissionStatus() async {
    final hasPermission = await _checkSystemPermission();
    state = state.copyWith(hasSystemPermission: hasPermission);
  }
}
