import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:frontend/l10n/generated/l10n.dart';
import 'package:frontend/models/get_interest_points_command.dart';
import 'package:frontend/presentation/widgets/html_utils.dart';
import 'package:frontend/providers/interest_points_details.provider.dart';
import 'package:frontend/providers/interest_points_provider.dart';
import 'package:frontend/providers/theme_provider.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart' as gmaps;
import 'package:flutter/services.dart' show rootBundle;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_svg/svg.dart' as svg;
import 'dart:ui' as ui;
import 'package:url_launcher/url_launcher.dart';
import 'package:geolocator/geolocator.dart';

class ReusableMapWidget extends ConsumerStatefulWidget {
  final Map<String, List<String>>? filterGroups;
  final Set<String>? initialSelectedFilters;
  final List<String>? fixedCategories;
  final bool showFilters;
  final String? title;

  const ReusableMapWidget({
    super.key,
    this.filterGroups,
    this.initialSelectedFilters,
    this.fixedCategories,
    this.showFilters = false,
    this.title,
  });

  @override
  _ReusableMapWidgetState createState() => _ReusableMapWidgetState();
}

class _ReusableMapWidgetState extends ConsumerState<ReusableMapWidget> {
  gmaps.GoogleMapController? _mapController;
  final gmaps.LatLng _defaultLocation = const gmaps.LatLng(42.4842, 26.5035);
  gmaps.LatLng? _userLocation; // Real user location
  Set<gmaps.Marker> _markers = {};
  String? _mapStyle;
  bool _isMapStyleLoaded = false;
  bool _areIconsLoaded = false;

  // Dynamic filter state
  Set<String> _selectedFilters = {};

  // All available categories
  List<String> get _allCategories {
    if (widget.fixedCategories != null) {
      return widget.fixedCategories!;
    }
    if (widget.filterGroups != null) {
      return widget.filterGroups!.values
          .expand((categories) => categories)
          .toList();
    }
    return [];
  }

  // Currently active categories based on selected filters or fixed categories
  List<String> get _activeCategories {
    if (widget.fixedCategories != null) {
      return widget.fixedCategories!;
    }
    if (widget.filterGroups != null) {
      List<String> active = [];
      for (String filter in _selectedFilters) {
        if (widget.filterGroups!.containsKey(filter)) {
          active.addAll(widget.filterGroups![filter]!);
        }
      }
      return active;
    }
    return [];
  }

  // Comprehensive icon mapping for all categories
  final Map<String, String> _categoryIconPaths = {
    // Tourism categories
    'Landmark': 'assets/icons/landmark-icon.svg',
    'LegendsAndMyths': 'assets/icons/legends-and-myths-icon.svg',
    'Culture': 'assets/icons/culture-icon.svg',
    'Hiking': 'assets/icons/hiking-icon.svg',
    'Family': 'assets/icons/family-icon.svg',
    'NightLife': 'assets/icons/night-life-icon.svg',
    'Transport': 'assets/icons/transport-icon.svg',
    'TouristAgency': 'assets/icons/travel-agency-icon.svg',

    // Business categories (using actual file names)
    'Restaurant': 'assets/icons/restaurants-icon.svg',
    'Coffee': 'assets/icons/coffe-icon.svg',
    'Bars': 'assets/icons/bar-icon.svg',
    'PastryShop': 'assets/icons/pastry-shop-icon.svg',
    'GasStation': 'assets/icons/gas-station-icon.svg',
    'Shop': 'assets/icons/shop-icon.svg',
    'Hotel': 'assets/icons/hotel-icon.svg',
    'GuestHouse': 'assets/icons/guest-house-icon.svg',
    'Bank': 'assets/icons/bank-icon.svg',
    'CurrencyChange': 'assets/icons/currency-change-icon.svg',
    'InsuranceCompany': 'assets/icons/insurance-icon.svg',
    'Atm': 'assets/icons/atm-icon.svg',
    'BioShop': 'assets/icons/bio-shop-icon.svg',
    'Farm': 'assets/icons/farm-icon.svg',
    'RecycleCenter': 'assets/icons/recycle-icon.svg',
    'EcologyInitiative': 'assets/icons/eco-icon.svg',

    // Cultural & Entertainment
    'Museum': 'assets/icons/museum-icon.svg',
    'Theatre': 'assets/icons/theaters-icon.svg',
    'Gallery': 'assets/icons/gallery-icon.svg',

    // Healthcare
    'Pharmacy': 'assets/icons/pharmacy-icon.svg',
    'MedicalEstablishment': 'assets/icons/medical-establishment-icon.svg',
    'DoctorsOffice': 'assets/icons/doctors-office-icon.svg',
    'MedicalLab': 'assets/icons/medical-lab-icon.svg',
    'Veterinary': 'assets/icons/veterinary-icon.svg',

    // Education
    'Kindergarden': 'assets/icons/kindergarden-icon.svg',
    'Nursery': 'assets/icons/nursery-icon.svg',
    'ChildNutritionCenter': 'assets/icons/child-nutrition-icon.svg',
    'School': 'assets/icons/school-icon.svg',
    'University': 'assets/icons/university-icon.svg',
    'DevelopmentCenter': 'assets/icons/development-icon.svg',

    // Sports
    'SportsFacility': 'assets/icons/sport-facility-icon.svg',
  };

  // Map of category to icon
  final Map<String, gmaps.BitmapDescriptor> _categoryIcons = {};

  @override
  void initState() {
    super.initState();

    // Initialize selected filters
    if (widget.initialSelectedFilters != null) {
      _selectedFilters = Set.from(widget.initialSelectedFilters!);
    } else if (widget.filterGroups != null) {
      _selectedFilters = Set.from(widget.filterGroups!.keys);
    }

    _loadCustomMarkerIcons();
    _getCurrentLocation();

    // Set initial categories
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        ref
            .read(interestPointFiltersProvider.notifier)
            .setCategories(_activeCategories);
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (mounted) {
      _loadMapStyle();
    }
  }

  // Get user's current location
  Future<void> _getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        print('Location services are disabled.');
        return;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          print('Location permissions are denied');
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        print('Location permissions are permanently denied.');
        return;
      }

      Position position = await Geolocator.getCurrentPosition();
      if (mounted) {
        setState(() {
          _userLocation = gmaps.LatLng(position.latitude, position.longitude);
        });

        // Move camera to user location
        if (_mapController != null) {
          _mapController!.animateCamera(
            gmaps.CameraUpdate.newLatLng(_userLocation!),
          );
        }
      }
    } catch (e) {
      print('Error getting location: $e');
    }
  }

  // Simplified navigation - just open external maps
  Future<void> _openNavigation(InterestPointsGetModel place) async {
    if (place.latitude == null || place.longitude == null) return;

    final double lat = place.latitude!;
    final double lng = place.longitude!;

    // Create Google Maps URL with navigation
    final String url =
        'https://www.google.com/maps/dir/?api=1&destination=$lat,$lng';

    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        throw 'Could not launch navigation';
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Could not open navigation: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadCustomMarkerIcons() async {
    for (var category in _allCategories) {
      if (!mounted) return;
      try {
        final iconPath = _categoryIconPaths[category];
        if (iconPath != null) {
          final gmaps.BitmapDescriptor? customIcon =
              await _loadSvgMarker(iconPath);
          if (customIcon != null) {
            _categoryIcons[category] = customIcon;
          } else {
            _categoryIcons[category] = gmaps.BitmapDescriptor.defaultMarker;
          }
        } else {
          _categoryIcons[category] = gmaps.BitmapDescriptor.defaultMarker;
        }
      } catch (e) {
        print("Error loading icon for $category: $e");
        _categoryIcons[category] = gmaps.BitmapDescriptor.defaultMarker;
      }
    }

    if (mounted) {
      setState(() {
        _areIconsLoaded = true;
      });
    }
  }

  Future<gmaps.BitmapDescriptor?> _loadSvgMarker(String iconPath) async {
    if (!mounted) return null;

    try {
      final svgString = await rootBundle.loadString(iconPath);
      if (!mounted) return null;

      final pictureInfo =
          await svg.vg.loadPicture(SvgStringLoader(svgString), null);
      if (!mounted) return null;

      final ui.PictureRecorder recorder = ui.PictureRecorder();
      final Canvas canvas = Canvas(recorder);

      final double logicalSize = 45.0;
      final double pixelRatio = MediaQuery.of(context).devicePixelRatio;
      final double bitmapWidth = logicalSize * pixelRatio;
      final double bitmapHeight = logicalSize * pixelRatio;

      final double scaleX = bitmapWidth / pictureInfo.size.width;
      final double scaleY = bitmapHeight / pictureInfo.size.height;
      final double scale = scaleX < scaleY ? scaleX : scaleY;
      final double adjustedScale = scale / pixelRatio;

      final double translateX =
          (logicalSize - (pictureInfo.size.width * adjustedScale)) /
              2 *
              pixelRatio;
      final double translateY =
          (logicalSize - (pictureInfo.size.height * adjustedScale)) /
              2 *
              pixelRatio;

      canvas.translate(translateX, translateY);
      canvas.scale(adjustedScale * pixelRatio);
      canvas.drawPicture(pictureInfo.picture);

      final ui.Image image = await recorder.endRecording().toImage(
            bitmapWidth.toInt(),
            bitmapHeight.toInt(),
          );
      if (!mounted) return null;

      final ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);
      if (!mounted) return null;

      if (byteData != null) {
        final Uint8List uint8List = byteData.buffer.asUint8List();
        return gmaps.BitmapDescriptor.fromBytes(uint8List);
      }
    } catch (e) {
      print("Error loading SVG marker $iconPath: $e");
    }

    return null;
  }

  Future<void> _loadMapStyle() async {
    if (!mounted) return;

    final themeMode = ref.read(themeNotifierProvider);
    final isDarkMode = themeMode == ThemeMode.dark;

    try {
      if (isDarkMode) {
        _mapStyle =
            await rootBundle.loadString('assets/map_styles/dark_map.json');
      } else {
        _mapStyle =
            await rootBundle.loadString('assets/map_styles/light_map.json');
      }

      if (mounted) {
        setState(() {
          _isMapStyleLoaded = true;
        });
      }
    } catch (e) {
      print("Error loading map style: $e");
    }
  }

  void _updateMarkers(List<InterestPointsGetModel> places) {
    if (!_areIconsLoaded || !mounted) {
      return;
    }

    Set<gmaps.Marker> loadedMarkers = {};

    // Filter places based on active categories
    final filteredPlaces = places
        .where((place) => _activeCategories.contains(place.category))
        .toList();

    for (var place in filteredPlaces) {
      if (place.latitude != null && place.longitude != null) {
        gmaps.BitmapDescriptor markerIcon = _categoryIcons[place.category] ??
            gmaps.BitmapDescriptor.defaultMarker;

        gmaps.Marker marker = gmaps.Marker(
          markerId: gmaps.MarkerId(place.id.toString()),
          position: gmaps.LatLng(place.latitude!, place.longitude!),
          icon: markerIcon,
          onTap: () {
            if (mounted) {
              _showInterestPointDialog(place);
            }
          },
        );

        loadedMarkers.add(marker);
      }
    }

    if (mounted) {
      setState(() => _markers = loadedMarkers);
    }
  }

  void _toggleFilter(String filter) {
    setState(() {
      if (_selectedFilters.contains(filter)) {
        _selectedFilters.remove(filter);
      } else {
        _selectedFilters.add(filter);
      }
    });

    // Update the provider with new categories
    ref
        .read(interestPointFiltersProvider.notifier)
        .setCategories(_activeCategories);
  }

  String _getTranslatedFilterName(String filterKey) {
    switch (filterKey.toLowerCase()) {
      case 'business':
        return S.of(context).business;
      case 'landmarks':
        return S.of(context).landmarks;
      case 'healthcare':
        return S.of(context).healthcare;
      case 'education':
        return S.of(context).education;
      case 'sports':
        return S.of(context).sports;
      default:
        return filterKey; // Fallback to original key if no translation found
    }
  }

  void _showFilterBottomSheet() {
    if (!widget.showFilters || widget.filterGroups == null) return;

    final themeMode = ref.read(themeNotifierProvider);
    final isDarkMode = themeMode == ThemeMode.dark;

    // Check if device is in landscape mode
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    if (isLandscape) {
      // Use a dialog for landscape mode to utilize the wider screen better
      showDialog(
        context: context,
        builder: (context) {
          return StatefulBuilder(
            builder: (context, setModalState) {
              return Dialog(
                backgroundColor:
                    isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Container(
                  width: screenWidth * 0.8, // Use 80% of screen width
                  height: screenHeight * 0.7, // Use 70% of screen height
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            S.of(context).filterCategories,
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: isDarkMode ? Colors.white : Colors.black87,
                            ),
                          ),
                          IconButton(
                            onPressed: () => Navigator.pop(context),
                            icon: Icon(
                              Icons.close,
                              color:
                                  isDarkMode ? Colors.white70 : Colors.black54,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Filter chips in a grid layout for landscape
                      Expanded(
                        child: GridView.builder(
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 3, // 3 columns in landscape
                            childAspectRatio: 3.0, // Wider chips
                            crossAxisSpacing: 8,
                            mainAxisSpacing: 8,
                          ),
                          itemCount: widget.filterGroups!.keys.length,
                          itemBuilder: (context, index) {
                            final filter =
                                widget.filterGroups!.keys.elementAt(index);
                            final isSelected =
                                _selectedFilters.contains(filter);

                            return FilterChip(
                              label: Text(
                                _getTranslatedFilterName(filter),
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: isSelected
                                      ? Colors.white
                                      : (isDarkMode
                                          ? Colors.white70
                                          : Colors.black87),
                                  fontSize:
                                      12, // Slightly smaller text for grid
                                ),
                              ),
                              selected: isSelected,
                              onSelected: (selected) {
                                setModalState(() {
                                  _toggleFilter(filter);
                                });
                                setState(() {}); // Update main screen
                              },
                              backgroundColor: isDarkMode
                                  ? const Color(0xFF2A2A2A)
                                  : Colors.grey[200],
                              selectedColor: const Color(0xFF22D400),
                              checkmarkColor: Colors.white,
                              materialTapTargetSize:
                                  MaterialTapTargetSize.shrinkWrap,
                            );
                          },
                        ),
                      ),

                      const SizedBox(height: 20),

                      // Apply button
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () => Navigator.pop(context),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF22D400),
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: Text(S.of(context).applyFilters),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      );
    } else {
      // Use bottom sheet for portrait mode (original implementation)
      showModalBottomSheet(
        context: context,
        backgroundColor: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        isScrollControlled: true, // Allow the sheet to be scrollable
        builder: (context) {
          return StatefulBuilder(
            builder: (context, setModalState) {
              return DraggableScrollableSheet(
                initialChildSize: 0.4, // Start at 40% of screen height
                minChildSize: 0.3, // Minimum 30%
                maxChildSize: 0.8, // Maximum 80%
                expand: false,
                builder: (context, scrollController) {
                  return Container(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              S.of(context).filterCategories,
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color:
                                    isDarkMode ? Colors.white : Colors.black87,
                              ),
                            ),
                            IconButton(
                              onPressed: () => Navigator.pop(context),
                              icon: Icon(
                                Icons.close,
                                color: isDarkMode
                                    ? Colors.white70
                                    : Colors.black54,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        // Filter chips in a scrollable view for portrait
                        // Replace the existing filter chips section in your _showFilterBottomSheet method
// In the portrait mode bottom sheet, replace the Wrap widget section with this:

// Filter chips in a scrollable view for portrait
                        Expanded(
                          child: SingleChildScrollView(
                            controller: scrollController,
                            child: LayoutBuilder(
                              builder: (context, constraints) {
                                return Wrap(
                                  spacing: 8,
                                  runSpacing: 8,
                                  children:
                                      widget.filterGroups!.keys.map((filter) {
                                    final isSelected =
                                        _selectedFilters.contains(filter);
                                    final translatedName =
                                        _getTranslatedFilterName(filter);

                                    return ConstrainedBox(
                                      constraints: BoxConstraints(
                                        maxWidth: constraints.maxWidth -
                                            16, // Leave some margin
                                        minWidth:
                                            80, // Minimum width for readability
                                      ),
                                      child: FilterChip(
                                        label: Text(
                                          translatedName,
                                          overflow: TextOverflow.ellipsis,
                                          maxLines: 1,
                                        ),
                                        selected: isSelected,
                                        onSelected: (selected) {
                                          setModalState(() {
                                            _toggleFilter(filter);
                                          });
                                          setState(() {}); // Update main screen
                                        },
                                        backgroundColor: isDarkMode
                                            ? const Color(0xFF2A2A2A)
                                            : Colors.grey[200],
                                        selectedColor: const Color(0xFF22D400),
                                        labelStyle: TextStyle(
                                          color: isSelected
                                              ? Colors.white
                                              : (isDarkMode
                                                  ? Colors.white70
                                                  : Colors.black87),
                                          fontSize:
                                              14, // Slightly smaller font to prevent overflow
                                        ),
                                        checkmarkColor: Colors.white,
                                        materialTapTargetSize:
                                            MaterialTapTargetSize.shrinkWrap,
                                        visualDensity: VisualDensity.compact,
                                      ),
                                    );
                                  }).toList(),
                                );
                              },
                            ),
                          ),
                        ),

                        const SizedBox(height: 20),

                        // Apply button
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: () => Navigator.pop(context),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF22D400),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text(S.of(context).applyFilters),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              );
            },
          );
        },
      );
    }
  }

  void _showInterestPointDialog(InterestPointsGetModel place) {
    final themeMode = ref.read(themeNotifierProvider);
    final isDarkMode = themeMode == ThemeMode.dark;

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return Consumer(
          builder: (context, ref, _) {
            final asyncDetails = ref.watch(fetchInterestPointDetailsProvider(
              place.id,
              Localizations.localeOf(context).languageCode,
            ));

            return Dialog(
              backgroundColor:
                  isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Container(
                constraints: const BoxConstraints(
                  maxWidth: 400,
                  maxHeight: 600,
                ),
                child: asyncDetails.when(
                  data: (details) {
                    final List<String> imageUrls = [];
                    if (details.images != null && details.images!.isNotEmpty) {
                      var coverImage = details.images!.firstWhere(
                        (image) => image.isCover == true,
                        orElse: () => details.images!.first,
                      );

                      if (coverImage.preSignedUrl != null) {
                        imageUrls.add(coverImage.preSignedUrl!);
                      }

                      imageUrls.addAll(details.images!
                          .where((image) =>
                              image.isCover != true &&
                              image.preSignedUrl != null)
                          .map((image) => image.preSignedUrl!));
                    }

                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header with close button
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: isDarkMode
                                ? const Color(0xFF2A2A2A)
                                : const Color(0xFFF5F5F5),
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(16),
                              topRight: Radius.circular(16),
                            ),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  details.name ?? 'Unknown Place',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: isDarkMode
                                        ? Colors.white
                                        : const Color(0xFF424242),
                                  ),
                                ),
                              ),
                              IconButton(
                                onPressed: () =>
                                    Navigator.of(dialogContext).pop(),
                                icon: Icon(
                                  Icons.close,
                                  color: isDarkMode
                                      ? Colors.white70
                                      : Colors.black54,
                                ),
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(),
                              ),
                            ],
                          ),
                        ),

                        // Content
                        Flexible(
                          child: SingleChildScrollView(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Images section
                                if (imageUrls.isNotEmpty) ...[
                                  Container(
                                    height: 200,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(12),
                                      color: isDarkMode
                                          ? const Color(0xFF2A2A2A)
                                          : const Color(0xFFF5F5F5),
                                    ),
                                    child: PageView.builder(
                                      itemCount: imageUrls.length,
                                      itemBuilder: (context, index) {
                                        return Container(
                                          margin: const EdgeInsets.symmetric(
                                              horizontal: 4),
                                          child: ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(12),
                                            child: Image.network(
                                              imageUrls[index],
                                              fit: BoxFit.cover,
                                              width: double.infinity,
                                              errorBuilder:
                                                  (context, error, stackTrace) {
                                                return Container(
                                                  decoration: BoxDecoration(
                                                    color: isDarkMode
                                                        ? const Color(
                                                            0xFF2A2A2A)
                                                        : const Color(
                                                            0xFFF5F5F5),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            12),
                                                  ),
                                                  child: Icon(
                                                    Icons.broken_image,
                                                    size: 48,
                                                    color: isDarkMode
                                                        ? Colors.white38
                                                        : Colors.black38,
                                                  ),
                                                );
                                              },
                                              loadingBuilder: (context, child,
                                                  loadingProgress) {
                                                if (loadingProgress == null) {
                                                  return child;
                                                }
                                                return Container(
                                                  decoration: BoxDecoration(
                                                    color: isDarkMode
                                                        ? const Color(
                                                            0xFF2A2A2A)
                                                        : const Color(
                                                            0xFFF5F5F5),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            12),
                                                  ),
                                                  child: Center(
                                                    child:
                                                        CircularProgressIndicator(
                                                      value: loadingProgress
                                                                  .expectedTotalBytes !=
                                                              null
                                                          ? loadingProgress
                                                                  .cumulativeBytesLoaded /
                                                              loadingProgress
                                                                  .expectedTotalBytes!
                                                          : null,
                                                      strokeWidth: 2,
                                                    ),
                                                  ),
                                                );
                                              },
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                  const SizedBox(height: 16),
                                ],
                                // Description
                                Text(
                                  S.of(context).description,
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: isDarkMode
                                        ? Colors.white
                                        : const Color(0xFF424242),
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: isDarkMode
                                        ? const Color(0xFF2A2A2A)
                                        : const Color(0xFFF8F9FA),
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: isDarkMode
                                          ? const Color(0xFF3A3A3A)
                                          : const Color(0xFFE0E0E0),
                                    ),
                                  ),
                                  child: HtmlUtils.createStyledHtmlWidget(
                                    htmlContent: details.description ??
                                        "No description available.",
                                    isDarkMode: isDarkMode,
                                    baseFontSize: 14.0,
                                    onLinkTap: (url, attributes, element) {
                                      print('Link tapped: $url');
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        // Simplified Action buttons
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: isDarkMode
                                ? const Color(0xFF2A2A2A)
                                : const Color(0xFFF5F5F5),
                            borderRadius: const BorderRadius.only(
                              bottomLeft: Radius.circular(16),
                              bottomRight: Radius.circular(16),
                            ),
                          ),
                          child: Row(
                            children: [
                              // Navigate button
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: () {
                                    Navigator.of(dialogContext).pop();
                                    _openNavigation(place);
                                  },
                                  icon: const Icon(
                                    Icons.navigation,
                                    size: 18,
                                    color: Colors.white,
                                  ),
                                  label: Text(S.of(context).navigate),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: const Color(
                                        0xFF22D400), // Changed to green
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 12),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                ),
                              ),

                              const SizedBox(width: 12),

                              // Close button
                              TextButton(
                                onPressed: () =>
                                    Navigator.of(dialogContext).pop(),
                                style: TextButton.styleFrom(
                                  foregroundColor: isDarkMode
                                      ? Colors.white70
                                      : Colors.black54,
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 12, horizontal: 16),
                                ),
                                child: Text(S.of(context).close),
                              ),
                            ],
                          ),
                        ),
                      ],
                    );
                  },
                  loading: () => Container(
                    height: 200,
                    child: const Center(
                      child: CircularProgressIndicator(),
                    ),
                  ),
                  error: (err, stack) => Container(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 48,
                          color: isDarkMode ? Colors.red[300] : Colors.red[600],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Error loading details',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: isDarkMode
                                ? Colors.white
                                : const Color(0xFF424242),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          err.toString(),
                          style: TextStyle(
                            fontSize: 14,
                            color: isDarkMode ? Colors.white70 : Colors.black54,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        TextButton(
                          onPressed: () => Navigator.of(dialogContext).pop(),
                          child: Text(S.of(context).close),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  void _onMapCreated(gmaps.GoogleMapController controller) {
    _mapController = controller;
  }

  @override
  Widget build(BuildContext context) {
    final themeMode = ref.watch(themeNotifierProvider);
    final isDarkMode = themeMode == ThemeMode.dark;

    final tourismPlaces = ref.watch(fetchInterestPointsProvider);

    if (_isMapStyleLoaded && _mapController != null) {
      final currentThemeMode = ref.read(themeNotifierProvider);
      final currentIsDarkMode = currentThemeMode == ThemeMode.dark;
      if (currentIsDarkMode != isDarkMode) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            _loadMapStyle();
          }
        });
      }
    }

    return Stack(
      children: [
        // Map
        tourismPlaces.when(
          data: (places) {
            print(
                'Fetched ${places.length} places for ${widget.title ?? "Map"}');
            if (mounted) {
              _updateMarkers(places);
            }
            return gmaps.GoogleMap(
              onMapCreated: _onMapCreated,
              initialCameraPosition: gmaps.CameraPosition(
                target: _userLocation ?? _defaultLocation,
                zoom: 14.0,
              ),
              markers: _markers,
              mapType: gmaps.MapType.normal,
              myLocationEnabled: true,
              myLocationButtonEnabled: true,
              compassEnabled: true,
              zoomControlsEnabled: true,
              tiltGesturesEnabled: true,
              mapToolbarEnabled: false,
              style: _mapStyle,
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (err, stack) => Center(
            child: Text('Error loading map data: $err'),
          ),
        ),

        // Filter button (only show if filters are enabled)
        if (widget.showFilters && widget.filterGroups != null)
          Positioned(
            top: MediaQuery.of(context).padding.top + 16,
            right: 16,
            child: FloatingActionButton(
              onPressed: _showFilterBottomSheet,
              backgroundColor:
                  isDarkMode ? const Color(0xFF2A2A2A) : Colors.white,
              foregroundColor: isDarkMode ? Colors.white : Colors.black87,
              mini: true,
              child: const Icon(Icons.filter_list),
            ),
          ),

        // Selected filters indicator (only show if filters are enabled)
        // Replace the existing "Selected filters indicator" section with this:
        if (widget.showFilters && _selectedFilters.isNotEmpty)
          Positioned(
            top: MediaQuery.of(context).padding.top + 70,
            left: 16,
            right: 16,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: isDarkMode
                    ? const Color(0xFF2A2A2A).withOpacity(0.9)
                    : Colors.white.withOpacity(0.9),
                borderRadius: BorderRadius.circular(25),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 6,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: _selectedFilters.map((filter) {
                    return Container(
                      margin: const EdgeInsets.only(right: 10),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        color: const Color(0xFF22D400),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Text(
                        _getTranslatedFilterName(filter), // Use translated name
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
          ),
      ],
    );
  }

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }
}
