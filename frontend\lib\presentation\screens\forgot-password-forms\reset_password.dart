import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/l10n/generated/l10n.dart';
import 'package:frontend/providers/reset_password_provider.dart';
import 'package:go_router/go_router.dart';

class ResetPasswordScreen extends ConsumerStatefulWidget {
  final String id;
  final String email;
  final String expires;
  final String signature;

  const ResetPasswordScreen({
    super.key,
    required this.id,
    required this.email,
    required this.expires,
    required this.signature,
  });

  @override
  ConsumerState<ResetPasswordScreen> createState() =>
      _ResetPasswordScreenState();
}

class _ResetPasswordScreenState extends ConsumerState<ResetPasswordScreen> {
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  @override
  void initState() {
    super.initState();
    _validateResetLink();
  }

  @override
  void dispose() {
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  void _validateResetLink() {
    // Check if the reset link has expired
    try {
      final expiryDate = DateTime.parse(widget.expires);
      if (DateTime.now().isAfter(expiryDate)) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _showExpiredLinkDialog();
        });
      }
    } catch (e) {
      print('Error parsing expiry date: $e');
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _showInvalidLinkDialog();
      });
    }
  }

  void _showExpiredLinkDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text(S.of(context).linkExpired),
        content: Text(S.of(context).resetLinkExpiredMessage),
        actions: [
          TextButton(
            onPressed: () {
              context.push('/forgot-password');
            },
            child: Text(S.of(context).requestNewLink),
          ),
        ],
      ),
    );
  }

  void _showInvalidLinkDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text(S.of(context).invalidLink),
        content: Text(S.of(context).invalidLinkMessage),
        actions: [
          TextButton(
            onPressed: () {
              context.go('/forgot-password');
            },
            child: Text(S.of(context).requestNewLink),
          ),
        ],
      ),
    );
  }

  void _onResetPasswordPressed() async {
    if (_formKey.currentState!.validate()) {
      print('🔄 Resetting password for: ${widget.email}');

      // Send reset password request using provider
      await ref.read(resetPasswordStateProvider.notifier).resetPassword(
            id: widget.id,
            email: widget.email,
            expires: widget.expires,
            signature: widget.signature,
            newPassword: _passwordController.text.trim(),
            confirmPassword: _confirmPasswordController.text.trim(),
          );
    }
  }

  String? _validatePassword(String? value) {
    if (value == null || value.trim().isEmpty) {
      return S.of(context).passwordRequired;
    }

    if (value.length < 8) {
      return S.of(context).passwordTooShort;
    }

    // Add more password validation rules as needed
    if (!RegExp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)').hasMatch(value)) {
      return S.of(context).passwordRequirements;
    }

    return null;
  }

  String? _validateConfirmPassword(String? value) {
    if (value == null || value.trim().isEmpty) {
      return S.of(context).confirmPasswordRequired;
    }

    if (value != _passwordController.text) {
      return S.of(context).passwordsDoNotMatch;
    }

    return null;
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Listen to reset password state changes
    ref.listen<AsyncValue<bool>>(resetPasswordStateProvider, (previous, next) {
      next.when(
        data: (success) {
          if (success) {
            // Show success message
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(S.of(context).passwordResetSuccess),
                backgroundColor: Colors.green,
                behavior: SnackBarBehavior.floating,
                duration: const Duration(seconds: 4),
              ),
            );

            // Reset provider state and navigate to login
            ref.read(resetPasswordStateProvider.notifier).reset();
            context.pushReplacement('/login', extra: {'showBackButton': false});
          }
        },
        loading: () {
          // Loading state is handled by watching the provider below
        },
        error: (error, stack) {
          print('❌ Reset password error: $error');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(S.of(context).passwordResetError),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
              duration: const Duration(seconds: 4),
            ),
          );
        },
      );
    });

    // Watch the reset password state for loading indicator
    final resetPasswordState = ref.watch(resetPasswordStateProvider);
    final isLoading = resetPasswordState is AsyncLoading;

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
        title: Text(
          S.of(context).resetPassword,
          style: TextStyle(
            color: isDarkMode ? Colors.white : Colors.black87,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: Stack(
        children: [
          // Background Image with Opacity (same as login screen)
          SizedBox(
            height: MediaQuery.of(context).size.height,
            width: MediaQuery.of(context).size.width,
            child: Opacity(
              opacity: 0.35,
              child: Image.asset(
                'assets/images/login_background.png',
                fit: BoxFit.cover,
                height: double.infinity,
                width: double.infinity,
              ),
            ),
          ),
          // Main Content
          SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(height: 80),

                    // Reset Password Icon
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.green.withOpacity(0.1),
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.green.withOpacity(0.3),
                          width: 2,
                        ),
                      ),
                      child: Icon(
                        Icons.lock_reset,
                        size: 60,
                        color: Colors.green,
                      ),
                    ),

                    const SizedBox(height: 32),

                    // Title and Description
                    Text(
                      S.of(context).createNewPassword,
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: isDarkMode ? Colors.white : Colors.black87,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 16),

                    Text(
                      S.of(context).enterNewPasswordMessage,
                      style: TextStyle(
                        fontSize: 16,
                        color: isDarkMode
                            ? Colors.grey.shade300
                            : Colors.grey.shade600,
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 16),

                    // Email display
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        color: Colors.green.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border:
                            Border.all(color: Colors.green.withOpacity(0.3)),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.email_outlined,
                              color: Colors.green, size: 16),
                          const SizedBox(width: 8),
                          Text(
                            widget.email,
                            style: TextStyle(
                              color: Colors.green,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 32),

                    // New Password Input
                    _buildPasswordField(
                      context,
                      controller: _passwordController,
                      hint: S.of(context).newPassword,
                      obscureText: _obscurePassword,
                      onToggleVisibility: () {
                        setState(() {
                          _obscurePassword = !_obscurePassword;
                        });
                      },
                      validator: _validatePassword,
                    ),

                    const SizedBox(height: 16),

                    // Confirm Password Input
                    _buildPasswordField(
                      context,
                      controller: _confirmPasswordController,
                      hint: S.of(context).confirmNewPassword,
                      obscureText: _obscureConfirmPassword,
                      onToggleVisibility: () {
                        setState(() {
                          _obscureConfirmPassword = !_obscureConfirmPassword;
                        });
                      },
                      validator: _validateConfirmPassword,
                    ),

                    const SizedBox(height: 32),

                    // Reset Password Button
                    SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: ElevatedButton(
                        onPressed: isLoading ? null : _onResetPasswordPressed,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          elevation: 2,
                        ),
                        child: isLoading
                            ? const CircularProgressIndicator(
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              )
                            : Text(
                                S.of(context).resetPasswordButton,
                                style: const TextStyle(
                                  fontSize: 16,
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Password input field with toggle visibility
  Widget _buildPasswordField(
    BuildContext context, {
    required TextEditingController controller,
    required String hint,
    required bool obscureText,
    required VoidCallback onToggleVisibility,
    required String? Function(String?) validator,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return TextFormField(
      controller: controller,
      obscureText: obscureText,
      validator: validator,
      style: TextStyle(
        color: isDarkMode ? Colors.white : Colors.black87,
        fontSize: 16,
      ),
      decoration: InputDecoration(
        hintText: hint,
        hintStyle: TextStyle(
          color: isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
          fontSize: 16,
        ),
        filled: true,
        fillColor: isDarkMode
            ? Colors.grey.shade800.withOpacity(0.8)
            : Colors.white.withOpacity(0.9),
        prefixIcon: Icon(
          Icons.lock_outline,
          color: isDarkMode ? Colors.grey.shade300 : Colors.black54,
        ),
        suffixIcon: IconButton(
          icon: Icon(
            obscureText ? Icons.visibility_off : Icons.visibility,
            color: isDarkMode ? Colors.grey.shade300 : Colors.black54,
          ),
          onPressed: onToggleVisibility,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: isDarkMode ? Colors.grey.shade600 : Colors.grey.shade300,
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: isDarkMode ? Colors.grey.shade600 : Colors.grey.shade300,
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: Colors.green,
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: Colors.red,
            width: 1,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: Colors.red,
            width: 2,
          ),
        ),
      ),
    );
  }
}
