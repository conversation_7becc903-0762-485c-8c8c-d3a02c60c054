class FileModel {
  final String fileName;
  final String contentType;
  final String extension;
  final String content;

  FileModel({
    required this.fileName,
    required this.contentType,
    required this.extension,
    required this.content,
  });

  // Factory constructor to create a FileModel from a JSON map
  factory FileModel.fromJson(Map<String, dynamic> json) {
    return FileModel(
      fileName: json['fileName'],
      contentType: json['contentType'],
      extension: json['extension'],
      content: json['content'],
    );
  }

  // Method to convert a FileModel to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'fileName': fileName,
      'contentType': contentType,
      'extension': extension,
      'content': content,
    };
  }

  @override
  String toString() {
    return 'FileModel(fileName: $fileName, contentType: $contentType, extension: $extension, content: ${content.length > 50 ? '${content.substring(0, 50)}...' : content})';
  }
}

class ReportsPostModel {
  final String title;
  final String phoneNumber;
  final String description;
  final String address;
  final String type;
  final List<FileModel> files;

  ReportsPostModel({
    required this.title,
    required this.phoneNumber,
    required this.description,
    required this.address,
    required this.type,
    required this.files,
  });

  // Factory constructor to create a ReportsPostModel from a JSON map
  factory ReportsPostModel.fromJson(Map<String, dynamic> json) {
    return ReportsPostModel(
      title: json['Title'],
      phoneNumber: json['PhoneNumber'],
      description: json['Description'],
      address: json['Address'],
      type: json['Type'],
      files: (json['Files'] as List<dynamic>)
          .map((fileJson) => FileModel.fromJson(fileJson))
          .toList(),
    );
  }

  // Method to convert a ReportsPostModel to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'Title': title,
      'PhoneNumber': phoneNumber,
      'Description': description,
      'Address': address,
      'Type': type,
      'Files': files.map((file) => file.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'ReportsPostModel(title: $title, phoneNumber: $phoneNumber, description: $description, address: $address, type: $type, files: $files)';
  }
}
