// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'date_dropdown_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$dateFilterStateHash() => r'513cc5a39758845a63b1093070d45ba0ec8431c9';

/// See also [DateFilterState].
@ProviderFor(DateFilterState)
final dateFilterStateProvider =
    AutoDisposeNotifierProvider<DateFilterState, DateFilter>.internal(
  DateFilterState.new,
  name: r'dateFilterStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$dateFilterStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DateFilterState = AutoDisposeNotifier<DateFilter>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
