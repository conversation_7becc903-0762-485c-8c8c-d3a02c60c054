// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reports_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$reportSubmissionStateHash() =>
    r'4349ff7587ff7edef27a75c589e82a896ba31720';

/// See also [ReportSubmissionState].
@ProviderFor(ReportSubmissionState)
final reportSubmissionStateProvider = AutoDisposeNotifierProvider<
    ReportSubmissionState, AsyncValue<bool>>.internal(
  ReportSubmissionState.new,
  name: r'reportSubmissionStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$reportSubmissionStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ReportSubmissionState = AutoDisposeNotifier<AsyncValue<bool>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
