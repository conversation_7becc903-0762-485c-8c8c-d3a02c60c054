class StopModel {
  final int id;
  final int? sid;
  final double lat;
  final double lon;
  final String n;

  StopModel({
    required this.id,
    this.sid,
    required this.lat,
    required this.lon,
    required this.n,
  });

  factory StopModel.fromJson(Map<String, dynamic> json) {
    return StopModel(
      id: json['id'],
      sid: json['sid'],
      lat: json['lat'].toDouble(),
      lon: json['lon'].toDouble(),
      n: json['n'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'sid': sid,
      'lat': lat,
      'lon': lon,
      'n': n,
    };
  }

  @override
  String toString() {
    return 'StopModel(id: $id, sid: $sid, lat: $lat, lon: $lon, n: $n)';
  }
}
