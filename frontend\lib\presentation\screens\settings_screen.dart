// SettingsScreen.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../widgets/settings_header.dart';
import '../widgets/settings_options.dart';
import '../../providers/auth_providers.dart';

class SettingsScreen extends ConsumerWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // ✅ Watch the auth state at the top level to prevent multiple watchers
    final authState = ref.watch(authNotifierProvider);
    final userState = ref.watch(userAuthNotifierProvider);

    return Scaffold(
      body: authState.when(
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stack) => Center(
          child: Text('Error: $error'),
        ),
        data: (_) => SingleChildScrollView(
          child: Column(
            children: [
              SettingsHeader(userState: userState), // ✅ Pass state as parameter
              const SizedBox(height: 20),
              SettingsOptions(
                  userState: userState), // ✅ Pass state as parameter
            ],
          ),
        ),
      ),
    );
  }
}
