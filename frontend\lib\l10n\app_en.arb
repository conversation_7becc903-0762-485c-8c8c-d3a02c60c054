{
  "appTitle": "Smart Yambol",
  "guest": "Guest",
  "settings": "Settings",
  "darkMode": "Dark Mode",
  "language": "Language",
  "terms": "Terms & Conditions",
  "login": "Login",
  "tourism": "Tourism",
  "tourismDescription": "Explore the beauty of Yambol and create lifelong memories!",
  "weather": "Weather",
  "weatherDescription": "Stay prepared! See the forecast and air quality.",
  "events": "Events",
  "eventsDescription": "Check out cultural and sports events in the city.",
  "repairs": "Repairs",
  "repairsDescription": "Stay informed! See where and when repairs are happening.",
  "parking": "Parking",
  "parkingDescription": "Find a parking spot or pay for blue zone parking via SMS.",
  "generalInfo": "General Info",
  "generalInfoDescription": "Discover everything you need to know about Yambol in one place.",
  "transport": "Transport",
  "transportDescription": "Up-to-date information on public transport routes and schedules.",
  "cameras": "Cameras",
  "camerasDescription": "Live-stream views from key locations in the city.",
  "news": "News",
  "newsDescription": "Stay updated! Follow the latest news and events.",
  "taxes": "Taxes",
  "taxesDescription": "View and pay your municipal taxes quickly and easily.",
  "weddingServices": "Weddings",
  "weddingServicesDescription": "Easily pick the perfect date and location for your wedding!",
  "reports": "Reports",
  "reportsDescription": "Report an issue in the city and help make it better.",
  "favorites": "Favorites",
  "location": "Location",
  "home": "Home",
  "messages": "Messages",
  "settings": "Settings",
  "login": "Login",
  "emailOrUsername": "Email or Username",
  "password": "Password",
  "forgotPassword": "Forgot Password?",
  "loginButton": "Log In",
  "orContinueWith": "- or continue with -",
  "createNewAccount": "Create a new account",
  "registerHere": "here",
  "register": "Register",
  "emailOrUsername": "Email or Username",
  "password": "Password",
  "confirmPassword": "Confirm Password",
  "agreeToTerms": "By clicking the Register button, you agree to the",
  "registerTerms": "terms & conditions",
  "registerButton": "Register",
  "orContinueWith": "- or continue with -",
  "alreadyHaveAccount": "Already have an account?",
  "loginHere": "Log in here",
  "dontHaveAccount": "Don't have an account?",
  "firstName": "First Name",
  "lastName": "Last Name",
  "email": "Email",
  "emailRequired": "Email is required",
  "invalidEmail": "Please enter a valid email",
  "registrationSuccessful": "Registration successful!",
  "passwordRequired": "Password is required",
  "passwordTooShort": "Password is too short",
  "confirmPasswordRequired": "Confirm password required",
  "passwordsDoNotMatch": "Passwords do not match",
  "firstNameRequired": "First name is required",
  "lastNameRequired": "Last name is required",
 "tourismWelcomeMessage": "Explore Yambol! Discover its landmarks and get inspired by its history.",
  "tourismLegendsAndMyths": "Legends and Myths",
  "tourismSights": "Landmarks",
  "tourismCulturalAndArtisticSites": "Cultural and Artistic Sites",
  "tourismRoutesAndActivities": "Routes and Activities",
  "tourismFamilyEntertainment": "Family Entertainment",
  "tourismNightlife": "Nightlife",
  "tourismTransport": "Transport",
  "tourismTravelAgencies": "Travel Agencies",
 "loginSuccess": "Login successful",
 "loginError": "Login failed",
 "legendsAndMythsWelcomeMessage": "Discover the mysteries of Yambol: Legends and myths that will captivate you",
 "weatherTitle": "Weather",
  "cityName": "Yambol",
  "wind": "Wind",
  "humidity": "Humidity",
  "errorLoadingWeather": "Error loading weather data",
  "retry": "Retry",
  "monday": "Monday",
  "tuesday": "Tuesday",
  "wednesday": "Wednesday",
  "thursday": "Thursday",
  "friday": "Friday",
  "saturday": "Saturday",
  "sunday": "Sunday",
 "eventsWelcomeMessage" : "Don't miss anything! Explore cultural and sports events in the city!",
 "search" : "Search...",
 "tourismCulturalSites": "Cultural sites",
 "map" : "Map",
 "all" : "All",
  "sportEvents" : "Sport",
  "cultureEvents" : "Culture",
  "celebrationEvents" : "Celebrations",
  "mon": "Mon",
  "tue": "Tue",
  "wed": "Wed",
  "thu": "Thu",
  "fri": "Fri",
  "sat": "Sat",
  "sun": "Sun",
   "january": "January",
  "february": "February",
  "march": "March",
  "april": "April",
  "may": "May",
  "june": "June",
  "july": "July",
  "august": "August",
  "september": "September",
  "october": "October",
  "november": "November",
  "december": "December",
  "today" : "Today",
  "thisWeek" : "This week",
  "thisMonth" : "This Month",
  "noFavorites": "No favourites",
  "tourismScreenTitle": "Tourism",
  "more": "More",
  "selectCategory": "Select category",
  "emailConfirmationTitle": "Email Confirmation",
  "emailConfirmationMessage": "Please check your email to confirm your account.",
  "didNotReceiveEmail": "Didn't receive the email?",
  "resendVerificationEmail": "Resend Verification Email",
  "verificationEmailResent": "Verification email resent!",
  "errorOccurred": "Error occurred",
  "emailVerificationTitle": "Email Verification",
  "verifyEmailButton": "Verify Email",
  "emailVerifiedSuccessfully": "Email verified successfully!",
  "generalInfoWelcomeMessage" : "Yambol – business, education, and essential city information!",
  "gasStations" : "Gas stations",
  "generalInformationFull" : "General information",
  "shop" : "Shops",
  "restaurants" : "Restaurants",
  "coffee" : "Coffee",
  "bars" : "Bars",
  "pastryShops" : "Pastry shops",
  "establishments" : "Establishments",
  "establishmentsWelcomeMessage" : "Cozy restaurants, traditional cuisine, and modern venues with a unique atmosphere!",
  "hotels" : "Hotels",
  "guestHouses" : "Guest houses",
  "accommodation" : "Accommodation",
  "accommodationWelcomeMessage" : "Comfortable hotels, cozy guesthouses, and great places to stay for every taste!",
  "finance" : "Finance",
  "banks" : "Banks",
  "currencyExchanges" : "Currency Exchange",
  "insuranceCompanies" : "Insurance",
  "atms" : "ATMs",
  "financeYambolWelcomeMessage" : "Secure banking services, affordable loans, and financial solutions for you in Yambol!",
  "bioShops" : "Bio shops",
  "farms" : "Farms",
  "recycling" : "Recycling",
  "ecoInitiatives" : "Eco initiatives",
  "ecology" : "Ecology",
  "ecoYambolWelcomeMessage" : "Clean nature, sustainable future, and eco-friendly solutions for Yambol!",
  "culture" : "Culture",
  "museums" : "Museums",
  "theaters" : "Theaters",
  "galleries" : "Galleries",
  "cultureYambolWelcomeMessage" : "Rich cultural heritage, inspiring art, and traditions in Yambol!",
  "education" : "Education",
  "kindergardens" : "Kindergardens",
  "nursery" : "Nursery",
  "childNutritionCenter" : "Child nutrition center",
  "schools" : "Schools",
  "universities" : "Universities",
  "developmentCenters" : "Development centers",
  "health" : "Health",
  "pharmacies" : "Pharmacies",
  "medicalEstablishments" : "Medical establishments",
  "doctorsOffices" : "Doctors offices",
  "medicalLabs" : "Medical labs",
  "veterinaries" : "Veterinaries",
  "healthYambolWelcomeMessage" : "Health and care for everyone – quality medical services and a healthy lifestyle in Yambol!",
  "educationYambolWelcomeMessage" : "Quality education for a bright future – innovative learning and development in Yambol!",
  "sport" : "Sport",
  "sportFacilities" : "Sport Facilities",
  "sportYambolWelcomeMessage" : "Sports and an active lifestyle – modern facilities and opportunities for movement in Yambol!",
  "newsYambolWelcomeMessage" : "Stay informed! Follow the latest news and events.",
  "close" : "Close",
  "description" : "Description",
  "municipalityTaxes" : "Municipality Taxes",
  "information" : "Information",
  "chooseWayToPay": "You can pay online using your CIN or with an electronic signature (which must be installed on your device).",
  "waysToPay" : "Ways to pay",
  "kinNumber" : "Pay with KIN",
  "kinDesc" : "Fast and convenient payment with your KIN number",
  "electronicSignature" : "Pay with electronic signature",
  "electronicSignatureDesc" : "Secure payment with your electronic signature",
  "userHelp" : "Need help? Contact us at phone: 0875 333 844",
  "problemElectronicSignature" : "The electronic signature payment link could not be opened.",
  "problemKin" : "The KIN payment link could not be opened.",
  "navigate" : "Navigate",
  "business" : "Business",
  "landmarks" : "Landmarks",
  "healthcare" : "Healthcare",
  "education" : "Education",
  "sports" : "Sports",
  "applyFilters" : "Apply Filters",
  "filterCategories" : "Filter Categories",
  "verifyingEmail": "Verifying your email...",
  "processingVerification": "Processing verification...",
  "retryVerification": "Retry Verification",
  "verificationFailed" : "Verification failed",
  "checkSpamFolder" : "Don't forget to check your spam folder!",
  "userAlreadyExists": "An account with this email already exists!",
  "invalidCredentials": "Invalid email or password. Please check your credentials and try again.",
  "emailNotVerified": "Please verify your email address before logging in.",
  "weakPassword": "Password must be at least 8 characters long and contain uppercase, lowercase, and numbers.",
  "invalidEmailFormat": "Please enter a valid email address.",
  "connectionTimeout": "Connection timeout. Please check your internet connection and try again.",
  "noInternet": "No internet connection. Please check your network settings.",
  "userNotFound": "User not found. Please check your credentials or register a new account.",
  "resourceNotFound": "The requested resource was not found.",
  "serverError": "Server error. Please try again later.",
  "validationError": "Validation failed. Please check your input.",
  "networkError": "An unexpected error occurred.",
  "accessDenied": "Access denied.",
  "passwordRequirements": "Password Requirements:",
  "requirementMinLength": "At least 6 characters",
  "requirementUppercase": "At least one uppercase letter (A-Z)",
  "requirementNumber": "At least one number (0-9)",
  "requirementSpecialChar": "At least one special character (!@#$%^&*)",
  "passwordMustContainUppercase": "Password must contain at least one uppercase letter",
  "passwordMustContainNumber": "Password must contain at least one number",
  "passwordMustContainSpecialChar": "Password must contain at least one special character",
   "dangerousBuildingOrSite": "Dangerous Building or Site",
  "streetLight": "Street Light",
  "trash": "Trash",
  "dangerousPlayground": "Dangerous Playground",
  "pothole": "Pothole",
  "brokenOrMissingSign": "Broken or Missing Sign",
  "illegalParking": "Illegal Parking",
  "hangingCables": "Hanging Cables",
  "waterLeakage": "Water Leakage",
  "fallenTree": "Fallen Tree",
  "collapsedRoad": "Collapsed Road",
  "illegalBuilding": "Illegal Building",
  "trafficLightNotWorking": "Traffic Light Not Working",
  "strayDogs": "Stray Dogs",
  "publicOrder": "Public Order",
  "abandonedCar": "Abandoned Car",
  "other": "Other",
  "title" : "Title",
  "typeOfSignal" : "Type of Signal",
  "phone" : "Phone",
  "address" : "Address",
  "description" : "Description",
  "files" : "Files",
  "addFiles" : "Add Files",
  "submitReport" : "Submit Report",
  "noFilesSelected" : "No files selected",
  "titleIsRequired" : "Title is required",
  "phoneIsRequired" : "Phone is required",
  "typeOfSignalIsRequired" : "Type of signal is required",
  "reportSubmittedSuccessfully" : "Report submitted successfully!",
  "reportSubmissionFailed" : "Report submission failed. Please try again.",
  "reportAnIssue" : "Report an issue",
  "loginRequired" : "Login required to submit reports",
  "pleaseLoginToSubmitReports" : "Please login to submit reports",
  "selectSource" : "Select source",
  "camera" : "Camera",
  "gallery" : "Gallery",
  "passwordResetEmailSent": "Password reset email sent successfully!",
  "emailRequired": "Email is required",
  "invalidEmail": "Please enter a valid email address",
  "resetYourPassword": "Reset Your Password",
  "enterEmailToReset": "Enter your email address and we'll send you a link to reset your password.",
  "sendResetLink": "Send Reset Link",
  "rememberYourPassword": "Remember your password?",
  "backToLogin": "Back to Login",
  "emailAddress": "Email Address",
  "errorSendingResetEmail": "Error sending reset email. Please try again.",
  "linkExpired": "Link Expired",
  "resetLinkExpiredMessage": "This password reset link has expired. Please request a new one.",
  "invalidLink": "Invalid Link",
  "invalidLinkMessage": "This password reset link is invalid. Please request a new one.",
  "requestNewLink": "Request New Link",
  "passwordResetSuccess": "Password reset successfully!",
  "passwordResetError": "Failed to reset password. Please try again.",
  "resetPassword": "Reset Password",
  "createNewPassword": "Create New Password",
  "enterNewPasswordMessage": "Enter your new password below. Make sure it's strong and secure.",
  "newPassword": "New Password",
  "confirmNewPassword": "Confirm New Password",
  "resetPasswordButton": "Reset Password",
  "passwordRequired": "Password is required",
  "passwordTooShort": "Password must be at least 8 characters",
  "passwordRequirements": "Password must contain at least one uppercase letter, one lowercase letter, and one number",
  "confirmPasswordRequired": "Please confirm your password",
  "passwordsDoNotMatch": "Passwords do not match",
  "repairStatusOngoing": "🔧 In Progress",
  "repairStatusCompleted": "✅ Completed",
  "repairStatusScheduled": "📅 Scheduled", 
  "notSpecified": "Not specified",
  "unknownRepair": "Unknown repair",
  "start": "Start",
  "end": "End",
  "description": "Description", 
  "noDescriptionAvailable": "No description available.",
  "repairNotFound": "Repair not found",
  "loadingError": "Loading error",
  "notifications": "Notifications",
  "allEnabled": "All enabled",
  "newsOnly": "News only",
  "eventsOnly": "Events only",
  "allDisabled": "All disabled",
  "repairsOnly": "Repairs only",
  "systemPermissionRequired": "System Permission Required",
  "systemPermissionMessage": "Enable notifications in your phone settings to receive alerts.",
  "enableNotifications": "Enable Notifications",
  "enableNotificationsMessage": "To receive notifications, you need to allow notifications in your phone settings. Would you like to enable them now?",
  "enable": "Enable",
  "cancel": "Cancel",
  "selectABusStop" : "Select a bus stop",
  "busStop" : "Bus stop",
  "busStopNotFound" : "Bus stop not found",
  "busStopNotSelected" : "Bus stop not selected",
  "busStopSelected" : "Bus stop selected",
  "bussArrivals" : "Bus arrivals",
  "now" : "Now",
  "min" : "min",
  "noBusArrivals" : "No bus arrivals",
  "noBusArrivalsMessage" : "There are no bus arrivals at this stop at the moment.",
  "scheduled" : "Scheduled",
  "tapForBusArrivals" : "Tap for bus arrivals",
  "live": "LIVE",
  "offline": "OFFLINE",
  "stream_active": "Stream Active",
  "connection_failed": "Connection Failed",
  "real_time_video_feed": "Real-time video feed",
  "unable_to_connect": "Unable to connect to camera",
  "camera_controls": "Camera Controls",
  "refresh": "Refresh",
  "full_screen": "Full Screen",
  "exit_full_screen": "Exit Full Screen",
  "loading_stream": "Loading stream...",
  "connection_failed_title": "Connection Failed",
  "tap_refresh_to_try_again": "Tap refresh to try again",
  "help_text": "This is a live stream from Yambol city center. Use the controls above to refresh.",
  "yambol_live_camera": "Yambol Live Camera",
  "city_center": "City Center",
  "live_stream": "Live Stream",
  "airQuality": "Air Quality",
  "airQualityDescription": "Breathe easy! Monitor the air quality and take care of your health.",
  "blueZoneParkingMessage" : "This is a blue zone parking area. Parking fees may apply during business hours.",
  "freeParkingMessage" : "This is a free parking spot. No parking fees apply.",
  "parkingDetails" : "Parking Details",
  "blueZone" : "Blue Zone",
  "freeParking" : "Free Parking",
  "parkingZone" : "Parking Zone",
  "parkingInformations" : "Parking Informations",
  "air_quality_zone": "Air Quality Zone",
    "weather_station": "Weather Station",
    "loading_details": "Loading details...",
    "loading_sensor_data": "Loading sensor data...",
    "error_loading_data": "Error loading data",
    "error_loading_sensor_data": "Error loading sensor data",
    "try_again": "Try again",
    "no_sensor_data_available": "No data available from desired sensors",
    "sensor_humidity": "Air Humidity",
    "sensor_temperature": "Air Temperature",
    "sensor_pressure": "Atmospheric Pressure",
    "sensor_pm25": "PM 2.5",
    "sensor_pm10": "PM 10.0",
    "sensor_radiation": "Gamma Radiation",
    "sensor_pm": "Fine Particulate Matter",
    "status_normal": "Normal",
    "status_moderate": "Moderate",
    "status_dangerous": "Dangerous",
    "air_quality_zone": "Air Quality Zone",
    "weather_station": "Weather Station",
    "loading_details": "Loading details...",
    "loading_sensor_data": "Loading sensor data...",
    "error_loading_data": "Error loading data",
    "error_loading_sensor_data": "Error loading sensor data",
    "try_again": "Try again",
    "no_sensor_data_available": "No data available from desired sensors",
    "sensor_humidity": "Air Humidity",
    "sensor_temperature": "Air Temperature",
    "sensor_pressure": "Atmospheric Pressure",
    "sensor_pm25": "PM 2.5",
    "sensor_pm10": "PM 10.0",
    "sensor_radiation": "Gamma Radiation",
    "sensor_pm": "Fine Particulate Matter",
    "status_normal": "Normal",
    "status_moderate": "Moderate",
    "status_dangerous": "Dangerous",
    "select_air_quality_zone": "Select air quality zone",
    "loading_zones": "Loading zones...",
    "error_loading_zones": "Error loading zones",
    "zone_with_number": "Zone {number}",
    "@zone_with_number": {
      "placeholders": {
        "number": {
          "type": "String"
        }
      }
    },
    "number_label": "Number: {number}",
    "@number_label": {
      "placeholders": {
        "number": {
          "type": "String"
        }
      }
    },
    "active": "Active",
    "inactive": "Inactive",
    "tap_for_details": "Tap for details"

  "recentAnnouncements" : "Recent Announcements",
  "ago" : "ago",
  "h" : "h",
  "m" : "m",
  "day" : "day",
  "days" : "days",
  "hour" : "hour",
  "hours" : "hours",
  "minute" : "minute",
  "minutes" : "minutes",
  "markAllAsRead": "Mark all as read",
  "loadingAnnouncements" : "Loading announcements...",
  "failedToLoadAnnouncements" : "Failed to load announcements",
  "pleaseCheckYourConnection" : "Please check your connection and try again.",
  "noAnnouncementsMessage": "You don't have any announcements yet.\nCheck back later for updates.",
  "nouAnnouncements" : "No announcements",
  "announcementHeaderMessage": "Important announcements from Yambol Municipality"
}