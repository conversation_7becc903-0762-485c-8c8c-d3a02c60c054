import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart'; // Add this import for GoRouter
import '../widgets/feature_tile.dart';
import '../../data/feature_items.dart';
import '../../providers/app_providers.dart';

class SmartYambolScreen extends ConsumerWidget {
  const SmartYambolScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isGridView = ref.watch(layoutModeProvider);
    final theme = Theme.of(context); // Fetch current theme mode

    int getCrossAxisCount(BuildContext context) {
      double screenWidth = MediaQuery.of(context).size.width;
      if (screenWidth < 376) {
        // For small screens like iPhone SE
        return 2;
      } else if (screenWidth < 600) {
        // For medium screens (like some tablets)
        return 3;
      } else {
        // For larger screens
        return 4;
      }
    }

    return Container(
      color: theme.scaffoldBackgroundColor, // Adapt background to theme
      child: Column(
        children: [
          // Toggle Bar with Dynamic Colors
          Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.grid_view,
                    size: 30,
                    color: isGridView
                        ? Color(0xFF22D400)
                        : (theme.brightness == Brightness.dark
                            ? Colors.white60
                            : Colors.black45), // Adapt color
                  ),
                  onPressed: () =>
                      ref.read(layoutModeProvider.notifier).toggleLayout(),
                ),
                SizedBox(width: 30), // Add some space between the buttons
                IconButton(
                  icon: Icon(
                    Icons.view_list,
                    size: 30,
                    color: !isGridView
                        ? Color(0xFF22D400)
                        : (theme.brightness == Brightness.dark
                            ? Colors.white60
                            : Colors.black45), // Adapt color
                  ),
                  onPressed: () =>
                      ref.read(layoutModeProvider.notifier).toggleLayout(),
                ),
              ],
            ),
          ),
          // Custom divider with dynamic green line
          Stack(
            children: [
              Divider(
                height: 1, // Height of the divider
                thickness: 0.3, // Thickness of the divider line
                color: Colors.grey, // Color of the divider
                indent: 0, // Start indent from left
                endIndent: 0, // End indent from right
              ),
              // Green line on top of the divider
              Positioned(
                top: 0, // Make sure it's on top of the divider
                left: 0,
                right: MediaQuery.of(context).size.width / 2,
                child: Container(
                  height: 1, // Line height
                  color: isGridView ? Color(0xFF22D400) : Colors.transparent,
                  width: MediaQuery.of(context).size.width /
                      2, // Half of the screen width
                ),
              ),
              Positioned(
                top: 0, // Make sure it's on top of the divider
                left: MediaQuery.of(context).size.width / 2,
                right: 0,
                child: Container(
                  height: 1, // Line height
                  color: !isGridView ? Color(0xFF22D400) : Colors.transparent,
                  width: MediaQuery.of(context).size.width /
                      2, // Half of the screen width
                ),
              ),
            ],
          ),
          // Dynamic Layout - Grid & List Adapts to Theme
          Expanded(
            child: isGridView
                ? GridView.builder(
                    padding: const EdgeInsets.all(16.0),
                    itemCount: featureItems.length,
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: getCrossAxisCount(context),
                      crossAxisSpacing: 12,
                      mainAxisSpacing: 12,
                      childAspectRatio: 1,
                    ),
                    itemBuilder: (context, index) {
                      final feature = featureItems[index];
                      print(feature['translationKey']);
                      return GestureDetector(
                        onTap: () {
                          // If the feature is 'tourism', navigate using GoRouter
                          if (feature['translationKey'] == 'tourism') {
                            context.push(
                                '/tourism'); // Use GoRouter for navigation
                          } else if (feature['translationKey'] == 'weather') {
                            context.push('/weather');
                          } else if (feature['translationKey'] == 'events') {
                            context.push('/events');
                          } else if (feature['translationKey'] ==
                              'general_info') {
                            context.push('/general');
                          } else if (feature['translationKey'] == 'news') {
                            context.push('/news');
                          } else if (feature['translationKey'] == 'parking') {
                            context.push('/parking');
                          } else if (feature['translationKey'] == 'taxes') {
                            context.push('/taxes');
                          } else if (feature['translationKey'] == 'weddings') {
                            context.push('/weddings');
                          } else if (feature['translationKey'] == 'reports') {
                            context.push('/reports');
                          } else if (feature['translationKey'] == 'repairs') {
                            context.push('/repairs');
                          } else if (feature['translationKey'] == 'transport') {
                            context.push('/transport');
                          } else if (feature['translationKey'] == 'cameras') {
                            context.push('/cameras');
                          } else if (feature['translationKey'] ==
                              'air_quality') {
                            context.push('/air-quality');
                          }
                        },
                        child: FeatureTile(
                            icon: feature['icon'],
                            translationKey: feature['translationKey'],
                            descriptionKey: feature['descriptionKey'],
                            color: feature['color'],
                            isGridView: isGridView,
                            disabled: feature['disabled']),
                      );
                    },
                  )
                : ListView.builder(
                    padding: const EdgeInsets.all(16.0),
                    itemCount: featureItems.length,
                    itemBuilder: (context, index) {
                      final feature = featureItems[index];
                      return GestureDetector(
                        onTap: () {
                          // If the feature is 'tourism', navigate using GoRouter
                          if (feature['translationKey'] == 'tourism') {
                            context.push(
                                '/tourism'); // Use GoRouter for navigation
                          } else if (feature['translationKey'] == 'weather') {
                            context.push('/weather');
                          } else if (feature['translationKey'] == 'events') {
                            context.push('/events');
                          } else if (feature['translationKey'] ==
                              'general_info') {
                            context.push('/general');
                          } else if (feature['translationKey'] == 'news') {
                            context.push('/news');
                          } else if (feature['translationKey'] == 'parking') {
                            context.push('/parking');
                          } else if (feature['translationKey'] == 'taxes') {
                            context.push('/taxes');
                          } else if (feature['translationKey'] == 'weddings') {
                            context.push('/weddings');
                          } else if (feature['translationKey'] == 'reports') {
                            context.push('/reports');
                          } else if (feature['translationKey'] == 'repairs') {
                            context.push('/repairs');
                          } else if (feature['translationKey'] == 'transport') {
                            context.push('/transport');
                          } else if (feature['translationKey'] == 'cameras') {
                            context.push('/cameras');
                          } else if (feature['translationKey'] ==
                              'air_quality') {
                            context.push('/air-quality');
                          }
                        },
                        child: FeatureTile(
                          icon: feature['icon'],
                          translationKey: feature['translationKey'],
                          descriptionKey: feature['descriptionKey'],
                          color: feature['color'],
                          isGridView: isGridView,
                          disabled: feature['disabled'],
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }
}
