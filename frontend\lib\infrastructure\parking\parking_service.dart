import 'package:dio/dio.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:frontend/models/get_parking_command.dart';

class ParkingService {
  static final Dio _dio = Dio(BaseOptions(
    baseUrl: dotenv.env['BASE_URL']!, // Load BASE_URL from .env
  ));

  /// **Fetch All Parking Lots**
  static Future<List<ParkingModel>> fetchParkingLots() async {
    const String parkingEndpoint = 'ParkingLots';

    try {
      print('PARKING REQUEST: Fetching all parking lots');

      final response = await _dio.get(parkingEndpoint);

      if (response.statusCode == 200) {
        print('PARKING RESPONSE: ${response.data}');

        List<dynamic> jsonData = response.data;
        return jsonData.map((item) => ParkingModel.fromJson(item)).toList();
      } else {
        throw Exception(
            'Failed to load parking lots with status ${response.statusCode}');
      }
    } on DioException catch (e) {
      print("❌ Parking Service Dio Error: ${e.response?.statusCode}");
      print("📩 Response Data: ${e.response?.data}");
      print("📝 Error Message: ${e.message}");
      return [];
    } catch (e) {
      print("⚠ Parking Service Unexpected Error: $e");
      return [];
    }
  }
}
