import 'l10n.dart';

// ignore_for_file: type=lint

/// The translations for Bulgarian (`bg`).
class SBg extends S {
  SBg([String locale = 'bg']) : super(locale);

  @override
  String get appTitle => 'Смарт Ямбол';

  @override
  String get guest => 'Гост';

  @override
  String get settings => 'Настройки';

  @override
  String get darkMode => 'Тъмна тема';

  @override
  String get language => 'Език';

  @override
  String get terms => 'Условия за използване';

  @override
  String get login => 'Вход';

  @override
  String get tourism => 'Туризъм';

  @override
  String get tourismDescription => '„Ямбол те кани – открий неговата красота и създай спомени за цял живот!“';

  @override
  String get weather => 'Времето';

  @override
  String get weatherDescription => 'Бъди подготвен! Виж прогнозата и качеството на въздуха.';

  @override
  String get events => 'Събития';

  @override
  String get eventsDescription => 'Разгледай културни и спортни събития в града.';

  @override
  String get repairs => 'Ремонти';

  @override
  String get repairsDescription => 'Остани информиран! Виж къде и кога се извършват ремонти.';

  @override
  String get parking => 'Паркинг';

  @override
  String get parkingDescription => 'Паркирай без стрес! Намери свободно място или изпрати SMS за синя зона.';

  @override
  String get generalInfo => 'Обща Инф.';

  @override
  String get generalInfoDescription => 'Открий града! Всичко, което трябва да знаеш за Ямбол, на едно място.';

  @override
  String get transport => 'Транспорт';

  @override
  String get transportDescription => 'Планирай пътуването си лесно – виж разписания и актуална информация за градския транспорт в реално време.';

  @override
  String get cameras => 'Камери';

  @override
  String get camerasDescription => 'Виж какво се случва! Гледай на живо от ключови места в града.';

  @override
  String get news => 'Новини';

  @override
  String get newsDescription => 'Бъди в час! Следи актуалните новини и събития.';

  @override
  String get taxes => 'Данъци';

  @override
  String get taxesDescription => 'Виж и плати своите общински данъци бързо и лесно.';

  @override
  String get weddingServices => 'Сватби';

  @override
  String get weddingServicesDescription => 'Избери дата и място за твоята сватба лесно и бързо!';

  @override
  String get reports => 'Сигнали';

  @override
  String get reportsDescription => 'Бъди активен! Подай сигнал за нередност.';

  @override
  String get favorites => 'Любими';

  @override
  String get location => 'Локация';

  @override
  String get home => 'Начало';

  @override
  String get messages => 'Съобщения';

  @override
  String get emailOrUsername => 'Потребителско име или имейл';

  @override
  String get password => 'Парола';

  @override
  String get forgotPassword => 'Забравена парола?';

  @override
  String get loginButton => 'Влез';

  @override
  String get orContinueWith => '- или продължи с -';

  @override
  String get createNewAccount => 'Създай нов профил';

  @override
  String get registerHere => 'от тук';

  @override
  String get register => 'Регистрация';

  @override
  String get confirmPassword => 'Повтори парола';

  @override
  String get agreeToTerms => 'С натискането на бутона Регистрация се съгласявате с';

  @override
  String get registerTerms => 'условията за ползване';

  @override
  String get registerButton => 'Регистрация';

  @override
  String get alreadyHaveAccount => 'Имате вече съществуващ профил?';

  @override
  String get loginHere => 'Влезте от тук';

  @override
  String get dontHaveAccount => 'Нямате акаунт?';

  @override
  String get firstName => 'Име';

  @override
  String get lastName => 'Фамилия';

  @override
  String get email => 'Имейл';

  @override
  String get emailRequired => 'Имейлът е задължителен';

  @override
  String get invalidEmail => 'Моля, въведете валиден имейл адрес';

  @override
  String get registrationSuccessful => 'Регистрацията е успешна!';

  @override
  String get passwordRequired => 'Паролата е задължителна';

  @override
  String get passwordTooShort => 'Паролата трябва да бъде поне 8 символа';

  @override
  String get confirmPasswordRequired => 'Моля, потвърдете паролата си';

  @override
  String get passwordsDoNotMatch => 'Паролите не съвпадат';

  @override
  String get firstNameRequired => 'Името е задължително';

  @override
  String get lastNameRequired => 'Фамилията е задължителна';

  @override
  String get tourismWelcomeMessage => 'Опознай Ямбол! Разгледай забележителностите и се вдъхнови от историята му.';

  @override
  String get tourismLegendsAndMyths => 'Легенди и митове';

  @override
  String get tourismSights => 'Забележителности';

  @override
  String get tourismCulturalAndArtisticSites => 'Културни и артистични обекти';

  @override
  String get tourismRoutesAndActivities => 'Маршрути и активности';

  @override
  String get tourismFamilyEntertainment => 'Семейни забавления';

  @override
  String get tourismNightlife => 'Нощен живот';

  @override
  String get tourismTransport => 'Транспорт';

  @override
  String get tourismTravelAgencies => 'Туристически агенции';

  @override
  String get loginSuccess => 'Входът е успешен';

  @override
  String get loginError => 'Грешка при входа';

  @override
  String get legendsAndMythsWelcomeMessage => 'Открий мистериите на Ямбол: Легенди и митове, които ще те завладеят';

  @override
  String get weatherTitle => 'Времето';

  @override
  String get cityName => 'Ямбол';

  @override
  String get wind => 'Вятър';

  @override
  String get humidity => 'Влажност';

  @override
  String get errorLoadingWeather => 'Грешка при зареждане на времето';

  @override
  String get retry => 'Опитай отново';

  @override
  String get monday => 'Понеделник';

  @override
  String get tuesday => 'Вторник';

  @override
  String get wednesday => 'Сряда';

  @override
  String get thursday => 'Четвъртък';

  @override
  String get friday => 'Петък';

  @override
  String get saturday => 'Събота';

  @override
  String get sunday => 'Неделя';

  @override
  String get eventsWelcomeMessage => 'Не пропускай нищо! Разгледай културни и спортни събития в града!';

  @override
  String get search => 'Търси...';

  @override
  String get tourismCulturalSites => 'Културни и арт обекти';

  @override
  String get map => 'Карта';

  @override
  String get all => 'Всички';

  @override
  String get sportEvents => 'Спортни';

  @override
  String get cultureEvents => 'Културни';

  @override
  String get celebrationEvents => 'Празници';

  @override
  String get mon => 'ПН';

  @override
  String get tue => 'ВТ';

  @override
  String get wed => 'С';

  @override
  String get thu => 'Ч';

  @override
  String get fri => 'П';

  @override
  String get sat => 'СБ';

  @override
  String get sun => 'НД';

  @override
  String get january => 'Януари';

  @override
  String get february => 'Февруари';

  @override
  String get march => 'Март';

  @override
  String get april => 'Април';

  @override
  String get may => 'Май';

  @override
  String get june => 'Юни';

  @override
  String get july => 'Юли';

  @override
  String get august => 'Август';

  @override
  String get september => 'Септември';

  @override
  String get october => 'Октомври';

  @override
  String get november => 'Ноември';

  @override
  String get december => 'Декември';

  @override
  String get today => 'Днес';

  @override
  String get thisWeek => 'Тази седмица';

  @override
  String get thisMonth => 'Този месец';

  @override
  String get noFavorites => 'Няма любими';

  @override
  String get tourismScreenTitle => 'Туризъм';

  @override
  String get more => 'Още';

  @override
  String get selectCategory => 'Избери категория';

  @override
  String get emailConfirmationTitle => 'Потвърждение на имейл';

  @override
  String get emailConfirmationMessage => 'Моля, проверете своя имейл, за да потвърдите акаунта си.';

  @override
  String get didNotReceiveEmail => 'Не сте получили имейл?';

  @override
  String get resendVerificationEmail => 'Изпрати повторно имейла за потвърждение';

  @override
  String get verificationEmailResent => 'Имейлът за потвърждение беше изпратен отново!';

  @override
  String get errorOccurred => 'Възникна грешка';

  @override
  String get emailVerificationTitle => 'Потвърждение на имейл';

  @override
  String get verifyEmailButton => 'Потвърдете имейла';

  @override
  String get emailVerifiedSuccessfully => 'Имейлът беше потвърден успешно!';

  @override
  String get generalInfoWelcomeMessage => 'Ямбол – бизнес, образование и важна информация за града!';

  @override
  String get gasStations => 'Бензиностанции';

  @override
  String get generalInformationFull => 'Обща информация';

  @override
  String get shop => 'Търговия';

  @override
  String get restaurants => 'Ресторанти';

  @override
  String get coffee => 'Кафета';

  @override
  String get bars => 'Барове';

  @override
  String get pastryShops => 'Сладкарници';

  @override
  String get establishments => 'Заведения';

  @override
  String get establishmentsWelcomeMessage => 'Уютни ресторанти, традиционна кухня и модерни заведения с неповторима атмосфера!';

  @override
  String get hotels => 'Хотели';

  @override
  String get guestHouses => 'Къщи за гости';

  @override
  String get accommodation => 'Настаняване';

  @override
  String get accommodationWelcomeMessage => 'Комфортни хотели, уютни къщи за гости и отлични места за престой!';

  @override
  String get finance => 'Финанси';

  @override
  String get banks => 'Банки';

  @override
  String get currencyExchanges => 'Обменни бюра';

  @override
  String get insuranceCompanies => 'Застрахователи';

  @override
  String get atms => 'Банкомати';

  @override
  String get financeYambolWelcomeMessage => 'Сигурни банкови услуги, изгодни кредити и финансови решения за вас в Ямбол!';

  @override
  String get bioShops => 'Био магазини';

  @override
  String get farms => 'Ферми';

  @override
  String get recycling => 'Рециклиране';

  @override
  String get ecoInitiatives => 'Еко инициативи';

  @override
  String get ecology => 'Екология';

  @override
  String get ecoYambolWelcomeMessage => 'Чиста природа, устойчиво бъдеще и екологични решения за Ямбол!';

  @override
  String get culture => 'Култура';

  @override
  String get museums => 'Музеи';

  @override
  String get theaters => 'Театри';

  @override
  String get galleries => 'Галерии';

  @override
  String get cultureYambolWelcomeMessage => 'Богато културно наследство, вдъхновяващо изкуство и традиции в Ямбол!';

  @override
  String get education => 'Образование';

  @override
  String get kindergardens => 'Детски градини';

  @override
  String get nursery => 'Детски ясли';

  @override
  String get childNutritionCenter => 'Детска кухня';

  @override
  String get schools => 'Училища';

  @override
  String get universities => 'Университети';

  @override
  String get developmentCenters => 'ЦПЛР';

  @override
  String get health => 'Здраве';

  @override
  String get pharmacies => 'Аптеки';

  @override
  String get medicalEstablishments => 'Лечебни заведения';

  @override
  String get doctorsOffices => 'Медицински кабинети';

  @override
  String get medicalLabs => 'Лаборатории';

  @override
  String get veterinaries => 'Ветеринарни клиники';

  @override
  String get healthYambolWelcomeMessage => 'Здраве и грижа за всеки – качествени медицински услуги и здравословен начин на живот в Ямбол!';

  @override
  String get educationYambolWelcomeMessage => 'Качествено образование за светло бъдеще – иновативно обучение и развитие в Ямбол!';

  @override
  String get sport => 'Спорт';

  @override
  String get sportFacilities => 'Спортни бази';

  @override
  String get sportYambolWelcomeMessage => 'Спорт и активен живот – модерни съоръжения и възможности за движение в Ямбол!';

  @override
  String get newsYambolWelcomeMessage => 'Бъди в час! Следи актуалните новини и събития.';

  @override
  String get close => 'Затвори';

  @override
  String get description => 'Описание';

  @override
  String get municipalityTaxes => 'Общински данъци';

  @override
  String get information => 'Информация';

  @override
  String get chooseWayToPay => 'Можете да платите онлайн с КИН или с електронен подпис (необходимо е той да бъде инсталиран на устройството Ви).';

  @override
  String get waysToPay => 'Начини за плащане';

  @override
  String get kinNumber => 'Плащане с КИН';

  @override
  String get kinDesc => 'Бързо и удобно плащане с вашия КИН номер';

  @override
  String get electronicSignature => 'Плащане с електронен подпис';

  @override
  String get electronicSignatureDesc => 'Сигурно плащане с вашия електронен подпис';

  @override
  String get userHelp => 'Нужда от помощ? Свържете се с нас на телефон: 0875 333 844';

  @override
  String get problemElectronicSignature => 'Не може да се отвори връзката за плащане с електронен подпис.';

  @override
  String get problemKin => 'Не може да се отвори връзката за плащане с КИН.';

  @override
  String get navigate => 'Навигирай';

  @override
  String get business => 'Бизнес';

  @override
  String get landmarks => 'Забележителности';

  @override
  String get healthcare => 'Здраве';

  @override
  String get sports => 'Спорт';

  @override
  String get applyFilters => 'Приложи филтри';

  @override
  String get filterCategories => 'Категории за филтриране';

  @override
  String get verifyingEmail => 'Проверяваме вашия имейл...';

  @override
  String get processingVerification => 'Обработваме проверката...';

  @override
  String get retryVerification => 'Повторете проверката';

  @override
  String get verificationFailed => 'Проверката неуспешна';

  @override
  String get checkSpamFolder => 'Не забравяйте да проверите папката със спам!';

  @override
  String get userAlreadyExists => 'Акаунт с този имейл вече съществува!';

  @override
  String get invalidCredentials => 'Невалиден имейл или парола. Моля, проверете данните си и опитайте отново.';

  @override
  String get emailNotVerified => 'Моля, потвърдете имейл адреса си преди да влезете.';

  @override
  String get weakPassword => 'Паролата трябва да е поне 8 символа и да съдържа главни, малки букви и цифри.';

  @override
  String get invalidEmailFormat => 'Моля, въведете валиден имейл адрес.';

  @override
  String get connectionTimeout => 'Изтече времето за връзка. Моля, проверете интернет връзката си и опитайте отново.';

  @override
  String get noInternet => 'Няма интернет връзка. Моля, проверете мрежовите си настройки.';

  @override
  String get userNotFound => 'Потребителят не е намерен. Моля, проверете данните си или се регистрирайте.';

  @override
  String get resourceNotFound => 'Заявеният ресурс не е намерен.';

  @override
  String get serverError => 'Грешка в сървъра. Моля, опитайте отново по-късно.';

  @override
  String get validationError => 'Неуспешна валидация. Моля, проверете въведените данни.';

  @override
  String get networkError => 'Възникна неочаквана грешка.';

  @override
  String get accessDenied => 'Достъпът е отказан.';

  @override
  String get passwordRequirements => 'Паролата трябва да съдържа поне една главна буква, една малка буква и една цифра';

  @override
  String get requirementMinLength => 'Поне 6 символа';

  @override
  String get requirementUppercase => 'Поне една главна буква (A-Z)';

  @override
  String get requirementNumber => 'Поне една цифра (0-9)';

  @override
  String get requirementSpecialChar => 'Поне един специален символ (!@#\$%^&*)';

  @override
  String get passwordMustContainUppercase => 'Паролата трябва да съдържа поне една главна буква';

  @override
  String get passwordMustContainNumber => 'Паролата трябва да съдържа поне една цифра';

  @override
  String get passwordMustContainSpecialChar => 'Паролата трябва да съдържа поне един специален символ';

  @override
  String get dangerousBuildingOrSite => 'Опасна сграда или съоръжение';

  @override
  String get streetLight => 'Неработещо улично осветление';

  @override
  String get trash => 'Боклук/сметище';

  @override
  String get dangerousPlayground => 'Опасна детска площадка';

  @override
  String get pothole => 'Опасна улична дупка';

  @override
  String get brokenOrMissingSign => 'Повредена /липсваща табела/пътен знак';

  @override
  String get illegalParking => 'Неправилно паркиране';

  @override
  String get hangingCables => 'Опасни висящи кабели';

  @override
  String get waterLeakage => 'Теч на вода';

  @override
  String get fallenTree => 'Паднало дърво';

  @override
  String get collapsedRoad => 'Пропаднал път';

  @override
  String get illegalBuilding => 'Незаконно строителство';

  @override
  String get trafficLightNotWorking => 'Неработещ светофар';

  @override
  String get strayDogs => 'Безстопанствени кучета';

  @override
  String get publicOrder => 'Нарушаване на обществения ред/шум от заведения';

  @override
  String get abandonedCar => 'Изоставен автомобил';

  @override
  String get other => 'Друго';

  @override
  String get title => 'Заглавие';

  @override
  String get typeOfSignal => 'Тип на сигнала';

  @override
  String get phone => 'Телефон';

  @override
  String get address => 'Адрес';

  @override
  String get files => 'Файлове';

  @override
  String get addFiles => 'Добави файлове';

  @override
  String get submitReport => 'Изпрати сигнал';

  @override
  String get noFilesSelected => 'Не сте избрали файлове';

  @override
  String get titleIsRequired => 'Заглавието е задължително';

  @override
  String get phoneIsRequired => 'Телефонът е задължителен';

  @override
  String get typeOfSignalIsRequired => 'Типът на сигнала е задължителен';

  @override
  String get reportSubmittedSuccessfully => 'Сигналът е изпратен успешно!';

  @override
  String get reportSubmissionFailed => 'Изпращането на сигнала неуспешно. Моля, опитайте отново.';

  @override
  String get reportAnIssue => 'Изпрати сигнал';

  @override
  String get loginRequired => 'Входа е задължителен за да изпратите сигнал';

  @override
  String get pleaseLoginToSubmitReports => 'Моля, влезте в профила си, за да изпратите сигнал';

  @override
  String get selectSource => 'Изберете начин';

  @override
  String get camera => 'Камера';

  @override
  String get gallery => 'Галерия';

  @override
  String get passwordResetEmailSent => 'Имейлът за нулиране на паролата е изпратен успешно!';

  @override
  String get resetYourPassword => 'Нулиране на Паролата';

  @override
  String get enterEmailToReset => 'Въведете вашия имейл адрес и ще ви изпратим връзка за нулиране на паролата.';

  @override
  String get sendResetLink => 'Изпрати Връзка за Нулиране';

  @override
  String get rememberYourPassword => 'Помните си паролата?';

  @override
  String get backToLogin => 'Обратно към Вход';

  @override
  String get emailAddress => 'Имейл Адрес';

  @override
  String get errorSendingResetEmail => 'Възникна грешка при изпращане на имейла за нулиране на паролата. Моля, опитайте отново.';

  @override
  String get linkExpired => 'Връзката е изтекла';

  @override
  String get resetLinkExpiredMessage => 'Тази връзка за нулиране на паролата е изтекла. Моля, заявете нова.';

  @override
  String get invalidLink => 'Невалидна връзка';

  @override
  String get invalidLinkMessage => 'Тази връзка за нулиране на паролата е невалидна. Моля, заявете нова.';

  @override
  String get requestNewLink => 'Заяви нова връзка';

  @override
  String get passwordResetSuccess => 'Паролата е нулирана успешно!';

  @override
  String get passwordResetError => 'Неуспешно нулиране на паролата. Моля, опитайте отново.';

  @override
  String get resetPassword => 'Нулиране на парола';

  @override
  String get createNewPassword => 'Създайте нова парола';

  @override
  String get enterNewPasswordMessage => 'Въведете вашата нова парола по-долу. Уверете се, че е силна и сигурна.';

  @override
  String get newPassword => 'Нова парола';

  @override
  String get confirmNewPassword => 'Потвърдете новата парола';

  @override
  String get resetPasswordButton => 'Нулирай паролата';

  @override
  String get repairStatusOngoing => '🔧 В ход';

  @override
  String get repairStatusCompleted => '✅ Завършен';

  @override
  String get repairStatusScheduled => '📅 Планиран';

  @override
  String get notSpecified => 'Не е посочено';

  @override
  String get unknownRepair => 'Неизвестен ремонт';

  @override
  String get start => 'Начало';

  @override
  String get end => 'Край';

  @override
  String get noDescriptionAvailable => 'Няма налично описание.';

  @override
  String get repairNotFound => 'Ремонтът не е намерен';

  @override
  String get loadingError => 'Грешка при зареждане';

  @override
  String get notifications => 'Известия';

  @override
  String get allEnabled => 'Всички включени';

  @override
  String get newsOnly => 'Само новини';

  @override
  String get eventsOnly => 'Само събития';

  @override
  String get allDisabled => 'Всички изключени';

  @override
  String get repairsOnly => 'Само ремонти';

  @override
  String get systemPermissionRequired => 'Необходимо е системно разрешение';

  @override
  String get systemPermissionMessage => 'Включете известията в настройките на телефона си, за да получавате уведомления.';

  @override
  String get enableNotifications => 'Включи известия';

  @override
  String get enableNotificationsMessage => 'За да получавате известия, трябва да разрешите известията в настройките на телефона си. Искате ли да ги включите сега?';

  @override
  String get enable => 'Включи';

  @override
  String get cancel => 'Откажи';

  @override
  String get selectABusStop => 'Избери спирка';

  @override
  String get busStop => 'Спирка';

  @override
  String get busStopNotFound => 'Спирката не е намерена';

  @override
  String get busStopNotSelected => 'Не сте избрали спирка';

  @override
  String get busStopSelected => 'Спирката е избрана';

  @override
  String get bussArrivals => 'Пристигане на автобуса';

  @override
  String get now => 'Сега';

  @override
  String get min => 'мин';

  @override
  String get noBusArrivals => 'Няма пристигане на автобуса';

  @override
  String get noBusArrivalsMessage => 'Няма пристигане на автобуса в тази спирка в момента.';

  @override
  String get scheduled => 'Планирано';

  @override
  String get tapForBusArrivals => 'Натиснете за да видите пристигане на автобуса';

  @override
  String get live => 'НА ЖИВО';

  @override
  String get offline => 'ОФЛАЙН';

  @override
  String get stream_active => 'Активен поток';

  @override
  String get connection_failed => 'Неуспешна връзка';

  @override
  String get real_time_video_feed => 'Видео поток в реално време';

  @override
  String get unable_to_connect => 'Неуспешно свързване с камерата';

  @override
  String get camera_controls => 'Управление на камерата';

  @override
  String get refresh => 'Обнови';

  @override
  String get full_screen => 'Цял екран';

  @override
  String get exit_full_screen => 'Изход от цял екран';

  @override
  String get loading_stream => 'Зареждане на потока...';

  @override
  String get connection_failed_title => 'Неуспешна връзка';

  @override
  String get tap_refresh_to_try_again => 'Натиснете обнови за да опитате отново';

  @override
  String get help_text => 'Това е поток на живо от центъра на град Ямбол. Използвайте контролите по-горе за да обновите.';

  @override
  String get yambol_live_camera => 'Камера на живо Ямбол';

  @override
  String get city_center => 'Градски център';

  @override
  String get live_stream => 'Поток на живо';

  @override
  String get airQuality => 'Качество на въздуха';

  @override
  String get airQualityDescription => 'Дишай спокойно! Проследи качеството на въздуха и вземи грижа за здравето си.';

  @override
  String get blueZoneParkingMessage => 'Това е синя зона за паркиране. Може да се прилагат такси почасово.';

  @override
  String get freeParkingMessage => 'Това е безплатно място за паркиране. Няма такси.';

  @override
  String get parkingDetails => 'Детайли за паркирането';

  @override
  String get blueZone => 'Синя зона';

  @override
  String get freeParking => 'Безплатно паркиране';

  @override
  String get parkingZone => 'Зона за паркиране';

  @override
  String get parkingInformations => 'Информация за паркирането';

  @override
  String get air_quality_zone => 'Зона за качество на въздуха';

  @override
  String get weather_station => 'Метеостанция';

  @override
  String get loading_details => 'Зареждане на детайли...';

  @override
  String get loading_sensor_data => 'Зареждане на данни от сензори...';

  @override
  String get error_loading_data => 'Грешка при зареждане на данни';

  @override
  String get error_loading_sensor_data => 'Грешка при зареждане на данни от сензори';

  @override
  String get try_again => 'Опитай отново';

  @override
  String get no_sensor_data_available => 'Няма налични данни от желаните сензори';

  @override
  String get sensor_humidity => 'Влажност на въздуха';

  @override
  String get sensor_temperature => 'Температура на въздуха';

  @override
  String get sensor_pressure => 'Атмосферно налягане';

  @override
  String get sensor_pm25 => 'ФПЧ 2.5';

  @override
  String get sensor_pm10 => 'ФПЧ 10.0';

  @override
  String get sensor_radiation => 'Гама радиация';

  @override
  String get sensor_pm => 'Фини прахови частици';

  @override
  String get status_normal => 'Норма';

  @override
  String get status_moderate => 'Средно';

  @override
  String get status_dangerous => 'Опасно';

  @override
  String get select_air_quality_zone => 'Избери зона за качество на въздуха';

  @override
  String get loading_zones => 'Зареждане на зони...';

  @override
  String get error_loading_zones => 'Грешка при зареждане на зони';

  @override
  String zone_with_number(String number) {
    return 'Зона $number';
  }

  @override
  String number_label(String number) {
    return 'Номер: $number';
  }

  @override
  String get active => 'Активна';

  @override
  String get inactive => 'Неактивна';

  @override
  String get tap_for_details => 'Натисни за детайли';
  String get recentAnnouncements => 'Скорошни съобщения';

  @override
  String get ago => 'преди';

  @override
  String get h => 'ч';

  @override
  String get m => 'мин';

  @override
  String get day => 'ден';

  @override
  String get days => 'дена';

  @override
  String get hour => 'час';

  @override
  String get hours => 'часа';

  @override
  String get minute => 'минута';

  @override
  String get minutes => 'минути';

  @override
  String get markAllAsRead => 'Маркирай всички като прочетени';

  @override
  String get loadingAnnouncements => 'Зареждане на съобщения...';

  @override
  String get failedToLoadAnnouncements => 'Неуспешно зареждане на съобщенията';

  @override
  String get pleaseCheckYourConnection => 'Моля, проверете своята интернет връзка и опитайте отново.';

  @override
  String get noAnnouncementsMessage => 'Все още нямате съобщения.\nПроверете отново по-късно.';

  @override
  String get nouAnnouncements => 'Няма съобщения';

  @override
  String get announcementHeaderMessage => 'Важни съобщения от Община Ямбол';
}
