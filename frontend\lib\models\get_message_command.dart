class MessageModel {
  final int id;
  final String title;
  final String content;
  final DateTime date;

  MessageModel({
    required this.id,
    required this.title,
    required this.content,
    required this.date,
  });

  factory MessageModel.fromJson(Map<String, dynamic> json) {
    return MessageModel(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      content: json['content'] ?? '',
      date: DateTime.tryParse(json['date'] ?? '') ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'date': date.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'MessageModel(id: $id, title: $title, content: $content, date: $date)';
  }
}
