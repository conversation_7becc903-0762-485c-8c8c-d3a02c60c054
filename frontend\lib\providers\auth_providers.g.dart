// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$authNotifierHash() => r'bfbdd799b9a7b7bcacab90b767b15fc1b148737d';

/// See also [AuthNotifier].
@ProviderFor(AuthNotifier)
final authNotifierProvider =
    AutoDisposeAsyncNotifierProvider<AuthNotifier, void>.internal(
  AuthNotifier.new,
  name: r'authNotifierProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$authNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AuthNotifier = AutoDisposeAsyncNotifier<void>;
String _$userAuthNotifierHash() => r'228f054f526495006809ae0e5154fc34335290aa';

/// See also [UserAuthNotifier].
@ProviderFor(UserAuthNotifier)
final userAuthNotifierProvider =
    AutoDisposeNotifierProvider<UserAuthNotifier, UserAuthState>.internal(
  UserAuthNotifier.new,
  name: r'userAuthNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userAuthNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UserAuthNotifier = AutoDisposeNotifier<UserAuthState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
