import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/l10n/generated/l10n.dart';
import 'package:frontend/presentation/screens/air-quality/air_quality_details.dart';
import 'package:frontend/providers/air_quality_provider.dart';
import 'package:frontend/models/air_quality_locations_command.dart';
import 'package:frontend/models/air_quality_zone_command.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'dart:ui' as ui;
import 'package:google_maps_flutter/google_maps_flutter.dart' as gmaps;

// Constants
class AirQualityConstants {
  static const LatLng yambolCenter = LatLng(42.4842, 26.5035);
  static const double defaultZoom = 14.0;
  static const double selectedStationZoom = 16.0;
  static const double stationIconSize = 45.0;
  static const String darkMapStylePath = 'assets/map_styles/dark_map.json';
  static const String lightMapStylePath = 'assets/map_styles/light_map.json';
}

class AirQualityScreen extends ConsumerStatefulWidget {
  const AirQualityScreen({super.key});

  @override
  ConsumerState<AirQualityScreen> createState() => _AirQualityScreenState();
}

class _AirQualityScreenState extends ConsumerState<AirQualityScreen> {
  GoogleMapController? _mapController;
  String? _mapStyle;
  BitmapDescriptor? _airQualityIcon;
  Set<Marker> _markers = {};

  // Track loading state
  bool _isIconLoaded = false;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_isIconLoaded) {
      _loadCustomAirQualityIcon();
    }
    _loadMapStyle();
  }

  Future<void> _loadMapStyle() async {
    if (!mounted) return;

    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final stylePath = isDarkMode
        ? AirQualityConstants.darkMapStylePath
        : AirQualityConstants.lightMapStylePath;

    try {
      final style = await rootBundle.loadString(stylePath);

      if (mounted) {
        setState(() {
          _mapStyle = style;
        });
      }
    } catch (e) {
      debugPrint("Error loading map style: $e");
      // Continue without custom style - _mapStyle remains null
    }
  }

  Future<void> _loadCustomAirQualityIcon() async {
    if (!mounted) return;

    try {
      final icon = await _createCustomAirQualityIcon();

      if (mounted) {
        setState(() {
          _airQualityIcon = icon;
          _isIconLoaded = true;
        });
      }
    } catch (e) {
      debugPrint("Error loading custom air quality icon: $e");
      // Fallback to default marker
      if (mounted) {
        setState(() {
          _airQualityIcon =
              BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue);
          _isIconLoaded = true;
        });
      }
    }
  }

  Future<BitmapDescriptor> _createCustomAirQualityIcon() async {
    final ui.PictureRecorder recorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(recorder);

    const double size = AirQualityConstants.stationIconSize;
    final double pixelRatio = MediaQuery.of(context).devicePixelRatio;
    final double bitmapSize = size * pixelRatio;

    // Draw a green circle with white border
    final Paint fillPaint = Paint()
      ..color = const Color(0xFF22D400)
      ..style = PaintingStyle.fill;

    final Paint borderPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3 * pixelRatio;

    final center = Offset(bitmapSize / 2, bitmapSize / 2);
    final radius = (bitmapSize / 2) - (3 * pixelRatio);

    canvas.drawCircle(center, radius, fillPaint);
    canvas.drawCircle(center, radius, borderPaint);

    // Draw air quality symbol (simple "AQ" text)
    final textPainter = TextPainter(
      text: TextSpan(
        text: 'AQ',
        style: TextStyle(
          color: Colors.white,
          fontSize: 12 * pixelRatio,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        center.dx - textPainter.width / 2,
        center.dy - textPainter.height / 2,
      ),
    );

    // Convert to image
    final ui.Image image = await recorder.endRecording().toImage(
          bitmapSize.toInt(),
          bitmapSize.toInt(),
        );

    final ByteData? byteData =
        await image.toByteData(format: ui.ImageByteFormat.png);
    if (byteData == null) {
      throw Exception('Failed to convert image to bytes');
    }

    final Uint8List uint8List = byteData.buffer.asUint8List();
    return gmaps.BitmapDescriptor.fromBytes(uint8List);
  }

  void _updateMarkersDisplay(
    List<AirQualityLocationModel> locations,
    List<AirQualityZoneModel> zones,
  ) {
    if (!mounted || _airQualityIcon == null) return;

    // Create a map of deviceId to zone for quick lookup
    final zoneMap = <String, AirQualityZoneModel>{};
    for (final zone in zones) {
      zoneMap[zone.number] = zone;
    }

    final Set<Marker> newMarkers = locations.map((location) {
      final zone = zoneMap[location.deviceId];
      return _createCombinedMarker(location, zone);
    }).toSet();

    if (mounted) {
      setState(() {
        _markers = newMarkers;
      });
    }
  }

  Marker _createCombinedMarker(
    AirQualityLocationModel location,
    AirQualityZoneModel? zone,
  ) {
    final zoneName = zone?.name.isNotEmpty == true
        ? zone!.name
        : S.of(context).zone_with_number(location.deviceId);

    return Marker(
      markerId: MarkerId('location_${location.deviceId}'),
      position: LatLng(location.latitude, location.longitude),
      icon: _airQualityIcon!,
      infoWindow: InfoWindow(
        title: zoneName,
        snippet: zone?.active == true
            ? S.of(context).active
            : S.of(context).tap_for_details,
      ),
      onTap: () => _showLocationDetailsModal(location),
    );
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
  }

  Future<void> _showLocationDetailsModal(
      AirQualityLocationModel location) async {
    // Update selected location in provider
    ref.read(selectedLocationProvider.notifier).selectLocation(location);

    // Show modal
    if (!mounted) return;

    await showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => AirQualityDetailsModal(deviceId: location.deviceId),
    );
  }

  Future<void> _animateToLocation(AirQualityLocationModel location) async {
    await _mapController?.animateCamera(
      CameraUpdate.newLatLngZoom(
        LatLng(location.latitude, location.longitude),
        AirQualityConstants.selectedStationZoom,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final locationsAsync = ref.watch(fetchAllLocationsProvider);
    final zonesAsync = ref.watch(fetchAllZonesProvider);
    final selectedLocation = ref.watch(selectedLocationProvider);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: Column(
        children: [
          _buildZonesDropdownHeader(
              context, isDarkMode, zonesAsync, selectedLocation),
          _buildGoogleMap(isDarkMode, locationsAsync, zonesAsync),
        ],
      ),
    );
  }

  Widget _buildZonesDropdownHeader(
    BuildContext context,
    bool isDarkMode,
    AsyncValue<List<AirQualityZoneModel>> zonesAsync,
    AirQualityLocationModel? selectedLocation,
  ) {
    return Container(
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top + 16,
        left: 16,
        right: 16,
        bottom: 16,
      ),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey.shade900 : const Color(0xFF22D400),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: zonesAsync.when(
        data: (zones) =>
            _buildZonesDropdown(context, isDarkMode, zones, selectedLocation),
        loading: () => _buildLoadingDropdown(context, isDarkMode),
        error: (error, stack) => _buildErrorDropdown(context, isDarkMode),
      ),
    );
  }

  Widget _buildZonesDropdown(
    BuildContext context,
    bool isDarkMode,
    List<AirQualityZoneModel> zones,
    AirQualityLocationModel? selectedLocation,
  ) {
    // Find the selected zone based on selected location
    AirQualityZoneModel? selectedZone;
    if (selectedLocation != null) {
      selectedZone = zones.firstWhere(
        (zone) => zone.number == selectedLocation.deviceId,
        orElse: () => zones.first,
      );
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: isDarkMode
            ? Colors.grey.shade800.withOpacity(0.9)
            : Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDarkMode ? Colors.grey.shade600 : Colors.grey.shade300,
          width: 1,
        ),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<AirQualityZoneModel>(
          isExpanded: true,
          value: selectedZone,
          hint: Text(
            S.of(context).select_air_quality_zone,
            style: GoogleFonts.roboto(
              fontSize: 16,
              color: isDarkMode ? Colors.grey.shade300 : Colors.grey[600],
            ),
          ),
          icon: Icon(
            Icons.arrow_drop_down,
            color: isDarkMode ? Colors.grey.shade300 : const Color(0xFF22D400),
          ),
          style: GoogleFonts.roboto(
            fontSize: 16,
            color: isDarkMode ? Colors.white : Colors.black87,
          ),
          dropdownColor: isDarkMode ? Colors.grey.shade800 : Colors.white,
          menuMaxHeight: MediaQuery.of(context).size.height * 0.5,
          items: zones.map(_buildDropdownItem).toList(),
          onChanged: (AirQualityZoneModel? newValue) async {
            if (newValue != null) {
              // Find the corresponding location with accurate coordinates
              final locationsAsync = ref.read(fetchAllLocationsProvider);
              await locationsAsync.when(
                data: (locations) async {
                  final matchingLocation = locations.firstWhere(
                    (loc) => loc.deviceId == newValue.number,
                    orElse: () => AirQualityLocationModel(
                      deviceId: newValue.number,
                      latitude: newValue.latitude,
                      longitude: newValue.longitude,
                    ),
                  );

                  ref
                      .read(selectedLocationProvider.notifier)
                      .selectLocation(matchingLocation);
                  await _animateToLocation(matchingLocation);
                  // Show modal after animation
                  _showLocationDetailsModal(matchingLocation);
                },
                loading: () async {
                  // If locations are still loading, use zone coordinates as fallback
                  final location = AirQualityLocationModel(
                    deviceId: newValue.number,
                    latitude: newValue.latitude,
                    longitude: newValue.longitude,
                  );
                  ref
                      .read(selectedLocationProvider.notifier)
                      .selectLocation(location);
                  await _animateToLocation(location);
                  _showLocationDetailsModal(location);
                },
                error: (error, stack) async {
                  // On error, use zone coordinates as fallback
                  final location = AirQualityLocationModel(
                    deviceId: newValue.number,
                    latitude: newValue.latitude,
                    longitude: newValue.longitude,
                  );
                  ref
                      .read(selectedLocationProvider.notifier)
                      .selectLocation(location);
                  await _animateToLocation(location);
                  _showLocationDetailsModal(location);
                },
              );
            }
          },
        ),
      ),
    );
  }

  Widget _buildLoadingDropdown(BuildContext context, bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      decoration: BoxDecoration(
        color: isDarkMode
            ? Colors.grey.shade800.withOpacity(0.9)
            : Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDarkMode ? Colors.grey.shade600 : Colors.grey.shade300,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                isDarkMode ? Colors.grey.shade300 : const Color(0xFF22D400),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Text(
            S.of(context).loading_zones,
            style: GoogleFonts.roboto(
              fontSize: 16,
              color: isDarkMode ? Colors.grey.shade300 : Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorDropdown(BuildContext context, bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      decoration: BoxDecoration(
        color: isDarkMode
            ? Colors.grey.shade800.withOpacity(0.9)
            : Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDarkMode ? Colors.grey.shade600 : Colors.grey.shade300,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              S.of(context).error_loading_zones,
              style: GoogleFonts.roboto(
                fontSize: 16,
                color: isDarkMode ? Colors.grey.shade300 : Colors.grey[600],
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              ref.invalidate(fetchAllZonesProvider);
              ref.invalidate(fetchAllLocationsProvider);
            },
            child: Text(
              S.of(context).try_again,
              style: TextStyle(
                color:
                    isDarkMode ? Colors.grey.shade300 : const Color(0xFF22D400),
              ),
            ),
          ),
        ],
      ),
    );
  }

  DropdownMenuItem<AirQualityZoneModel> _buildDropdownItem(
      AirQualityZoneModel zone) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return DropdownMenuItem<AirQualityZoneModel>(
      value: zone,
      child: Row(
        children: [
          Icon(
            Icons.air,
            color: isDarkMode ? Colors.grey.shade300 : const Color(0xFF22D400),
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  zone.name.isNotEmpty
                      ? zone.name
                      : S.of(context).zone_with_number(zone.number),
                  style: TextStyle(
                    color: isDarkMode ? Colors.white : Colors.black87,
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: zone.active
                  ? Colors.green.withOpacity(0.2)
                  : Colors.grey.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              zone.active ? S.of(context).active : S.of(context).inactive,
              style: TextStyle(
                fontSize: 12,
                color: zone.active ? Colors.green : Colors.grey,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGoogleMap(
    bool isDarkMode,
    AsyncValue<List<AirQualityLocationModel>> locationsAsync,
    AsyncValue<List<AirQualityZoneModel>> zonesAsync,
  ) {
    // Update markers when both locations and zones are loaded
    if (_airQualityIcon != null && mounted) {
      locationsAsync.whenData((locations) {
        zonesAsync.whenData((zones) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _updateMarkersDisplay(locations, zones);
          });
        });
      });
    }

    return Expanded(
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade300,
            width: 0.5,
          ),
        ),
        child: GoogleMap(
          onMapCreated: _onMapCreated,
          initialCameraPosition: const CameraPosition(
            target: AirQualityConstants.yambolCenter,
            zoom: AirQualityConstants.defaultZoom,
          ),
          markers: _markers,
          style: _mapStyle,
          mapType: MapType.normal,
          myLocationEnabled: true,
          myLocationButtonEnabled: true,
          compassEnabled: true,
          zoomControlsEnabled: true,
          scrollGesturesEnabled: true,
          zoomGesturesEnabled: true,
          tiltGesturesEnabled: true,
          rotateGesturesEnabled: true,
          mapToolbarEnabled: false,
          buildingsEnabled: false,
          trafficEnabled: false,
          liteModeEnabled: false,
          gestureRecognizers: _buildGestureRecognizers(),
        ),
      ),
    );
  }

  Set<Factory<OneSequenceGestureRecognizer>> _buildGestureRecognizers() {
    return <Factory<OneSequenceGestureRecognizer>>{
      Factory<PanGestureRecognizer>(() => PanGestureRecognizer()),
      Factory<ScaleGestureRecognizer>(() => ScaleGestureRecognizer()),
      Factory<TapGestureRecognizer>(() => TapGestureRecognizer()),
      Factory<VerticalDragGestureRecognizer>(
          () => VerticalDragGestureRecognizer()),
      Factory<HorizontalDragGestureRecognizer>(
          () => HorizontalDragGestureRecognizer()),
    };
  }

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }
}
