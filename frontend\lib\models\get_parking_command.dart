class BorderPoint {
  final int index;
  final double latitude;
  final double longitude;
  final int parkingLotId;

  BorderPoint({
    required this.index,
    required this.latitude,
    required this.longitude,
    required this.parkingLotId,
  });

  factory BorderPoint.fromJson(Map<String, dynamic> json) {
    return BorderPoint(
      index: json['index'] ?? 0,
      latitude: (json['latitude'] ?? 0).toDouble(),
      longitude: (json['longitude'] ?? 0).toDouble(),
      parkingLotId: json['parkingLotId'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'index': index,
      'latitude': latitude,
      'longitude': longitude,
      'parkingLotId': parkingLotId,
    };
  }

  @override
  String toString() {
    return 'BorderPoint(index: $index, latitude: $latitude, longitude: $longitude, parkingLotId: $parkingLotId)';
  }
}

class ParkingModel {
  final int id;
  final double latitude;
  final double longitude;
  final List<BorderPoint> borderPoints;

  ParkingModel({
    required this.id,
    required this.latitude,
    required this.longitude,
    required this.borderPoints,
  });

  factory ParkingModel.fromJson(Map<String, dynamic> json) {
    return ParkingModel(
      id: json['id'] ?? 0,
      latitude: (json['latitude'] ?? 0).toDouble(),
      longitude: (json['longitude'] ?? 0).toDouble(),
      borderPoints: (json['borderPoints'] as List<dynamic>?)
              ?.map((point) =>
                  BorderPoint.fromJson(point as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'latitude': latitude,
      'longitude': longitude,
      'borderPoints': borderPoints.map((point) => point.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'ParkingModel(id: $id, latitude: $latitude, longitude: $longitude, borderPoints: $borderPoints)';
  }
}
