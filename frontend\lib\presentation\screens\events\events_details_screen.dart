import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/presentation/widgets/html_utils.dart';
import 'package:frontend/presentation/widgets/image_carousel.dart';
import 'package:frontend/providers/events_provider.dart';
import 'package:frontend/providers/language_provider.dart';
import 'package:frontend/providers/theme_provider.dart';
import 'package:intl/intl.dart';

class EventsDetailsScreen extends ConsumerStatefulWidget {
  final int id;

  const EventsDetailsScreen({super.key, required this.id});

  @override
  ConsumerState<EventsDetailsScreen> createState() =>
      _EventsDetailsScreenState();
}

class _EventsDetailsScreenState extends ConsumerState<EventsDetailsScreen> {
  bool _isImagesLoaded = false;
  final List<String> _imageUrls = [];

  // Function to preload images
  Future<void> _preloadImages(List<String> urls) async {
    if (urls.isEmpty) {
      setState(() {
        _isImagesLoaded = true;
      });
      return;
    }

    int loadedCount = 0;

    for (final url in urls) {
      final image = Image.network(url);
      final imageProvider = image.image;

      imageProvider.resolve(const ImageConfiguration()).addListener(
            ImageStreamListener((_, __) {
              loadedCount++;
              if (loadedCount == urls.length) {
                if (mounted) {
                  setState(() {
                    _isImagesLoaded = true;
                  });
                }
              }
            }, onError: (_, __) {
              loadedCount++;
              if (loadedCount == urls.length) {
                if (mounted) {
                  setState(() {
                    _isImagesLoaded = true;
                  });
                }
              }
            }),
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeMode = ref.watch(themeNotifierProvider);
    final languageCode = ref.watch(languageNotifierProvider).languageCode;
    final isDarkMode = themeMode == ThemeMode.dark;

    // Fetch event details using the provider with language code
    final eventAsync = ref.watch(
        fetchEventByIdProvider((id: widget.id, languageCode: languageCode)));

    return Scaffold(
      body: eventAsync.when(
        data: (event) {
          if (event == null) {
            return const Center(
              child: Text(
                'Event not found',
                style: TextStyle(fontSize: 18),
              ),
            );
          }

          // Process images only once when data is received
          if (_imageUrls.isEmpty) {
            if (event.images != null && event.images!.isNotEmpty) {
              var coverImage = event.images!.firstWhere(
                (image) => image.isCover == true,
                orElse: () => event.images!.first,
              );

              if (coverImage.preSignedUrl != null) {
                _imageUrls.add(coverImage.preSignedUrl!);
              }

              _imageUrls.addAll(event.images!
                  .where((image) =>
                      image.isCover != true && image.preSignedUrl != null)
                  .map((image) => image.preSignedUrl!));
            }

            // Start preloading images
            _preloadImages(_imageUrls);
          }

          // Show loading indicator until all images are loaded
          if (!_isImagesLoaded) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          // Check if we have images
          final hasImages = _imageUrls.isNotEmpty;

          // Format the date for display
          String formattedDate = '';
          if (event.startDate != null) {
            formattedDate =
                DateFormat('dd.MM.yyyy г.').format(event.startDate!);
          }

          // Once everything is loaded, show the content
          return Column(
            children: [
              // Image Carousel - Only if we have images
              if (hasImages) ImageCarousel(imageUrls: _imageUrls),

              // Heading Section - Centered with date
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(
                  horizontal: 16.0,
                  // Adjust vertical padding based on image presence
                  vertical: hasImages ? 12.0 : 32.0,
                ),
                child: Column(
                  children: [
                    Text(
                      event.name ?? "Unknown Event",
                      style: TextStyle(
                        fontFamily: 'Roboto',
                        fontWeight: FontWeight.w700,
                        fontSize: 22,
                        color:
                            isDarkMode ? Colors.white : const Color(0xFF424242),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    // Add date below the title if available
                    if (formattedDate.isNotEmpty) ...[
                      SizedBox(height: 8),
                      Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: Color(0xFF22D400),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Text(
                          formattedDate,
                          style: TextStyle(
                            fontFamily: 'Roboto',
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),

              // Divider for visual separation
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Divider(
                  thickness: 1.2,
                  color: isDarkMode ? Colors.white24 : Colors.black12,
                ),
              ),

              // Enhanced Description Section using HtmlUtils
              Expanded(
                child: Center(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16.0, vertical: 10),
                    child: Container(
                      width: double.infinity,
                      constraints: BoxConstraints(
                        // Adjust minimum height based on image presence
                        minHeight: hasImages ? 200 : 300,
                      ),
                      decoration: BoxDecoration(
                        color:
                            isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: isDarkMode
                                ? Colors.black.withOpacity(0.3)
                                : Colors.black.withOpacity(0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      margin: EdgeInsets.only(
                        // Adjust bottom margin based on image presence
                        bottom: 16,
                        // Add top margin when no images to reduce space
                        top: hasImages ? 0 : 16,
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: HtmlUtils.createStyledHtmlWidget(
                          htmlContent:
                              event.description ?? "No description available.",
                          isDarkMode: isDarkMode,
                          baseFontSize: 15.0,
                          lineHeight: 1.6,
                          fontFamily: 'Roboto',
                          onLinkTap: (url, attributes, element) {
                            // Handle link navigation here
                            // You might want to use url_launcher or navigate to other screens
                            print('Link tapped: $url');
                          },
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          );
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stack) => Center(child: Text("Error: $error")),
      ),
    );
  }
}
