// Simple location model for map markers
class AirQualityLocationModel {
  final String deviceId;
  final double latitude;
  final double longitude;

  AirQualityLocationModel({
    required this.deviceId,
    required this.latitude,
    required this.longitude,
  });

  factory AirQualityLocationModel.fromMapEntry(
      MapEntry<String, dynamic> entry) {
    return AirQualityLocationModel(
      deviceId: entry.key,
      latitude: (entry.value['latitude'] ?? 0).toDouble(),
      longitude: (entry.value['longitude'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'deviceId': deviceId,
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  @override
  String toString() {
    return 'AirQualityLocationModel(deviceId: $deviceId, lat: $latitude, lon: $longitude)';
  }
}
