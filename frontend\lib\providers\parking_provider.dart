import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/models/get_parking_command.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:frontend/infrastructure/parking/parking_service.dart';

part 'parking_provider.g.dart';

// Provider to fetch all parking lots
@riverpod
Future<List<ParkingModel>> fetchAllParkingLots(Ref ref) async {
  print("Fetching all parking lots");
  return ParkingService.fetchParkingLots();
}
