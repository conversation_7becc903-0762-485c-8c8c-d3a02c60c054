import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/providers/theme_provider.dart';
import 'filter_chips.dart';
import 'search_bar.dart';

class FavoritesScreenHeader extends ConsumerWidget {
  final TextEditingController searchController;

  const FavoritesScreenHeader({super.key, required this.searchController});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isDarkMode = ref.watch(themeNotifierProvider) == ThemeMode.dark;
    final screenSize = MediaQuery.of(context).size;
    final isLandscape = screenSize.width > screenSize.height;

    // Responsive height calculation
    final headerHeight = isLandscape
        ? screenSize.height * 0.35 // Smaller percentage in landscape
        : screenSize.height * 0.24; // Original height in portrait

    // Minimum and maximum height constraints
    final clampedHeight = headerHeight.clamp(120.0, 200.0);

    return Container(
      width: double.infinity,
      height: clampedHeight,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Stack(
        children: [
          // Background Image
          Container(
            width: screenSize.width,
            height: clampedHeight,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              image: DecorationImage(
                image: const AssetImage('assets/images/settings_image.png'),
                fit: BoxFit.cover,
                colorFilter: ColorFilter.mode(
                  isDarkMode
                      ? Colors.white.withOpacity(0.3)
                      : const Color(0x3322D400),
                  BlendMode.darken,
                ),
              ),
            ),
          ),

          // Search Bar - Responsive positioning
          Positioned.fill(
            child: Align(
              alignment: isLandscape
                  ? const Alignment(0.0, -0.3) // Higher position in landscape
                  : const Alignment(0.0, -0.1), // Original position in portrait
              child: Padding(
                padding: EdgeInsets.symmetric(
                  horizontal:
                      isLandscape ? 32.0 : 20.0, // More padding in landscape
                ),
                child: SearchBarWidget(
                  controller: searchController,
                  isDarkMode: isDarkMode,
                ),
              ),
            ),
          ),

          // Filter Chips - Responsive positioning
          Positioned(
            bottom: isLandscape ? 8 : 14, // Less bottom padding in landscape
            left: 0,
            right: 0,
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal:
                    isLandscape ? 24.0 : 16.0, // More padding in landscape
              ),
              child: const FavoritesFilterChips(),
            ),
          ),
        ],
      ),
    );
  }
}
