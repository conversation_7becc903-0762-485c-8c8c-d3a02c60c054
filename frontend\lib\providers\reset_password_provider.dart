import 'package:frontend/infrastructure/forgot-password/reset_password.service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'reset_password_provider.g.dart';

// Reset password state
@riverpod
class ResetPasswordState extends _$ResetPasswordState {
  @override
  AsyncValue<bool> build() => const AsyncValue.data(false);

  Future<void> resetPassword({
    required String id,
    required String email,
    required String expires,
    required String signature,
    required String newPassword,
    required String confirmPassword,
  }) async {
    state = const AsyncValue.loading();

    try {
      final success = await ResetPasswordService.resetPassword(
        id: id,
        email: email,
        expires: expires,
        signature: signature,
        newPassword: newPassword,
        confirmPassword: confirmPassword,
      );

      state = AsyncValue.data(success);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  void reset() {
    state = const AsyncValue.data(false);
  }
}
