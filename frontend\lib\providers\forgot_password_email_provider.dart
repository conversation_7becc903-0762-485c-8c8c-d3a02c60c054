import 'package:frontend/infrastructure/forgot-password/forgot_password.service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'forgot_password_email_provider.g.dart';

// Forgot password state
@riverpod
class ForgotPasswordState extends _$ForgotPasswordState {
  @override
  AsyncValue<bool> build() => const AsyncValue.data(false);

  Future<void> sendForgotPasswordEmail({
    required String email,
    required String languageCode,
  }) async {
    state = const AsyncValue.loading();

    try {
      final success = await ForgotPasswordService.sendForgotPasswordEmail(
        email: email,
        languageCode: languageCode,
      );

      state = AsyncValue.data(success);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  void reset() {
    state = const AsyncValue.data(false);
  }
}
