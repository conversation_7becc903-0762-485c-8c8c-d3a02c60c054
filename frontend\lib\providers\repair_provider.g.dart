// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'repair_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchAllRepairsHash() => r'49ce66715d8aeeff4e429a6c02e89a8c99ed7409';

/// See also [fetchAllRepairs].
@ProviderFor(fetchAllRepairs)
final fetchAllRepairsProvider =
    AutoDisposeFutureProvider<List<RepairModel>>.internal(
  fetchAllRepairs,
  name: r'fetchAllRepairsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchAllRepairsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchAllRepairsRef = AutoDisposeFutureProviderRef<List<RepairModel>>;
String _$fetchRepairByIdHash() => r'd732cdf29dd1e1d7dce62dac7eb7b0fc2957e229';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [fetchRepairById].
@ProviderFor(fetchRepairById)
const fetchRepairByIdProvider = FetchRepairByIdFamily();

/// See also [fetchRepairById].
class FetchRepairByIdFamily extends Family<AsyncValue<RepairDetailsModel?>> {
  /// See also [fetchRepairById].
  const FetchRepairByIdFamily();

  /// See also [fetchRepairById].
  FetchRepairByIdProvider call(
    ({int id, String languageCode}) params,
  ) {
    return FetchRepairByIdProvider(
      params,
    );
  }

  @override
  FetchRepairByIdProvider getProviderOverride(
    covariant FetchRepairByIdProvider provider,
  ) {
    return call(
      provider.params,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchRepairByIdProvider';
}

/// See also [fetchRepairById].
class FetchRepairByIdProvider
    extends AutoDisposeFutureProvider<RepairDetailsModel?> {
  /// See also [fetchRepairById].
  FetchRepairByIdProvider(
    ({int id, String languageCode}) params,
  ) : this._internal(
          (ref) => fetchRepairById(
            ref as FetchRepairByIdRef,
            params,
          ),
          from: fetchRepairByIdProvider,
          name: r'fetchRepairByIdProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$fetchRepairByIdHash,
          dependencies: FetchRepairByIdFamily._dependencies,
          allTransitiveDependencies:
              FetchRepairByIdFamily._allTransitiveDependencies,
          params: params,
        );

  FetchRepairByIdProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.params,
  }) : super.internal();

  final ({int id, String languageCode}) params;

  @override
  Override overrideWith(
    FutureOr<RepairDetailsModel?> Function(FetchRepairByIdRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchRepairByIdProvider._internal(
        (ref) => create(ref as FetchRepairByIdRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        params: params,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<RepairDetailsModel?> createElement() {
    return _FetchRepairByIdProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchRepairByIdProvider && other.params == params;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, params.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchRepairByIdRef on AutoDisposeFutureProviderRef<RepairDetailsModel?> {
  /// The parameter `params` of this provider.
  ({int id, String languageCode}) get params;
}

class _FetchRepairByIdProviderElement
    extends AutoDisposeFutureProviderElement<RepairDetailsModel?>
    with FetchRepairByIdRef {
  _FetchRepairByIdProviderElement(super.provider);

  @override
  ({int id, String languageCode}) get params =>
      (origin as FetchRepairByIdProvider).params;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
