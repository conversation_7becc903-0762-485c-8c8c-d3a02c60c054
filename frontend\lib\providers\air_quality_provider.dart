import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/infrastructure/air-quality/air_quality.service.dart';
import 'package:frontend/models/air_quality_locations_command.dart';
import 'package:frontend/models/air_quality_sensor_reading_command.dart';
import 'package:frontend/models/air_quality_zone_command.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'air_quality_provider.g.dart';

// Provider to fetch current sensor readings for a specific device
@riverpod
Future<List<SensorReadingModel>> fetchCurrentReadings(
  Ref ref, {
  required String deviceNumber,
  int every = 1,
}) async {
  print("Fetching current readings for device: $deviceNumber");
  return AirQualityService.fetchCurrentReadings(
      deviceNumber: deviceNumber, every: every);
}

// Provider to fetch all air quality locations (lightweight for map)
@riverpod
Future<List<AirQualityLocationModel>> fetchAllLocations(Ref ref) async {
  print("Fetching all air quality locations");
  return AirQualityService.fetchAllLocations();
}

// Provider to fetch all air quality zones (for names and details)
@riverpod
Future<List<AirQualityZoneModel>> fetchAllZones(Ref ref) async {
  print("Fetching all air quality zones");
  return AirQualityService.fetchAllZones();
}

// Provider to fetch zone details for a specific device number
@riverpod
Future<AirQualityZoneModel?> fetchZoneDetails(
  Ref ref, {
  required String deviceNumber,
}) async {
  print("Fetching zone details for device: $deviceNumber");
  return AirQualityService.fetchZoneDetails(deviceNumber: deviceNumber);
}

// Provider for selected air quality location
@riverpod
class SelectedLocation extends _$SelectedLocation {
  @override
  AirQualityLocationModel? build() => null;

  void selectLocation(AirQualityLocationModel? location) {
    state = location;
  }

  void clearSelection() {
    state = null;
  }
}
