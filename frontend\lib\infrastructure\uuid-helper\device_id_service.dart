import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

class DeviceIdService {
  static const _key = 'client_id';

  static Future<String> getOrCreateDeviceId() async {
    final prefs = await SharedPreferences.getInstance();
    String? existingId = prefs.getString(_key);

    if (existingId != null && existingId.isNotEmpty) {
      return existingId;
    }

    // Generate a new UUID
    final newId = const Uuid().v4();
    await prefs.setString(_key, newId);
    return newId;
  }
}
