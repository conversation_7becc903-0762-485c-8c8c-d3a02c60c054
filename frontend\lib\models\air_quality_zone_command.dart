// Main Air Quality Zone Model
import 'package:frontend/models/air_quality_config_command.dart';

class AirQualityZoneModel {
  final bool active;
  final int companyIdToLinkDevice;
  final AirQualityConfig config;
  final String configuration;
  final int countsThreshold;
  final String createdBy;
  final DateTime createdOn;
  final CustomerInfo customerInfo;
  final String description;
  final String deviceIcon;
  final DeviceIconDto deviceIconDto;
  final int every;
  final String hwVersion;
  final bool isConfigured;
  final double latitude;
  final double longitude;
  final MeterInfo meterInfo;
  final int missedTransmissionSeverity;
  final String model;
  final String name;
  final String nomenclature;
  final String number;
  final bool parent;
  final String parentNumber;
  final String recordPeriod;
  final String swVersion;
  final String timezone;

  AirQualityZoneModel({
    required this.active,
    required this.companyIdToLinkDevice,
    required this.config,
    required this.configuration,
    required this.countsThreshold,
    required this.createdBy,
    required this.createdOn,
    required this.customerInfo,
    required this.description,
    required this.deviceIcon,
    required this.deviceIconDto,
    required this.every,
    required this.hwVersion,
    required this.isConfigured,
    required this.latitude,
    required this.longitude,
    required this.meterInfo,
    required this.missedTransmissionSeverity,
    required this.model,
    required this.name,
    required this.nomenclature,
    required this.number,
    required this.parent,
    required this.parentNumber,
    required this.recordPeriod,
    required this.swVersion,
    required this.timezone,
  });

  factory AirQualityZoneModel.fromJson(Map<String, dynamic> json) {
    return AirQualityZoneModel(
      active: json['active'] ?? false,
      companyIdToLinkDevice: json['companyIdToLinkDevice'] ?? 0,
      config: AirQualityConfig.fromJson(json['config'] ?? {}),
      configuration: json['configuration'] ?? '',
      countsThreshold: json['countsThreshold'] ?? 0,
      createdBy: json['createdBy'] ?? '',
      createdOn: DateTime.tryParse(json['createdOn'] ?? '') ?? DateTime.now(),
      customerInfo: CustomerInfo.fromJson(json['customerInfo'] ?? {}),
      description: json['description'] ?? '',
      deviceIcon: json['deviceIcon'] ?? '',
      deviceIconDto: DeviceIconDto.fromJson(json['deviceIconDto'] ?? {}),
      every: json['every'] ?? 0,
      hwVersion: json['hwVersion'] ?? '',
      isConfigured: json['isConfigured'] ?? false,
      latitude: (json['latitude'] ?? 0).toDouble(),
      longitude: (json['longitude'] ?? 0).toDouble(),
      meterInfo: MeterInfo.fromJson(json['meterInfo'] ?? {}),
      missedTransmissionSeverity: json['missedTransmissionSeverity'] ?? 0,
      model: json['model'] ?? '',
      name: json['name'] ?? '',
      nomenclature: json['nomenclature'] ?? '',
      number: json['number'] ?? '',
      parent: json['parent'] ?? false,
      parentNumber: json['parentNumber'] ?? '',
      recordPeriod: json['recordPeriod'] ?? '',
      swVersion: json['swVersion'] ?? '',
      timezone: json['timezone'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'active': active,
      'companyIdToLinkDevice': companyIdToLinkDevice,
      'config': config.toJson(),
      'configuration': configuration,
      'countsThreshold': countsThreshold,
      'createdBy': createdBy,
      'createdOn': createdOn.toIso8601String(),
      'customerInfo': customerInfo.toJson(),
      'description': description,
      'deviceIcon': deviceIcon,
      'deviceIconDto': deviceIconDto.toJson(),
      'every': every,
      'hwVersion': hwVersion,
      'isConfigured': isConfigured,
      'latitude': latitude,
      'longitude': longitude,
      'meterInfo': meterInfo.toJson(),
      'missedTransmissionSeverity': missedTransmissionSeverity,
      'model': model,
      'name': name,
      'nomenclature': nomenclature,
      'number': number,
      'parent': parent,
      'parentNumber': parentNumber,
      'recordPeriod': recordPeriod,
      'swVersion': swVersion,
      'timezone': timezone,
    };
  }

  @override
  String toString() {
    return 'AirQualityZoneModel(number: $number, name: $name, latitude: $latitude, longitude: $longitude, active: $active)';
  }
}

// Customer Information Model
class CustomerInfo {
  final String address;
  final String billingGroup;
  final String block;
  final String businessPartnerNumber;
  final String contactData;
  final String contractNumber;
  final int customerTypeId;
  final String description;
  final String entrance;
  final String municipality;
  final String name;
  final String quarter;
  final String street;
  final String streetNumber;
  final String zipCode;

  CustomerInfo({
    required this.address,
    required this.billingGroup,
    required this.block,
    required this.businessPartnerNumber,
    required this.contactData,
    required this.contractNumber,
    required this.customerTypeId,
    required this.description,
    required this.entrance,
    required this.municipality,
    required this.name,
    required this.quarter,
    required this.street,
    required this.streetNumber,
    required this.zipCode,
  });

  factory CustomerInfo.fromJson(Map<String, dynamic> json) {
    return CustomerInfo(
      address: json['address'] ?? '',
      billingGroup: json['billingGroup'] ?? '',
      block: json['block'] ?? '',
      businessPartnerNumber: json['businessPartnerNumber'] ?? '',
      contactData: json['contactData'] ?? '',
      contractNumber: json['contractNumber'] ?? '',
      customerTypeId: json['customerTypeId'] ?? 0,
      description: json['description'] ?? '',
      entrance: json['entrance'] ?? '',
      municipality: json['municipality'] ?? '',
      name: json['name'] ?? '',
      quarter: json['quarter'] ?? '',
      street: json['street'] ?? '',
      streetNumber: json['streetNumber'] ?? '',
      zipCode: json['zipCode'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'address': address,
      'billingGroup': billingGroup,
      'block': block,
      'businessPartnerNumber': businessPartnerNumber,
      'contactData': contactData,
      'contractNumber': contractNumber,
      'customerTypeId': customerTypeId,
      'description': description,
      'entrance': entrance,
      'municipality': municipality,
      'name': name,
      'quarter': quarter,
      'street': street,
      'streetNumber': streetNumber,
      'zipCode': zipCode,
    };
  }
}

// Device Icon Model
class DeviceIconDto {
  final String icon;
  final int id;
  final String name;
  final String scope;

  DeviceIconDto({
    required this.icon,
    required this.id,
    required this.name,
    required this.scope,
  });

  factory DeviceIconDto.fromJson(Map<String, dynamic> json) {
    return DeviceIconDto(
      icon: json['icon'] ?? '',
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      scope: json['scope'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'icon': icon,
      'id': id,
      'name': name,
      'scope': scope,
    };
  }
}

// Meter Information Model
class MeterInfo {
  final String brand;
  final double diameter;
  final String factoryNumber;
  final String mountDate;
  final double q1;
  final double q2;
  final double q3;
  final double q4;
  final double r;

  MeterInfo({
    required this.brand,
    required this.diameter,
    required this.factoryNumber,
    required this.mountDate,
    required this.q1,
    required this.q2,
    required this.q3,
    required this.q4,
    required this.r,
  });

  factory MeterInfo.fromJson(Map<String, dynamic> json) {
    return MeterInfo(
      brand: json['brand'] ?? '',
      diameter: (json['diameter'] ?? 0).toDouble(),
      factoryNumber: json['factoryNumber'] ?? '',
      mountDate: json['mountDate'] ?? '',
      q1: (json['q1'] ?? 0).toDouble(),
      q2: (json['q2'] ?? 0).toDouble(),
      q3: (json['q3'] ?? 0).toDouble(),
      q4: (json['q4'] ?? 0).toDouble(),
      r: (json['r'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'brand': brand,
      'diameter': diameter,
      'factoryNumber': factoryNumber,
      'mountDate': mountDate,
      'q1': q1,
      'q2': q2,
      'q3': q3,
      'q4': q4,
      'r': r,
    };
  }
}
