import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:frontend/infrastructure/interceptor/global_interceptor.dart';
import 'package:frontend/models/reset_password_command.dart';

class ResetPasswordService {
  // Use global HTTP client
  static final Dio _dio = GlobalHttpClient().dio;

  /// **Reset Password**
  static Future<bool> resetPassword({
    required String id,
    required String email,
    required String expires,
    required String signature,
    required String newPassword,
    required String confirmPassword,
  }) async {
    const String resetPasswordEndpoint = 'Users/ResetForgottenPassword';

    try {
      print('RESETTING PASSWORD...');

      // Get timezone offset in minutes
      final DateTime now = DateTime.now();
      final int timezoneOffset = now.timeZoneOffset.inMinutes;

      final resetPasswordCommand = ResetPasswordCommand(
        newPassword: newPassword,
        confirmPassword: confirmPassword,
        signature: signature,
        email: email,
        id: id,
        timezoneOffset: timezoneOffset,
        expires: expires,
      );

      print(
          '📤 Reset Password Request Data: ${jsonEncode(resetPasswordCommand.toJson())}');

      final response = await _dio.post(
        resetPasswordEndpoint,
        data: resetPasswordCommand.toJson(),
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200 ||
          response.statusCode == 201 ||
          response.statusCode == 204) {
        print('✅ Password reset successfully');
        return true;
      } else {
        print('❌ Failed to reset password with status ${response.statusCode}');
        throw Exception(
            'Failed to reset password with status ${response.statusCode}');
      }
    } on DioException catch (e) {
      print("❌ Reset Password Dio Error: ${e.response?.statusCode}");
      print("📩 Reset Password Response Data: ${e.response?.data}");
      print("📝 Reset Password Error Message: ${e.message}");

      // Handle specific error codes
      if (e.response?.statusCode == 400) {
        print("Bad request - invalid reset data or expired link");
      } else if (e.response?.statusCode == 404) {
        print("Reset request not found");
      } else if (e.response?.statusCode == 410) {
        print("Reset link has expired");
      }

      return false;
    } catch (e) {
      print("⚠ Reset Password Unexpected Error: $e");
      return false;
    }
  }
}
