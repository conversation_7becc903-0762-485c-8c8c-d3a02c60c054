import 'package:frontend/domain/enums/favorite_filter_type.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'favorites_filter_provider.g.dart';

@riverpod
class FavouriteState extends _$FavouriteState {
  @override
  FavoriteFilterType build() =>
      FavoriteFilterType.all; // Default to 'All' filter

  void setFilter(FavoriteFilterType filterType) => state = filterType;

  bool isSelected(FavoriteFilterType filterType) => state == filterType;

  String getFilterName() {
    switch (state) {
      case FavoriteFilterType.all:
        return 'All';
      case FavoriteFilterType.tourism:
        return 'Tourism';
      case FavoriteFilterType.weather:
        return 'Weather';
      case FavoriteFilterType.events:
        return 'Events';
      case FavoriteFilterType.repairs:
        return 'Repairs';
      case FavoriteFilterType.parking:
        return 'Parking';
      case FavoriteFilterType.generalInfo:
        return 'General Information';
      case FavoriteFilterType.traffic:
        return 'Traffic';
      case FavoriteFilterType.news:
        return 'News';
      case FavoriteFilterType.reports:
        return 'Reports';
    }
  }
}
