import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'favourites_provider.g.dart';

@riverpod
class FavouritesItems extends _$FavouritesItems {
  static const _prefsKey =
      'favourite_items'; // Changed from tourism-specific to general

  @override
  FutureOr<Set<String>> build() async {
    final prefs = await SharedPreferences.getInstance();
    final favList = prefs.getStringList(_prefsKey) ?? [];
    return favList.toSet();
  }

  Future<void> toggle(String id) async {
    final prefs = await SharedPreferences.getInstance();
    final current = state.value ?? <String>{};

    final updated =
        current.contains(id) ? (current..remove(id)) : (current..add(id));

    await prefs.setStringList(_prefsKey, updated.toList());
    state = AsyncData({...updated});
  }

  Future<bool> isFavourite(String id) async {
    final prefs = await SharedPreferences.getInstance();
    final favList = prefs.getStringList(_prefsKey) ?? [];
    return favList.contains(id);
  }
}
