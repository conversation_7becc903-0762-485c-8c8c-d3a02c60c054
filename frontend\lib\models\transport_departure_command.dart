class DepartureModel {
  final DateTime c;
  final DateTime d;
  final int? df;
  final String dir;
  final int did;
  final int dg;
  final List<String> fs;

  DepartureModel({
    required this.c,
    required this.d,
    this.df,
    required this.dir,
    required this.did,
    required this.dg,
    required this.fs,
  });

  factory DepartureModel.fromJson(Map<String, dynamic> json) {
    return DepartureModel(
      c: DateTime.parse(json['c']).toLocal(),
      d: DateTime.parse(json['d']).toLocal(),
      df: json['df'],
      dir: json['dir'],
      did: json['did'],
      dg: json['dg'],
      fs: List<String>.from(json['fs'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'c': c.toIso8601String(),
      'd': d.toIso8601String(),
      'df': df,
      'dir': dir,
      'did': did,
      'dg': dg,
      'fs': fs,
    };
  }

  // Helper method to get minutes until departure
  int get minutesUntilDeparture {
    final now = DateTime.now();
    final difference = d.difference(now);
    return difference.inMinutes;
  }

  // Helper method to check if time is by timetable
  bool get isByTimetable => fs.contains('by_timetable');

  @override
  String toString() {
    return 'DepartureModel(c: $c, d: $d, df: $df, dir: $dir, did: $did, dg: $dg, fs: $fs)';
  }
}
