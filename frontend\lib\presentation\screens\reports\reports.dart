import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/l10n/generated/l10n.dart';
import 'package:frontend/models/reports_service_command.dart';
import 'package:frontend/providers/app_providers.dart';
import 'package:frontend/providers/reports_provider.dart';
import 'package:frontend/providers/theme_provider.dart';
import 'package:frontend/providers/auth_providers.dart'; // Add this import
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:convert';

import 'package:image_picker/image_picker.dart';

class ReportsScreen extends ConsumerStatefulWidget {
  const ReportsScreen({super.key});

  @override
  ConsumerState<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends ConsumerState<ReportsScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();

  String? _selectedType;
  List<String> _reportTypes = [];
  bool _isLoadingReportTypes = true;

  Map<String, String> _localizedToApiMap = {};

  List<FileModel> _attachedFiles = [];

  @override
  void initState() {
    super.initState();
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Load report types only if user is authenticated
    final userState = ref.read(userAuthNotifierProvider);

    print('🔐 Auth Debug:');
    print('  isAuthenticated: ${userState.isAuthenticated}');
    print('  username: ${userState.username}');

    if (userState.isAuthenticated) {
      _loadReportTypes();
    }
  }

  Future<void> _loadReportTypes() async {
    try {
      final reportTypes = await ref
          .read(reportSubmissionStateProvider.notifier)
          .getReportTypes();

      if (mounted) {
        setState(() {
          if (reportTypes != null && !reportTypes.isEmpty) {
            _localizedToApiMap.clear();
            _reportTypes = reportTypes.values.map((apiType) {
              final localizedType = _getLocalizedType(context, apiType);
              _localizedToApiMap[localizedType] = apiType;
              return localizedType;
            }).toList();
            _selectedType = _reportTypes.isNotEmpty ? _reportTypes.first : null;
          } else {
            _reportTypes = _getLocalizedFallbackTypes(context);
            _buildFallbackMapping();
            _selectedType = _reportTypes.isNotEmpty ? _reportTypes.first : null;
          }
          _isLoadingReportTypes = false;
        });
      }
    } catch (e) {
      print('Error loading report types: $e');
      if (mounted) {
        setState(() {
          _reportTypes = _getLocalizedFallbackTypes(context);
          _buildFallbackMapping();
          _selectedType = _reportTypes.isNotEmpty ? _reportTypes.first : null;
          _isLoadingReportTypes = false;
        });
      }
    }
  }

  void _buildFallbackMapping() {
    _localizedToApiMap.clear();
    _localizedToApiMap[S.of(context).dangerousBuildingOrSite] =
        'DangerousBuildingOrSite';
    _localizedToApiMap[S.of(context).streetLight] = 'StreetLight';
    _localizedToApiMap[S.of(context).trash] = 'Trash';
    _localizedToApiMap[S.of(context).other] = 'Other';
  }

  List<String> _getLocalizedFallbackTypes(BuildContext context) {
    return [
      S.of(context).dangerousBuildingOrSite,
      S.of(context).streetLight,
      S.of(context).trash,
      S.of(context).other,
    ];
  }

  String _getLocalizedType(BuildContext context, String apiType) {
    switch (apiType) {
      case 'DangerousBuildingOrSite':
        return S.of(context).dangerousBuildingOrSite;
      case 'StreetLight':
        return S.of(context).streetLight;
      case 'Trash':
        return S.of(context).trash;
      case 'DangerousPlayground':
        return S.of(context).dangerousPlayground;
      case 'Pothole':
        return S.of(context).pothole;
      case 'BrokenOrMissingSign':
        return S.of(context).brokenOrMissingSign;
      case 'IllegalParking':
        return S.of(context).illegalParking;
      case 'HangingCables':
        return S.of(context).hangingCables;
      case 'WaterLeakage':
        return S.of(context).waterLeakage;
      case 'FallenTree':
        return S.of(context).fallenTree;
      case 'CollapsedRoad':
        return S.of(context).collapsedRoad;
      case 'IllegalBuilding':
        return S.of(context).illegalBuilding;
      case 'TrafficLightNotWorking':
        return S.of(context).trafficLightNotWorking;
      case 'StrayDogs':
        return S.of(context).strayDogs;
      case 'PublicOrder':
        return S.of(context).publicOrder;
      case 'AbandonedCar':
        return S.of(context).abandonedCar;
      case 'Other':
        return S.of(context).other;
      default:
        return apiType;
    }
  }

  @override
  void dispose() {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
    _titleController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _showFilePickerOptions() async {
    showModalBottomSheet(
      context: context,
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        final isDarkMode = ref.watch(themeNotifierProvider) == ThemeMode.dark;

        return Container(
          padding: EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                S
                    .of(context)
                    .selectSource, // Add to your localization: S.of(context).selectSource
                style: GoogleFonts.roboto(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: isDarkMode ? Colors.white : Color(0xFF333333),
                ),
              ),
              SizedBox(height: 20),

              // Camera option
              ListTile(
                leading: Icon(Icons.camera_alt, color: Color(0xFF22D400)),
                title: Text(
                  S
                      .of(context)
                      .camera, // Add to localization: S.of(context).camera
                  style: TextStyle(
                    color: isDarkMode ? Colors.white : Color(0xFF333333),
                  ),
                ),
                onTap: () {
                  Navigator.pop(context);
                  _pickFromCamera();
                },
              ),

              // Gallery option
              ListTile(
                leading: Icon(Icons.photo_library, color: Color(0xFF22D400)),
                title: Text(
                  S
                      .of(context)
                      .gallery, // Add to localization: S.of(context).gallery
                  style: TextStyle(
                    color: isDarkMode ? Colors.white : Color(0xFF333333),
                  ),
                ),
                onTap: () {
                  Navigator.pop(context);
                  _pickFromGallery();
                },
              ),

              // Files option
              ListTile(
                leading: Icon(Icons.folder, color: Color(0xFF22D400)),
                title: Text(
                  S.of(context).files, // This already exists
                  style: TextStyle(
                    color: isDarkMode ? Colors.white : Color(0xFF333333),
                  ),
                ),
                onTap: () {
                  Navigator.pop(context);
                  _pickFiles();
                },
              ),

              SizedBox(height: 20),
            ],
          ),
        );
      },
    );
  }

// Camera picker using image_picker
  Future<void> _pickFromCamera() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(source: ImageSource.camera);

      if (image != null) {
        final bytes = await image.readAsBytes();
        final fileModel = FileModel(
          fileName: image.name,
          contentType: _getContentType(image.path.split('.').last),
          extension: image.path.split('.').last,
          content: base64Encode(bytes),
        );

        setState(() {
          _attachedFiles.add(fileModel);
        });
      }
    } catch (e) {
      print('Error taking photo: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error taking photo: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

// Gallery picker using file_picker with media type
  Future<void> _pickFromGallery() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        allowMultiple: true,
        type: FileType.media, // This opens gallery on both platforms
        withData: true,
      );

      if (result != null && result.files.isNotEmpty) {
        for (PlatformFile file in result.files) {
          if (file.bytes != null) {
            final fileModel = FileModel(
              fileName: file.name,
              contentType: _getContentType(file.extension ?? ''),
              extension: file.extension ?? '',
              content: base64Encode(file.bytes!),
            );

            setState(() {
              _attachedFiles.add(fileModel);
            });
          }
        }
      }
    } catch (e) {
      print('Error picking from gallery: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error picking from gallery: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

// Files picker (your existing method, keep as is)
  Future<void> _pickFiles() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        allowMultiple: true,
        type: FileType.any, // Keep as 'any' for files option
        withData: true,
      );

      if (result != null && result.files.isNotEmpty) {
        for (PlatformFile file in result.files) {
          if (file.bytes != null) {
            final fileModel = FileModel(
              fileName: file.name,
              contentType: _getContentType(file.extension ?? ''),
              extension: file.extension ?? '',
              content: base64Encode(file.bytes!),
            );

            setState(() {
              _attachedFiles.add(fileModel);
            });
          }
        }
      }
    } catch (e) {
      print('Error picking files: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error picking files: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _getContentType(String extension) {
    switch (extension.toLowerCase()) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'pdf':
        return 'application/pdf';
      case 'doc':
        return 'application/msword';
      case 'docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      default:
        return 'application/octet-stream';
    }
  }

  void _removeFile(int index) {
    setState(() {
      _attachedFiles.removeAt(index);
    });
  }

  void _submitReport() {
    if (_formKey.currentState!.validate() && _selectedType != null) {
      final apiType = _localizedToApiMap[_selectedType!] ?? _selectedType!;

      ref.read(reportSubmissionStateProvider.notifier).submitReport(
            title: _titleController.text.trim(),
            phoneNumber: _phoneController.text.trim(),
            description: _descriptionController.text.trim(),
            address: _addressController.text.trim(),
            type: apiType,
            files: _attachedFiles,
          );
    }
  }

  // BUILD THE PLEASE LOGIN SCREEN
  Widget _buildLoginRequiredScreen(bool isDarkMode) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Icon
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  color: Color(0xFF22D400).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(50),
                  border: Border.all(
                    color: Color(0xFF22D400).withValues(alpha: 0.3),
                    width: 2,
                  ),
                ),
                child: Icon(
                  Icons.login,
                  size: 48,
                  color: Color(0xFF22D400),
                ),
              ),

              SizedBox(height: 32),

              // Title
              Text(
                S.of(context).loginRequired,
                style: GoogleFonts.roboto(
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                  color: isDarkMode ? Colors.white : Color(0xFF333333),
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: 16),

              // Message
              Text(
                S.of(context).pleaseLoginToSubmitReports,
                style: GoogleFonts.roboto(
                  fontSize: 16,
                  color: isDarkMode ? Colors.white70 : Color(0xFF666666),
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: 48),

              // Login Button
              Container(
                width: double.infinity,
                height: 54,
                child: ElevatedButton(
                  onPressed: () {
                    // Navigate to settings screen
                    ref.read(selectedIndexProvider.notifier).setIndex(4);
                    context.go('/settings');
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Color(0xFF22D400),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(27),
                    ),
                    elevation: 2,
                  ),
                  child: Text(
                    S.of(context).login,
                    style: GoogleFonts.roboto(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final themeMode = ref.watch(themeNotifierProvider);
    final isDarkMode = themeMode == ThemeMode.dark;

    // WATCH THE USER AUTH STATE
    final userState = ref.watch(userAuthNotifierProvider);

    // ✅ ALSO WATCH THE AUTH NOTIFIER TO HANDLE LOADING STATE
    final authState = ref.watch(authNotifierProvider);

    // ✅ SHOW LOADING WHILE AUTH IS BEING CHECKED
    return authState.when(
      loading: () => Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        body: Center(
          child: CircularProgressIndicator(
            color: Color(0xFF22D400),
          ),
        ),
      ),
      error: (error, stack) {
        // On auth error, show login required screen
        return _buildLoginRequiredScreen(isDarkMode);
      },
      data: (_) {
        // Auth check completed, now check if user is authenticated
        if (!userState.isAuthenticated) {
          return _buildLoginRequiredScreen(isDarkMode);
        }

        // LOAD REPORT TYPES IF NOT ALREADY LOADED
        if (_isLoadingReportTypes && _reportTypes.isEmpty) {
          _loadReportTypes();
        }

        final reportState = ref.watch(reportSubmissionStateProvider);

        // SHOW THE NORMAL REPORTS SCREEN IF AUTHENTICATED
        return Scaffold(
          backgroundColor: theme.scaffoldBackgroundColor,
          body: SingleChildScrollView(
            padding: EdgeInsets.all(16.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header Section with Background
                  Container(
                    width: double.infinity,
                    height: 120,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      image: DecorationImage(
                        image: AssetImage('assets/images/settings_image.png'),
                        fit: BoxFit.cover,
                        colorFilter: ColorFilter.mode(
                          isDarkMode
                              ? Colors.white.withValues(alpha: 0.3)
                              : Color(0x3322D400),
                          BlendMode.darken,
                        ),
                      ),
                    ),
                    child: Center(
                      child: Text(
                        S.of(context).reportAnIssue,
                        style: GoogleFonts.roboto(
                          fontWeight: FontWeight.w600,
                          fontSize: 24,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),

                  SizedBox(height: 24),

                  // Title Field
                  _buildInputField(
                    label: S.of(context).title,
                    controller: _titleController,
                    validator: (value) => value?.isEmpty ?? true
                        ? S.of(context).titleIsRequired
                        : null,
                    isDarkMode: isDarkMode,
                  ),

                  SizedBox(height: 16),

                  // Type Dropdown
                  _buildDropdownField(
                    label: S.of(context).typeOfSignal,
                    value: _selectedType,
                    items: _reportTypes,
                    onChanged: (value) => setState(() => _selectedType = value),
                    isDarkMode: isDarkMode,
                    isLoading: _isLoadingReportTypes,
                  ),

                  SizedBox(height: 16),

                  // Phone Field
                  _buildInputField(
                    label: S.of(context).phone,
                    controller: _phoneController,
                    keyboardType: TextInputType.phone,
                    validator: (value) => value?.isEmpty ?? true
                        ? S.of(context).phoneIsRequired
                        : null,
                    isDarkMode: isDarkMode,
                  ),

                  SizedBox(height: 16),

                  // Address Field
                  _buildInputField(
                    label: S.of(context).address,
                    controller: _addressController,
                    validator: null,
                    isDarkMode: isDarkMode,
                  ),

                  SizedBox(height: 16),

                  // Description Field
                  _buildInputField(
                    label: S.of(context).description,
                    controller: _descriptionController,
                    maxLines: 4,
                    validator: null,
                    isDarkMode: isDarkMode,
                  ),

                  SizedBox(height: 24),

                  // Files Section
                  _buildFilesSection(isDarkMode),

                  SizedBox(height: 32),

                  // Submit Button
                  Container(
                    width: double.infinity,
                    height: 56,
                    child: ElevatedButton(
                      onPressed: reportState.isLoading ? null : _submitReport,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Color(0xFF22D400),
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(28),
                        ),
                        elevation: 2,
                      ),
                      child: reportState.isLoading
                          ? CircularProgressIndicator(color: Colors.white)
                          : Text(
                              S.of(context).submitReport,
                              style: GoogleFonts.roboto(
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                              ),
                            ),
                    ),
                  ),

                  SizedBox(height: 16),

                  // Status Messages
                  reportState.when(
                    data: (success) => success
                        ? Container(
                            padding: EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Color(0xFF22D400).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Color(0xFF22D400)),
                            ),
                            child: Row(
                              children: [
                                Icon(Icons.check_circle,
                                    color: Color(0xFF22D400)),
                                SizedBox(width: 8),
                                Text(
                                  S.of(context).reportSubmittedSuccessfully,
                                  style: TextStyle(
                                    color: Color(0xFF22D400),
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : SizedBox.shrink(),
                    loading: () => SizedBox.shrink(),
                    error: (error, _) => Container(
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.red.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.red),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.error, color: Colors.red),
                          SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Error: ${error.toString()}',
                              style: TextStyle(
                                color: Colors.red,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // ... (rest of your existing widget methods stay the same)
  Widget _buildInputField({
    required String label,
    required TextEditingController controller,
    TextInputType? keyboardType,
    int maxLines = 1,
    String? Function(String?)? validator,
    required bool isDarkMode,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.roboto(
            fontWeight: FontWeight.w500,
            fontSize: 14,
            color: isDarkMode ? Colors.white : Color(0xFF626262),
          ),
        ),
        SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          maxLines: maxLines,
          validator: validator,
          style: TextStyle(
            color: isDarkMode ? Colors.white : Color(0xFF626262),
          ),
          decoration: InputDecoration(
            filled: true,
            fillColor: isDarkMode ? Color(0xFF333333) : Colors.white,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: isDarkMode ? Color(0xFF444444) : Color(0xFFE0E0E0),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: isDarkMode ? Color(0xFF444444) : Color(0xFFE0E0E0),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Color(0xFF22D400), width: 2),
            ),
            contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
        ),
      ],
    );
  }

  Widget _buildDropdownField({
    required String label,
    required String? value,
    required List<String> items,
    required void Function(String?) onChanged,
    required bool isDarkMode,
    bool isLoading = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.roboto(
            fontWeight: FontWeight.w500,
            fontSize: 14,
            color: isDarkMode ? Colors.white : Color(0xFF626262),
          ),
        ),
        SizedBox(height: 8),
        if (isLoading)
          Container(
            height: 56,
            decoration: BoxDecoration(
              color: isDarkMode ? Color(0xFF333333) : Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isDarkMode ? Color(0xFF444444) : Color(0xFFE0E0E0),
              ),
            ),
            child: Center(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Color(0xFF22D400),
                    ),
                  ),
                  SizedBox(width: 8),
                  Text(
                    'Loading report types...',
                    style: TextStyle(
                      color: isDarkMode ? Colors.white60 : Colors.black45,
                    ),
                  ),
                ],
              ),
            ),
          )
        else
          DropdownButtonFormField<String>(
            value: value,
            onChanged: onChanged,
            validator: (value) =>
                value == null ? 'Please select a report type' : null,
            isExpanded: true,
            items: items.map((String item) {
              return DropdownMenuItem<String>(
                value: item,
                child: Container(
                  width: double.infinity,
                  child: Text(
                    item,
                    style: TextStyle(
                      color: isDarkMode ? Colors.white : Color(0xFF626262),
                      fontSize: 14,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                    softWrap: true,
                  ),
                ),
              );
            }).toList(),
            decoration: InputDecoration(
              filled: true,
              fillColor: isDarkMode ? Color(0xFF333333) : Colors.white,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: isDarkMode ? Color(0xFF444444) : Color(0xFFE0E0E0),
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: isDarkMode ? Color(0xFF444444) : Color(0xFFE0E0E0),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Color(0xFF22D400), width: 2),
              ),
              contentPadding:
                  EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            dropdownColor: isDarkMode ? Color(0xFF333333) : Colors.white,
            icon: Icon(
              Icons.keyboard_arrow_down,
              color: isDarkMode ? Colors.white60 : Colors.black45,
            ),
            selectedItemBuilder: (BuildContext context) {
              return items.map((String item) {
                return Container(
                  width: double.infinity,
                  alignment: Alignment.centerLeft,
                  child: Text(
                    item,
                    style: TextStyle(
                      color: isDarkMode ? Colors.white : Color(0xFF626262),
                      fontSize: 14,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                );
              }).toList();
            },
            menuMaxHeight: MediaQuery.of(context).size.height * 0.4,
          ),
      ],
    );
  }

  Widget _buildFilesSection(bool isDarkMode) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              S.of(context).files,
              style: GoogleFonts.roboto(
                fontWeight: FontWeight.w500,
                fontSize: 14,
                color: isDarkMode ? Colors.white : Color(0xFF626262),
              ),
            ),
            TextButton.icon(
              onPressed: _showFilePickerOptions,
              icon: Icon(Icons.add, color: Color(0xFF22D400)),
              label: Text(
                S.of(context).addFiles,
                style: TextStyle(color: Color(0xFF22D400)),
              ),
            ),
          ],
        ),
        SizedBox(height: 8),
        Container(
          width: double.infinity,
          constraints: BoxConstraints(minHeight: 100),
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: isDarkMode ? Color(0xFF333333) : Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isDarkMode ? Color(0xFF444444) : Color(0xFFE0E0E0),
            ),
          ),
          child: _attachedFiles.isEmpty
              ? Column(
                  children: [
                    Icon(
                      Icons.cloud_upload_outlined,
                      size: 48,
                      color: isDarkMode ? Colors.white60 : Colors.black45,
                    ),
                    SizedBox(height: 8),
                    Text(
                      S.of(context).noFilesSelected,
                      style: TextStyle(
                        color: isDarkMode ? Colors.white60 : Colors.black45,
                      ),
                    ),
                  ],
                )
              : Column(
                  children: _attachedFiles.asMap().entries.map((entry) {
                    int index = entry.key;
                    FileModel file = entry.value;
                    return Container(
                      margin: EdgeInsets.only(bottom: 8),
                      padding: EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Color(0xFF22D400).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                            color: Color(0xFF22D400).withValues(alpha: 0.3)),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.attach_file,
                            color: Color(0xFF22D400),
                            size: 20,
                          ),
                          SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              file.fileName,
                              style: TextStyle(
                                color: isDarkMode
                                    ? Colors.white
                                    : Color(0xFF626262),
                                fontSize: 14,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          IconButton(
                            onPressed: () => _removeFile(index),
                            icon: Icon(
                              Icons.close,
                              color: Colors.red,
                              size: 20,
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ),
        ),
      ],
    );
  }
}
