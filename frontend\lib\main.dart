import 'package:device_preview/device_preview.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart'; // ADD THIS LINE
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:frontend/infrastructure/register_device/register_device_service.dart';
import 'package:frontend/l10n/generated/l10n.dart';
import 'package:frontend/providers/deep_link_provider.dart';
import 'package:frontend/providers/language_provider.dart';
import 'package:frontend/providers/theme_provider.dart';
import 'package:frontend/providers/auth_providers.dart';
import 'package:frontend/infrastructure/authentifiaction/authentification_service.dart';
import 'package:frontend/infrastructure/interceptor/global_interceptor.dart';
import 'package:frontend/router.dart';
import 'dart:io';
import 'dart:async';

// ADD THIS BACKGROUND HANDLER (outside main function)
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  print('📱 Background notification: ${message.notification?.title}');
}

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await dotenv.load(fileName: ".env.staging");

  HttpOverrides.global = MyHttpOverrides();

  // Initialize Firebase
  await Firebase.initializeApp();

  // ADD THIS LINE - Background message handler
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  // Register device for notifications
  await RegisterDeviceService.registerDevice();

  // ADD THESE TWO CRITICAL LINES:
  RegisterDeviceService.setupNotificationHandlers();
  await RegisterDeviceService.handleInitialNotification();

  // Initialize global HTTP client early - before provider container
  print('🔧 MAIN: Initializing global HTTP client early...');
  GlobalHttpClient().initialize();
  print('✅ MAIN: Global HTTP client initialized early');

  final container = ProviderContainer();
  await container.read(languageNotifierProvider.notifier).loadSavedLanguage();

  runApp(
    DevicePreview(
      enabled: !kReleaseMode,
      builder: (context) => UncontrolledProviderScope(
        container: container,
        child: const MyApp(),
      ),
    ),
  );
}

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}

class MyApp extends ConsumerStatefulWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  ConsumerState<MyApp> createState() => _MyAppState();
}

class _MyAppState extends ConsumerState<MyApp> {
  // Token refresh logic - exactly like Angular app.component.ts
  Timer? _refreshTimer;
  Timer? _logTimer;
  bool _sentRequest = false;

  @override
  void dispose() {
    // Cleanup like Angular ngOnDestroy
    _refreshTimer?.cancel();
    _logTimer?.cancel();
    super.dispose();
  }

  /// Setup token refresh timer - exactly like Angular
  void _setupTokenRefreshTimer(bool isLogged) {
    // Clear existing timers
    _refreshTimer?.cancel();
    _logTimer?.cancel();

    if (isLogged) {
      print('⏰ MAIN APP: Setting up token refresh timer');

      AuthenticationService.getTokenExpirationDate()
          .then((tokenExpirationDate) {
        if (tokenExpirationDate != null) {
          final currentTime = DateTime.now();
          final timeUntilExpiration =
              tokenExpirationDate.difference(currentTime);
          final refreshTime = timeUntilExpiration - const Duration(minutes: 5);

          print('📊 Token expires at: $tokenExpirationDate');
          print(
              '⏳ Time until expiration: ${timeUntilExpiration.inMinutes} minutes');
          print('🔄 Refresh scheduled in: ${refreshTime.inSeconds} seconds');

          if (refreshTime.inMilliseconds > 0) {
            // Set up refresh timer - exactly like Angular
            _refreshTimer = Timer(refreshTime, () {
              _refreshTokens();
            });

            // Set up monitoring timer - exactly like Angular
            _logTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
              final currentTime = DateTime.now();
              final remainingTime = tokenExpirationDate.difference(currentTime);
              final remainingSeconds =
                  remainingTime.inSeconds.clamp(0, double.infinity).toInt();

              // Only logout if no refresh in progress - exactly like Angular
              if (remainingSeconds <= 0) {
                if (!_sentRequest) {
                  timer.cancel();
                  _refreshTimer?.cancel();
                  print('⚠️ Token has expired. Logging out.');
                  _logout();
                } else {
                  print('🔄 Token expired but refresh in progress, waiting...');
                }
              }
            });
          } else {
            print('⚠️ Token expires soon, refreshing immediately');
            _refreshTokens();
          }
        } else {
          print('❌ Token expiration invalid, logging out');
          _logout();
        }
      }).catchError((e) {
        print('❌ Error setting up timer: $e');
        _logout();
      });
    } else {
      print('🔐 Not logged in - clearing timers');
      _refreshTimer?.cancel();
      _logTimer?.cancel();
    }
  }

  /// Refresh tokens - exactly like Angular
  Future<void> _refreshTokens() async {
    if (_sentRequest) {
      print('🔐 Refresh already in progress, skipping...');
      return;
    }

    _sentRequest = true;

    try {
      print('🔐 MAIN APP: Attempting to refresh tokens...');

      final result = await AuthenticationService.refreshToken();

      if (result != null && result['isAuthSuccessful'] == true) {
        print('✅ MAIN APP: Token refresh successful');

        // Clear timers and set up new ones
        _refreshTimer?.cancel();
        _logTimer?.cancel();

        // Small delay then setup new timer
        await Future.delayed(const Duration(milliseconds: 100));
        _setupTokenRefreshTimer(true);
      } else {
        print('❌ MAIN APP: Token refresh failed, logging out');
        _logout();
      }
    } catch (e) {
      print('❌ MAIN APP: Token refresh error: $e');
      _logout();
    } finally {
      _sentRequest = false;
    }
  }

  /// Logout - exactly like Angular
  void _logout() {
    _refreshTimer?.cancel();
    _logTimer?.cancel();

    // Call the auth provider logout
    ref.read(authNotifierProvider.notifier).logoutUser();
  }

  @override
  Widget build(BuildContext context) {
    final themeMode = ref.watch(themeNotifierProvider);
    final locale = ref.watch(languageNotifierProvider);

    // Watch deep links
    ref.watch(deepLinkProvider);

    // FIXED: Listen to auth state changes in build method
    ref.listen<UserAuthState>(userAuthNotifierProvider, (previous, next) {
      print('🔐 MAIN APP: Auth state changed: ${next.isAuthenticated}');
      _setupTokenRefreshTimer(next.isAuthenticated);
    });

    return MaterialApp.router(
      debugShowCheckedModeBanner: false,
      title: 'Smart Yambol',
      themeMode: themeMode,
      theme: ThemeData(
        brightness: Brightness.light,
        primaryColor: Colors.green,
        scaffoldBackgroundColor: Colors.white,
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          elevation: 2,
        ),
        bottomNavigationBarTheme: const BottomNavigationBarThemeData(
          backgroundColor: Colors.white,
          selectedItemColor: Colors.green,
          unselectedItemColor: Colors.black54,
        ),
      ),
      darkTheme: ThemeData(
        brightness: Brightness.dark,
        primaryColor: Colors.green,
        scaffoldBackgroundColor: Colors.black,
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.black,
          foregroundColor: Colors.white,
          elevation: 2,
        ),
        bottomNavigationBarTheme: const BottomNavigationBarThemeData(
          backgroundColor: Colors.black,
          selectedItemColor: Colors.green,
          unselectedItemColor: Colors.white60,
        ),
      ),
      locale: locale,
      supportedLocales: S.supportedLocales,
      localizationsDelegates: const [
        S.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      routerConfig: router,
    );
  }
}
