// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transport_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchAllStopsHash() => r'34f9a0dd715f203fe7dcd81ca972eeebd46e0477';

/// See also [fetchAllStops].
@ProviderFor(fetchAllStops)
final fetchAllStopsProvider =
    AutoDisposeFutureProvider<List<StopModel>>.internal(
  fetchAllStops,
  name: r'fetchAllStopsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchAllStopsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchAllStopsRef = AutoDisposeFutureProviderRef<List<StopModel>>;
String _$fetchPanelsHash() => r'd0ec622020fb20b6354e25953bae537357d815ee';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [fetchPanels].
@ProviderFor(fetchPanels)
const fetchPanelsProvider = FetchPanelsFamily();

/// See also [fetchPanels].
class FetchPanelsFamily extends Family<AsyncValue<List<PanelModel>>> {
  /// See also [fetchPanels].
  const FetchPanelsFamily();

  /// See also [fetchPanels].
  FetchPanelsProvider call({
    required List<int> posts,
  }) {
    return FetchPanelsProvider(
      posts: posts,
    );
  }

  @override
  FetchPanelsProvider getProviderOverride(
    covariant FetchPanelsProvider provider,
  ) {
    return call(
      posts: provider.posts,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchPanelsProvider';
}

/// See also [fetchPanels].
class FetchPanelsProvider extends AutoDisposeFutureProvider<List<PanelModel>> {
  /// See also [fetchPanels].
  FetchPanelsProvider({
    required List<int> posts,
  }) : this._internal(
          (ref) => fetchPanels(
            ref as FetchPanelsRef,
            posts: posts,
          ),
          from: fetchPanelsProvider,
          name: r'fetchPanelsProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$fetchPanelsHash,
          dependencies: FetchPanelsFamily._dependencies,
          allTransitiveDependencies:
              FetchPanelsFamily._allTransitiveDependencies,
          posts: posts,
        );

  FetchPanelsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.posts,
  }) : super.internal();

  final List<int> posts;

  @override
  Override overrideWith(
    FutureOr<List<PanelModel>> Function(FetchPanelsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchPanelsProvider._internal(
        (ref) => create(ref as FetchPanelsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        posts: posts,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<PanelModel>> createElement() {
    return _FetchPanelsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchPanelsProvider && other.posts == posts;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, posts.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchPanelsRef on AutoDisposeFutureProviderRef<List<PanelModel>> {
  /// The parameter `posts` of this provider.
  List<int> get posts;
}

class _FetchPanelsProviderElement
    extends AutoDisposeFutureProviderElement<List<PanelModel>>
    with FetchPanelsRef {
  _FetchPanelsProviderElement(super.provider);

  @override
  List<int> get posts => (origin as FetchPanelsProvider).posts;
}

String _$selectedStopHash() => r'6f480dd5a722ac7a6dfebcb6a00f9e407bb57ca5';

/// See also [SelectedStop].
@ProviderFor(SelectedStop)
final selectedStopProvider =
    AutoDisposeNotifierProvider<SelectedStop, StopModel?>.internal(
  SelectedStop.new,
  name: r'selectedStopProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$selectedStopHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectedStop = AutoDisposeNotifier<StopModel?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
