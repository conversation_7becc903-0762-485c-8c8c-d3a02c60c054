import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/models/favourites_base_menu_item_command.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../providers/favorites/favourites_provider.dart';
import '../../../providers/theme_provider.dart';

class FavoriteListItem extends ConsumerWidget {
  final BaseMenuItem item; // Changed from TourismMenuItem to BaseMenuItem

  const FavoriteListItem({
    super.key,
    required this.item,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isDarkMode = ref.watch(themeNotifierProvider) == ThemeMode.dark;
    final favAsync = ref.watch(favouritesItemsProvider);

    return favAsync.when(
      data: (favs) {
        final isFav = favs.contains(item.id);

        return GestureDetector(
          onTap: () {
            if (item.route != null) {
              context.push(item.route!);
            }
          },
          child: Container(
            margin: const EdgeInsets.only(bottom: 16),
            width: 400,
            height: 76,
            decoration: BoxDecoration(
              color: isDarkMode ? const Color(0xFF333333) : Colors.white,
              borderRadius: const BorderRadius.only(
                topRight: Radius.circular(10),
                bottomRight: Radius.circular(10),
              ),
              boxShadow: [
                BoxShadow(
                  color: isDarkMode
                      ? Colors.black.withOpacity(0.2)
                      : Colors.black.withOpacity(0.1),
                  offset: const Offset(4, 4),
                  blurRadius: 6,
                ),
              ],
            ),
            child: ListTile(
              leading: Padding(
                padding: const EdgeInsets.only(top: 8.0, left: 3.5),
                child: GestureDetector(
                  onTap: () {
                    ref.read(favouritesItemsProvider.notifier).toggle(item.id);
                  },
                  child: Icon(
                    isFav ? Icons.star : Icons.star_border,
                    color: isFav
                        ? Colors.green
                        : (isDarkMode ? Colors.white : const Color(0xFFBABBBA)),
                  ),
                ),
              ),
              title: Padding(
                padding: const EdgeInsets.only(top: 14.0),
                child: Text(
                  item.label(context),
                  style: GoogleFonts.roboto(
                    fontWeight: FontWeight.w400,
                    fontSize: 17,
                    height: 15 / 14,
                    letterSpacing: 0.0,
                    color: isDarkMode ? Colors.white : const Color(0xFF626262),
                  ),
                ),
              ),
            ),
          ),
        );
      },
      loading: () => const SizedBox(
        height: 76,
        child: Center(child: CircularProgressIndicator()),
      ),
      error: (_, __) => const SizedBox(
        height: 76,
        child: Center(child: Icon(Icons.error)),
      ),
    );
  }
}
