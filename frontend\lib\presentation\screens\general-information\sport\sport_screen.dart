import 'package:flutter/material.dart';
import 'package:frontend/l10n/generated/l10n.dart';
import 'package:frontend/providers/theme_provider.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class SportScreen extends ConsumerWidget {
  const SportScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeMode =
        ref.watch(themeNotifierProvider); // Access themeMode from provider

    final List<String> sportMenuItems = [S.of(context).sportFacilities];

    // Determine if it's dark mode
    final isDarkMode = themeMode == ThemeMode.dark;

    return Scaffold(
      backgroundColor:
          isDarkMode ? Colors.black : Colors.white, // Dark mode background
      body: Padding(
        padding: const EdgeInsets.all(0),
        child: Stack(
          children: [
            // Top Banner Image
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Stack(
                  children: [
                    Container(
                      width: MediaQuery.of(context).size.width,
                      height: 200, // Fixed height for the banner
                      decoration: BoxDecoration(
                        image: DecorationImage(
                          image: AssetImage('assets/images/general-info.png'),
                          fit: BoxFit.cover,
                          colorFilter: ColorFilter.mode(
                            isDarkMode
                                ? Colors.white.withValues(alpha: 0.3)
                                : Color(0x0F22D400), // Lighter dark mode filter
                            BlendMode.darken,
                          ),
                        ),
                      ),
                    ),
                    Positioned.fill(
                      child: Align(
                        alignment: Alignment.center,
                        child: Padding(
                          padding: const EdgeInsets.only(
                              top: 0), // Adjust top padding as needed
                          child: Text(
                            S.of(context).sportYambolWelcomeMessage,
                            textAlign: TextAlign.center,
                            style: GoogleFonts.roboto(
                              fontWeight: FontWeight.w700,
                              fontSize: 17,
                              height: 15 / 14,
                              letterSpacing: 0.0,
                              color: isDarkMode
                                  ? Colors.white
                                  : Color(
                                      0xFF626262), // White text in dark mode
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),

            // Static Menu List
            Positioned(
              top: 165, // Fixed position for the menu list
              left: 0,
              right: 13,
              bottom: 0,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 0),
                child: ListView.builder(
                  itemCount: sportMenuItems.length,
                  itemBuilder: (context, index) {
                    final item = sportMenuItems[index];

                    return GestureDetector(
                      onTap: () {
                        if (item == S.of(context).sportFacilities) {
                          context.push('/general/sport/sportFacilities');
                        }
                      },
                      child: Container(
                        margin: EdgeInsets.only(bottom: 16),
                        width: 400, // Fixed width for menu items
                        height: 76, // Fixed height for menu items
                        decoration: BoxDecoration(
                          color: isDarkMode
                              ? Color(0xFF333333)
                              : Colors
                                  .white, // Dark background for items in dark mode
                          borderRadius: BorderRadius.only(
                            topRight: Radius.circular(10),
                            bottomRight: Radius.circular(10),
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: isDarkMode
                                  ? Colors.black.withValues(alpha: 0.2)
                                  : Colors.black.withValues(alpha: 0.1),
                              offset: Offset(4, 4),
                              blurRadius: 6,
                            ),
                          ],
                        ),
                        child: ListTile(
                          title: Padding(
                            padding: const EdgeInsets.only(top: 14.0),
                            child: Text(
                              item,
                              style: GoogleFonts.roboto(
                                fontWeight: FontWeight.w400,
                                fontSize: 17,
                                height: 15 / 14,
                                letterSpacing: 0.0,
                                color: isDarkMode
                                    ? Colors.white
                                    : Color(
                                        0xFF626262), // Text color change based on theme
                              ),
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
