import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:frontend/infrastructure/interceptor/global_interceptor.dart';
import 'package:frontend/models/enum_command.dart';
import 'package:frontend/models/reports_service_command.dart';

class ReportsMobileService {
  // Use global HTTP client
  static final Dio _dio = GlobalHttpClient().dio;

  /// **Get Report Types**
  static Future<EnumModel?> getReportTypes() async {
    const String reportTypesEndpoint = 'Enums/ReportTypes';

    try {
      print('FETCHING REPORT TYPES...');

      final response = await _dio.get(
        reportTypesEndpoint,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        print('Report types fetched successfully: ${response.data}');
        return EnumModel.fromJson(response.data);
      } else {
        throw Exception(
            'Failed to fetch report types with status ${response.statusCode}');
      }
    } on DioException catch (e) {
      print("❌ Report Types Fetch Dio Error: ${e.response?.statusCode}");
      print("📩 Report Types Response Data: ${e.response?.data}");
      print("📝 Report Types Error Message: ${e.message}");
      return null;
    } catch (e) {
      print("⚠ Report Types Fetch Unexpected Error: $e");
      return null;
    }
  }

  /// **Submit Report**
  static Future<bool> submitReport({
    required String title,
    required String phoneNumber,
    String? description,
    String? address,
    required String type,
    List<FileModel>? files,
  }) async {
    const String reportsEndpoint = 'Reports';

    try {
      print('SUBMITTING REPORT...');

      final formData = FormData();
      formData.fields.add(MapEntry('Title', title));
      formData.fields.add(MapEntry('PhoneNumber', phoneNumber));
      formData.fields.add(MapEntry('Type', type));

      if (description?.isNotEmpty == true) {
        formData.fields.add(MapEntry('Description', description!));
      }

      if (address?.isNotEmpty == true) {
        formData.fields.add(MapEntry('Address', address!));
      }

      if (files != null && files.isNotEmpty) {
        for (int i = 0; i < files.length; i++) {
          final file = files[i];
          try {
            final bytes = base64Decode(file.content);
            final multipartFile = MultipartFile.fromBytes(
              bytes,
              filename: file.fileName,
              contentType: DioMediaType.parse(file.contentType),
            );
            formData.files.add(MapEntry('Files[$i]', multipartFile));
          } catch (e) {
            print('Error processing file ${file.fileName}: $e');
          }
        }
      }

      // No need to manually add token - global interceptor handles it
      final response = await _dio.post(reportsEndpoint, data: formData);

      if (response.statusCode == 200 || response.statusCode == 201) {
        print('✅ Report submitted successfully');
        return true;
      } else {
        throw Exception('Failed to submit report');
      }
    } on DioException catch (e) {
      print("❌ Report Submission Error: ${e.response?.statusCode}");
      return false;
    } catch (e) {
      print("⚠ Report Submission Unexpected Error: $e");
      return false;
    }
  }
}
