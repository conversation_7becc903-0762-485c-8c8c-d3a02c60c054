import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/l10n/generated/l10n.dart';
import 'package:frontend/models/get_repairs_by_id_command.dart';
import 'package:frontend/providers/language_provider.dart';
import 'package:frontend/providers/repair_provider.dart';
import 'package:frontend/providers/theme_provider.dart';
import 'package:frontend/presentation/widgets/image_carousel.dart';
import 'package:frontend/presentation/widgets/html_utils.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';

class RepairDetailsModal extends ConsumerStatefulWidget {
  final int repairId;

  const RepairDetailsModal({
    super.key,
    required this.repairId,
  });

  @override
  ConsumerState<RepairDetailsModal> createState() => _RepairDetailsModalState();
}

class _RepairDetailsModalState extends ConsumerState<RepairDetailsModal> {
  bool _isImagesLoaded = false;
  final List<String> _imageUrls = [];

  @override
  void initState() {
    super.initState();
    // Lock to portrait when modal opens
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  @override
  void dispose() {
    // Restore all orientations when modal closes
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
    super.dispose();
  }

  // Function to preload images
  Future<void> _preloadImages(List<String> urls) async {
    if (urls.isEmpty) {
      setState(() {
        _isImagesLoaded = true;
      });
      return;
    }

    int loadedCount = 0;

    for (final url in urls) {
      final image = Image.network(url);
      final imageProvider = image.image;

      imageProvider.resolve(const ImageConfiguration()).addListener(
            ImageStreamListener((_, __) {
              loadedCount++;
              if (loadedCount == urls.length) {
                if (mounted) {
                  setState(() {
                    _isImagesLoaded = true;
                  });
                }
              }
            }, onError: (_, __) {
              loadedCount++;
              if (loadedCount == urls.length) {
                if (mounted) {
                  setState(() {
                    _isImagesLoaded = true;
                  });
                }
              }
            }),
          );
    }
  }

  Color _getRepairStatusColor(RepairDetailsModel repair) {
    final now = DateTime.now();

    if (repair.startDate != null && repair.endDate != null) {
      if (repair.startDate!.isBefore(now) && repair.endDate!.isAfter(now)) {
        return const Color(0xFFFF9800); // Orange for ongoing
      } else if (repair.endDate!.isBefore(now)) {
        return const Color(0xFF22D400); // Green for completed
      }
    }
    return const Color(0xFFFF5722); // Red for scheduled
  }

  String _getRepairStatusText(RepairDetailsModel repair) {
    final now = DateTime.now();

    if (repair.startDate != null && repair.endDate != null) {
      if (repair.startDate!.isBefore(now) && repair.endDate!.isAfter(now)) {
        return S.of(context).repairStatusOngoing;
      } else if (repair.endDate!.isBefore(now)) {
        return S.of(context).repairStatusCompleted;
      }
    }
    return S.of(context).repairStatusScheduled;
  }

  String _formatDate(DateTime? date) {
    if (date == null) return S.of(context).notSpecified;
    return DateFormat('dd.MM.yyyy г.').format(date);
  }

  Widget _buildDateCard({
    required String title,
    required DateTime? date,
    required IconData icon,
    required Color color,
    required bool isDarkMode,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF333333) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color, width: 1.5),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.08),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: color,
              size: 16,
            ),
          ),
          const SizedBox(height: 6),
          Text(
            title,
            style: GoogleFonts.roboto(
              fontWeight: FontWeight.w600,
              fontSize: 12,
              color: color,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            _formatDate(date),
            style: GoogleFonts.roboto(
              fontSize: 11,
              fontWeight: FontWeight.w500,
              color: isDarkMode ? Colors.white70 : const Color(0xFF626262),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPortraitLayout(
    RepairDetailsModel repair,
    bool isDarkMode,
    double screenHeight,
    bool hasImages,
  ) {
    return Column(
      children: [
        // Image Carousel - Only if we have images
        if (hasImages)
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: ImageCarousel(imageUrls: _imageUrls),
            ),
          ),

        // Title Section
        Container(
          width: double.infinity,
          margin: EdgeInsets.symmetric(
              horizontal: 20, vertical: hasImages ? 16 : 20),
          child: Text(
            repair.name ?? S.of(context).unknownRepair,
            style: GoogleFonts.roboto(
              fontWeight: FontWeight.w700,
              fontSize: 22,
              color: isDarkMode ? Colors.white : const Color(0xFF1A1A1A),
              height: 1.2,
            ),
            textAlign: TextAlign.center,
          ),
        ),

        // Date Cards Section
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            children: [
              Expanded(
                child: _buildDateCard(
                  title: S.of(context).start,
                  date: repair.startDate,
                  icon: Icons.play_arrow,
                  color: const Color(0xFF22D400),
                  isDarkMode: isDarkMode,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildDateCard(
                  title: S.of(context).end,
                  date: repair.endDate,
                  icon: Icons.stop,
                  color: const Color(0xFFFF5722),
                  isDarkMode: isDarkMode,
                ),
              ),
            ],
          ),
        ),

        // Description Section
        Container(
          margin: const EdgeInsets.fromLTRB(20, 16, 20, 24),
          decoration: BoxDecoration(
            color: isDarkMode ? const Color(0xFF2A2A2A) : Colors.grey[50],
            borderRadius: BorderRadius.circular(18),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: isDarkMode ? 0.15 : 0.04),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 16, 16, 12),
                child: Row(
                  children: [
                    Container(
                      width: 36,
                      height: 36,
                      decoration: BoxDecoration(
                        color: const Color(0xFF22D400).withValues(alpha: 0.1),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.description_outlined,
                        color: Color(0xFF22D400),
                        size: 18,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      S.of(context).description,
                      style: GoogleFonts.roboto(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        color:
                            isDarkMode ? Colors.white : const Color(0xFF1A1A1A),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                constraints: BoxConstraints(
                  minHeight: 100,
                  maxHeight: screenHeight * 0.25,
                ),
                child: SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: HtmlUtils.createStyledHtmlWidget(
                    htmlContent: repair.description ??
                        S.of(context).noDescriptionAvailable,
                    isDarkMode: isDarkMode,
                    baseFontSize: 14.0,
                    lineHeight: 1.5,
                    fontFamily: 'Roboto',
                    onLinkTap: (url, attributes, element) {
                      print('Link tapped: $url');
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final themeMode = ref.watch(themeNotifierProvider);
    final languageCode = ref.watch(languageNotifierProvider).languageCode;
    final isDarkMode = themeMode == ThemeMode.dark;
    final screenHeight = MediaQuery.of(context).size.height;

    // Fetch repair details
    final repairAsync = ref.watch(fetchRepairByIdProvider(
        (id: widget.repairId, languageCode: languageCode)));

    return Container(
      height: screenHeight * 0.92,
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(25)),
      ),
      child: repairAsync.when(
        data: (repair) {
          if (repair == null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.search_off,
                    size: 64,
                    color: isDarkMode ? Colors.white60 : Colors.black45,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    S.of(context).repairNotFound,
                    style: GoogleFonts.roboto(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      color:
                          isDarkMode ? Colors.white : const Color(0xFF626262),
                    ),
                  ),
                ],
              ),
            );
          }

          // Process images only once when data is received
          if (_imageUrls.isEmpty) {
            if (repair.images != null && repair.images!.isNotEmpty) {
              var coverImage = repair.images!.firstWhere(
                (image) => image.isCover == true,
                orElse: () => repair.images!.first,
              );

              if (coverImage.preSignedUrl != null) {
                _imageUrls.add(coverImage.preSignedUrl!);
              }

              _imageUrls.addAll(repair.images!
                  .where((image) =>
                      image.isCover != true && image.preSignedUrl != null)
                  .map((image) => image.preSignedUrl!));
            }

            _preloadImages(_imageUrls);
          }

          if (!_isImagesLoaded) {
            return Center(
              child: CircularProgressIndicator(
                color: const Color(0xFF22D400),
                strokeWidth: 3,
              ),
            );
          }

          final hasImages = _imageUrls.isNotEmpty;

          return Column(
            children: [
              // Top handle for modal
              Container(
                margin: const EdgeInsets.only(top: 12, bottom: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: isDarkMode ? Colors.white30 : Colors.black26,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header with status and close button
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                child: Row(
                  children: [
                    // Status badge
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: _getRepairStatusColor(repair),
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: _getRepairStatusColor(repair)
                                .withValues(alpha: 0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Text(
                        _getRepairStatusText(repair),
                        style: GoogleFonts.roboto(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                          fontSize: 13,
                        ),
                      ),
                    ),
                    const Spacer(),
                    // Close button
                    GestureDetector(
                      onTap: () => Navigator.pop(context),
                      child: Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          color: isDarkMode
                              ? const Color(0xFF333333)
                              : Colors.grey[100],
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.close,
                          color: isDarkMode ? Colors.white70 : Colors.black54,
                          size: 18,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Content - portrait layout only
              Expanded(
                child: SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: _buildPortraitLayout(
                      repair, isDarkMode, screenHeight, hasImages),
                ),
              ),
            ],
          );
        },
        loading: () => Center(
          child: CircularProgressIndicator(
            color: const Color(0xFF22D400),
            strokeWidth: 3,
          ),
        ),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: isDarkMode ? Colors.red[300] : Colors.red[600],
              ),
              const SizedBox(height: 16),
              Text(
                S.of(context).loadingError,
                style: GoogleFonts.roboto(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  color: isDarkMode ? Colors.white : const Color(0xFF626262),
                ),
              ),
              const SizedBox(height: 8),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 40),
                child: Text(
                  error.toString(),
                  style: GoogleFonts.roboto(
                    fontSize: 14,
                    color: isDarkMode ? Colors.white60 : Colors.black45,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Helper function to show the modal
void showRepairDetailsModal(BuildContext context, int repairId) {
  print("Showing repair details modal for ID: $repairId");
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) => RepairDetailsModal(repairId: repairId),
  );
}
