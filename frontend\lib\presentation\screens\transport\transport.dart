import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/l10n/generated/l10n.dart';
import 'package:frontend/models/transport_departure_command.dart';
import 'package:frontend/models/transport_panel_command.dart';
import 'package:frontend/models/transport_stop_command.dart';
import 'package:frontend/providers/transport_provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'dart:ui' as ui;
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart' as gmaps;

// Constants
class TransportConstants {
  static const LatLng yambolCenter = LatLng(42.4842, 26.5035);
  static const double defaultZoom = 14.0;
  static const double selectedStopZoom = 16.0;
  static const double busStopIconSize = 45.0;
  static const String darkMapStylePath = 'assets/map_styles/dark_map.json';
  static const String lightMapStylePath = 'assets/map_styles/light_map.json';
  static const String busStopIconPath = 'assets/icons/bus_stop.svg';
}

class TransportScreen extends ConsumerStatefulWidget {
  const TransportScreen({super.key});

  @override
  ConsumerState<TransportScreen> createState() => _TransportScreenState();
}

class _TransportScreenState extends ConsumerState<TransportScreen> {
  GoogleMapController? _mapController;
  String? _mapStyle;
  BitmapDescriptor? _busStopIcon;
  Set<Marker> _markers = {};

  // Track loading state
  bool _isIconLoaded = false;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_isIconLoaded) {
      _loadCustomBusStopIcon();
    }
    _loadMapStyle();
  }

  Future<void> _loadMapStyle() async {
    if (!mounted) return;

    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final stylePath = isDarkMode
        ? TransportConstants.darkMapStylePath
        : TransportConstants.lightMapStylePath;

    try {
      final style = await rootBundle.loadString(stylePath);

      if (mounted) {
        setState(() {
          _mapStyle = style;
        });
      }
    } catch (e) {
      debugPrint("Error loading map style: $e");
      // Continue without custom style - _mapStyle remains null
    }
  }

  Future<void> _loadCustomBusStopIcon() async {
    if (!mounted) return;

    try {
      final icon = await _createCustomBusStopIcon();

      if (mounted) {
        setState(() {
          _busStopIcon = icon;
          _isIconLoaded = true;
        });
      }
    } catch (e) {
      debugPrint("Error loading custom bus stop icon: $e");
      // Fallback to default marker
      if (mounted) {
        setState(() {
          _busStopIcon =
              BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen);
          _isIconLoaded = true;
        });
      }
    }
  }

  Future<BitmapDescriptor> _createCustomBusStopIcon() async {
    final svgString =
        await rootBundle.loadString(TransportConstants.busStopIconPath);
    final pictureInfo = await vg.loadPicture(SvgStringLoader(svgString), null);

    final ui.PictureRecorder recorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(recorder);

    const double logicalSize = TransportConstants.busStopIconSize;
    final double pixelRatio = MediaQuery.of(context).devicePixelRatio;
    final double bitmapWidth = logicalSize * pixelRatio;
    final double bitmapHeight = logicalSize * pixelRatio;

    // Calculate scale and positioning
    final double scaleX = bitmapWidth / pictureInfo.size.width;
    final double scaleY = bitmapHeight / pictureInfo.size.height;
    final double scale = scaleX < scaleY ? scaleX : scaleY;
    final double adjustedScale = scale / pixelRatio;

    final double translateX =
        (logicalSize - (pictureInfo.size.width * adjustedScale)) /
            2 *
            pixelRatio;
    final double translateY =
        (logicalSize - (pictureInfo.size.height * adjustedScale)) /
            2 *
            pixelRatio;

    // Transform and draw
    canvas.translate(translateX, translateY);
    canvas.scale(adjustedScale * pixelRatio);
    canvas.drawPicture(pictureInfo.picture);

    // Convert to image
    final ui.Image image = await recorder.endRecording().toImage(
          bitmapWidth.toInt(),
          bitmapHeight.toInt(),
        );

    final ByteData? byteData =
        await image.toByteData(format: ui.ImageByteFormat.png);
    if (byteData == null) {
      throw Exception('Failed to convert image to bytes');
    }

    final Uint8List uint8List = byteData.buffer.asUint8List();
    return gmaps.BitmapDescriptor.fromBytes(uint8List);
  }

  void _updateStopsDisplay(List<StopModel> stops) {
    if (!mounted || _busStopIcon == null) return;

    final Set<Marker> newMarkers =
        stops.map((stop) => _createStopMarker(stop)).toSet();

    if (mounted) {
      setState(() {
        _markers = newMarkers;
      });
    }
  }

  Marker _createStopMarker(StopModel stop) {
    return Marker(
      markerId: MarkerId('stop_${stop.id}'),
      position: LatLng(stop.lat, stop.lon),
      icon: _busStopIcon!,
      infoWindow: InfoWindow(
        title: stop.n,
        snippet: S.of(context).tapForBusArrivals,
      ),
      onTap: () => _showBusArrivalsModal(stop),
    );
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
    // No need to set style here - it's handled by the GoogleMap widget's style property
  }

  Future<void> _showBusArrivalsModal(StopModel stop) async {
    // Update selected stop in provider
    ref.read(selectedStopProvider.notifier).selectStop(stop);

    // Show modal
    if (!mounted) return;

    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => BusArrivalsModal(stop: stop),
    );
  }

  Future<void> _animateToStop(StopModel stop) async {
    await _mapController?.animateCamera(
      CameraUpdate.newLatLngZoom(
        LatLng(stop.lat, stop.lon),
        TransportConstants.selectedStopZoom,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final stopsAsync = ref.watch(fetchAllStopsProvider);
    final selectedStop = ref.watch(selectedStopProvider);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: Column(
        children: [
          _buildStopsDropdownHeader(
              context, isDarkMode, stopsAsync, selectedStop),
          _buildGoogleMap(isDarkMode),
        ],
      ),
    );
  }

  Widget _buildStopsDropdownHeader(
    BuildContext context,
    bool isDarkMode,
    AsyncValue<List<StopModel>> stopsAsync,
    StopModel? selectedStop,
  ) {
    return Container(
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top + 16,
        left: 16,
        right: 16,
        bottom: 16,
      ),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey.shade900 : const Color(0xFF22D400),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: stopsAsync.when(
        data: (stops) =>
            _buildStopsDropdown(context, isDarkMode, stops, selectedStop),
        loading: () => _buildLoadingDropdown(isDarkMode),
        error: (error, stack) =>
            _buildErrorDropdown(context, isDarkMode, error),
      ),
    );
  }

  Widget _buildStopsDropdown(
    BuildContext context,
    bool isDarkMode,
    List<StopModel> stops,
    StopModel? selectedStop,
  ) {
    // Update markers when stops are loaded and icon is ready
    if (_busStopIcon != null && mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _updateStopsDisplay(stops);
      });
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: isDarkMode
            ? Colors.grey.shade800.withOpacity(0.9)
            : Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDarkMode ? Colors.grey.shade600 : Colors.grey.shade300,
          width: 1,
        ),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<StopModel>(
          isExpanded: true,
          value: selectedStop,
          hint: Text(
            S.of(context).selectABusStop,
            style: GoogleFonts.roboto(
              fontSize: 16,
              color: isDarkMode ? Colors.grey.shade300 : Colors.grey[600],
            ),
          ),
          icon: Icon(
            Icons.arrow_drop_down,
            color: isDarkMode ? Colors.grey.shade300 : const Color(0xFF22D400),
          ),
          style: GoogleFonts.roboto(
            fontSize: 16,
            color: isDarkMode ? Colors.white : Colors.black87,
          ),
          dropdownColor: isDarkMode ? Colors.grey.shade800 : Colors.white,
          menuMaxHeight: MediaQuery.of(context).size.height * 0.5,
          items: stops.map(_buildDropdownItem).toList(),
          onChanged: (StopModel? newValue) async {
            if (newValue != null) {
              ref.read(selectedStopProvider.notifier).selectStop(newValue);
              await Future.wait([
                _showBusArrivalsModal(newValue),
                _animateToStop(newValue),
              ]);
            }
          },
        ),
      ),
    );
  }

  DropdownMenuItem<StopModel> _buildDropdownItem(StopModel stop) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return DropdownMenuItem<StopModel>(
      value: stop,
      child: Row(
        children: [
          Icon(
            Icons.directions_bus,
            color: isDarkMode ? Colors.grey.shade300 : const Color(0xFF22D400),
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              stop.n,
              style: TextStyle(
                color: isDarkMode ? Colors.white : Colors.black87,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingDropdown(bool isDarkMode) {
    return Container(
      height: 56,
      decoration: BoxDecoration(
        color: isDarkMode
            ? Colors.grey.shade800.withOpacity(0.9)
            : Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Center(
        child: SizedBox(
          width: 24,
          height: 24,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF22D400)),
          ),
        ),
      ),
    );
  }

  Widget _buildErrorDropdown(
      BuildContext context, bool isDarkMode, Object error) {
    return Container(
      height: 56,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: isDarkMode
            ? Colors.grey.shade800.withOpacity(0.9)
            : Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.red.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 20),
          const SizedBox(width: 8),
          Text(
            'Error loading stops',
            style: GoogleFonts.roboto(fontSize: 16, color: Colors.red),
          ),
          const Spacer(),
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
            onPressed: () => ref.invalidate(fetchAllStopsProvider),
          ),
        ],
      ),
    );
  }

  Widget _buildGoogleMap(bool isDarkMode) {
    return Expanded(
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade300,
            width: 0.5,
          ),
        ),
        child: GoogleMap(
          onMapCreated: _onMapCreated,
          initialCameraPosition: const CameraPosition(
            target: TransportConstants.yambolCenter,
            zoom: TransportConstants.defaultZoom,
          ),
          markers: _markers,
          style: _mapStyle,
          mapType: MapType.normal,
          myLocationEnabled: true,
          myLocationButtonEnabled: true,
          compassEnabled: true,
          zoomControlsEnabled: true,
          scrollGesturesEnabled: true,
          zoomGesturesEnabled: true,
          tiltGesturesEnabled: true,
          rotateGesturesEnabled: true,
          mapToolbarEnabled: false,
          buildingsEnabled: false,
          trafficEnabled: false,
          liteModeEnabled: false,
          gestureRecognizers: _buildGestureRecognizers(),
        ),
      ),
    );
  }

  Set<Factory<OneSequenceGestureRecognizer>> _buildGestureRecognizers() {
    return <Factory<OneSequenceGestureRecognizer>>{
      Factory<PanGestureRecognizer>(() => PanGestureRecognizer()),
      Factory<ScaleGestureRecognizer>(() => ScaleGestureRecognizer()),
      Factory<TapGestureRecognizer>(() => TapGestureRecognizer()),
      Factory<VerticalDragGestureRecognizer>(
          () => VerticalDragGestureRecognizer()),
      Factory<HorizontalDragGestureRecognizer>(
          () => HorizontalDragGestureRecognizer()),
    };
  }

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }
}

// Separate the modal into its own file for better organization
class BusArrivalsModal extends ConsumerStatefulWidget {
  final StopModel stop;

  const BusArrivalsModal({
    super.key,
    required this.stop,
  });

  @override
  ConsumerState<BusArrivalsModal> createState() => _BusArrivalsModalState();
}

class _BusArrivalsModalState extends ConsumerState<BusArrivalsModal> {
  late final Future<List<PanelModel>> _panelsFuture;

  @override
  void initState() {
    super.initState();
    _panelsFuture =
        ref.read(fetchPanelsProvider(posts: [widget.stop.id]).future);
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      height: screenHeight * 0.6,
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey.shade900 : Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(25)),
        border: Border.all(
          color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade200,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          _buildModalHandle(isDarkMode),
          _buildModalHeader(isDarkMode),
          Expanded(child: _buildArrivalsList()),
        ],
      ),
    );
  }

  Widget _buildModalHandle(bool isDarkMode) {
    return Container(
      margin: const EdgeInsets.only(top: 12, bottom: 8),
      width: 40,
      height: 4,
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey.shade600 : Colors.black26,
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Widget _buildModalHeader(bool isDarkMode) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF22D400).withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.directions_bus,
              color: Color(0xFF22D400),
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.stop.n,
                  style: GoogleFonts.roboto(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: isDarkMode ? Colors.white : const Color(0xFF1A1A1A),
                  ),
                ),
                Text(
                  S.of(context).bussArrivals,
                  style: GoogleFonts.roboto(
                    fontSize: 14,
                    color: isDarkMode ? Colors.grey.shade300 : Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          _buildCloseButton(isDarkMode),
        ],
      ),
    );
  }

  Widget _buildCloseButton(bool isDarkMode) {
    return GestureDetector(
      onTap: () => Navigator.pop(context),
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          color: isDarkMode ? Colors.grey.shade800 : Colors.grey[100],
          shape: BoxShape.circle,
        ),
        child: Icon(
          Icons.close,
          color: isDarkMode ? Colors.grey.shade300 : Colors.black54,
          size: 18,
        ),
      ),
    );
  }

  Widget _buildArrivalsList() {
    return FutureBuilder<List<PanelModel>>(
      future: _panelsFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(color: Color(0xFF22D400)),
          );
        }

        if (snapshot.hasError) {
          return _buildErrorState();
        }

        final panels = snapshot.data ?? [];
        final departures = _extractAndSortDepartures(panels);

        if (departures.isEmpty) {
          return _buildEmptyState();
        }

        return _buildDeparturesList(departures);
      },
    );
  }

  Widget _buildErrorState() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 48),
          const SizedBox(height: 16),
          Text(
            'Error loading departures',
            style: GoogleFonts.roboto(
              fontSize: 16,
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          TextButton(
            onPressed: () => setState(() {}), // Rebuild to retry
            child:
                const Text('Retry', style: TextStyle(color: Color(0xFF22D400))),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.departure_board,
            color: isDarkMode ? Colors.grey.shade600 : Colors.grey[400],
            size: 48,
          ),
          const SizedBox(height: 16),
          Text(
            'No upcoming departures',
            style: GoogleFonts.roboto(
              fontSize: 16,
              color: isDarkMode ? Colors.grey.shade300 : Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  List<DepartureInfo> _extractAndSortDepartures(List<PanelModel> panels) {
    final allDepartures = <DepartureInfo>[];
    final now = DateTime.now();

    for (final panel in panels) {
      for (final line in panel.ls) {
        for (final departure in line.ts) {
          if (departure.d.isAfter(now)) {
            allDepartures.add(DepartureInfo(
              lineNumber: line.n,
              lineId: line.id,
              departure: departure,
            ));
          }
        }
      }
    }

    allDepartures.sort((a, b) => a.departure.d.compareTo(b.departure.d));
    return allDepartures.take(10).toList();
  }

  Widget _buildDeparturesList(List<DepartureInfo> departures) {
    return RefreshIndicator(
      onRefresh: () async {
        setState(() {});
        await _panelsFuture;
      },
      color: const Color(0xFF22D400),
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        itemCount: departures.length,
        itemBuilder: (context, index) => _buildDepartureItem(departures[index]),
      ),
    );
  }

  Widget _buildDepartureItem(DepartureInfo item) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final minutesUntil = item.departure.minutesUntilDeparture;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode
            ? Colors.grey.shade800.withOpacity(0.5)
            : Colors.grey[50],
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isDarkMode
              ? const Color(0xFF22D400).withOpacity(0.3)
              : const Color(0xFF22D400).withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          _buildBusNumberChip(item.lineNumber),
          const SizedBox(width: 16),
          Expanded(child: _buildDepartureInfo(item, isDarkMode)),
          _buildArrivalTimeChip(minutesUntil),
        ],
      ),
    );
  }

  Widget _buildBusNumberChip(String lineNumber) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: const Color(0xFF22D400),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        lineNumber,
        style: GoogleFonts.roboto(
          color: Colors.white,
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildDepartureInfo(DepartureInfo item, bool isDarkMode) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          item.departure.dir,
          style: GoogleFonts.roboto(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: isDarkMode ? Colors.white : const Color(0xFF1A1A1A),
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        _buildDepartureTimeInfo(item.departure, isDarkMode),
      ],
    );
  }

  Widget _buildDepartureTimeInfo(DepartureModel departure, bool isDarkMode) {
    return Row(
      children: [
        Icon(
          Icons.access_time,
          size: 14,
          color: isDarkMode ? Colors.grey.shade400 : Colors.grey[500],
        ),
        const SizedBox(width: 4),
        Text(
          _formatTime(departure.d),
          style: GoogleFonts.roboto(
            fontSize: 12,
            color: isDarkMode ? Colors.grey.shade300 : Colors.grey[600],
          ),
        ),
        if (departure.isByTimetable) ...[
          const SizedBox(width: 8),
          _buildStatusChip(S.of(context).scheduled, Colors.blue),
        ],
        if (departure.df != null && departure.df! != 0) ...[
          const SizedBox(width: 8),
          _buildDelayChip(departure.df!),
        ],
      ],
    );
  }

  Widget _buildStatusChip(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        text,
        style: GoogleFonts.roboto(
          fontSize: 10,
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildDelayChip(int delaySeconds) {
    final delayMinutes = (delaySeconds / 60).round();
    final isLate = delaySeconds > 0;
    final color = isLate ? Colors.orange : Colors.green;
    final text = isLate ? '+${delayMinutes}m' : '${delayMinutes}m';

    return _buildStatusChip(text, color);
  }

  Widget _buildArrivalTimeChip(int minutesUntil) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: _getArrivalTimeColor(minutesUntil),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        _formatMinutesUntil(minutesUntil),
        style: GoogleFonts.roboto(
          color: Colors.white,
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:'
        '${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _formatMinutesUntil(int minutes) {
    if (minutes <= 0) return S.of(context).now;
    if (minutes == 1) return '1 ${S.of(context).min}';
    return '$minutes ${S.of(context).min}';
  }

  Color _getArrivalTimeColor(int minutes) {
    if (minutes <= 0) return Colors.red;
    if (minutes <= 3) return const Color(0xFFFF5722);
    if (minutes <= 7) return const Color(0xFFFF9800);
    return const Color(0xFF22D400);
  }
}

// Helper class to combine line and departure info
class DepartureInfo {
  final String lineNumber;
  final int lineId;
  final DepartureModel departure;

  const DepartureInfo({
    required this.lineNumber,
    required this.lineId,
    required this.departure,
  });
}
