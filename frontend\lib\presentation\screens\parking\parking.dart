import 'package:flutter/material.dart';
import 'package:frontend/l10n/generated/l10n.dart';
import 'package:frontend/models/get_parking_command.dart';
import 'package:frontend/providers/parking_provider.dart';
import 'package:frontend/providers/theme_provider.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart' as gmaps;
import 'package:flutter/services.dart' show rootBundle;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:ui' as ui;
import 'dart:typed_data';
import 'package:url_launcher/url_launcher.dart';

class ParkingScreen extends ConsumerStatefulWidget {
  const ParkingScreen({super.key});

  @override
  _ParkingScreenState createState() => _ParkingScreenState();
}

class _ParkingScreenState extends ConsumerState<ParkingScreen> {
  gmaps.GoogleMapController? _mapController;
  final gmaps.LatLng _myCurrentLocation = const gmaps.LatLng(42.4842, 26.5035);
  Set<gmaps.Marker> _markers = {};
  Set<gmaps.Polygon> _polygons = {};
  String? _mapStyle;
  bool _isMapStyleLoaded = false;
  gmaps.BitmapDescriptor? _parkingIcon;
  gmaps.BitmapDescriptor? _freeParkingIcon;
  double _currentZoom = 14.0;
  final double _zoomThreshold =
      19.0; // Threshold for showing individual markers vs icons

  @override
  void initState() {
    super.initState();
    _loadCustomParkingIcon();
    _loadCustomFreeParkingIcon();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (mounted) {
      _loadMapStyle();
    }
  }

  Future<void> _loadMapStyle() async {
    if (!mounted) return;

    final themeMode = ref.read(themeNotifierProvider);
    final isDarkMode = themeMode == ThemeMode.dark;

    try {
      // Load the style based on dark or light mode
      if (isDarkMode) {
        _mapStyle =
            await rootBundle.loadString('assets/map_styles/dark_map.json');
      } else {
        _mapStyle =
            await rootBundle.loadString('assets/map_styles/light_map.json');
      }

      // Set the map style and update the state
      if (mounted) {
        setState(() {
          _isMapStyleLoaded = true;
        });
      }
    } catch (e) {
      print("Error loading map style: $e");
      if (mounted) {
        setState(() {
          _isMapStyleLoaded = true; // Continue without custom style
        });
      }
    }
  }

  Future<void> _loadCustomParkingIcon() async {
    try {
      // Load your custom parking SVG icon (same way as repair screen)
      final svgString =
          await rootBundle.loadString('assets/icons/parking-icon.svg');
      final pictureInfo =
          await vg.loadPicture(SvgStringLoader(svgString), null);

      final ui.PictureRecorder recorder = ui.PictureRecorder();
      final Canvas canvas = Canvas(recorder);

      final double logicalSize = 35.0; // Size of the marker
      final double pixelRatio = MediaQuery.of(context).devicePixelRatio;
      final double bitmapWidth = logicalSize * pixelRatio;
      final double bitmapHeight = logicalSize * pixelRatio;

      // Calculate scale factor to fit the SVG in the desired size
      final double scaleX = bitmapWidth / pictureInfo.size.width;
      final double scaleY = bitmapHeight / pictureInfo.size.height;
      final double scale = scaleX < scaleY ? scaleX : scaleY;
      final double adjustedScale = scale / pixelRatio;

      final double translateX =
          (logicalSize - (pictureInfo.size.width * adjustedScale)) /
              2 *
              pixelRatio;
      final double translateY =
          (logicalSize - (pictureInfo.size.height * adjustedScale)) /
              2 *
              pixelRatio;

      // Transform and draw
      canvas.translate(translateX, translateY);
      canvas.scale(adjustedScale * pixelRatio);
      canvas.drawPicture(pictureInfo.picture);

      // Convert to image
      final ui.Image image = await recorder.endRecording().toImage(
            bitmapWidth.toInt(),
            bitmapHeight.toInt(),
          );

      final ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);
      if (byteData != null) {
        final Uint8List uint8List = byteData.buffer.asUint8List();
        final gmaps.BitmapDescriptor icon =
            gmaps.BitmapDescriptor.fromBytes(uint8List);

        if (mounted) {
          setState(() {
            _parkingIcon = icon;
          });
        }
      }
    } catch (e) {
      print("Error loading custom parking icon: $e");
      // Fallback to default marker if SVG loading fails
      _parkingIcon = gmaps.BitmapDescriptor.defaultMarkerWithHue(
          gmaps.BitmapDescriptor.hueBlue);
    }
  }

  Future<void> _loadCustomFreeParkingIcon() async {
    try {
      // Load your custom free parking SVG icon
      final svgString =
          await rootBundle.loadString('assets/icons/free-parking-icon.svg');
      final pictureInfo =
          await vg.loadPicture(SvgStringLoader(svgString), null);

      final ui.PictureRecorder recorder = ui.PictureRecorder();
      final Canvas canvas = Canvas(recorder);

      final double logicalSize = 35.0; // Size of the marker
      final double pixelRatio = MediaQuery.of(context).devicePixelRatio;
      final double bitmapWidth = logicalSize * pixelRatio;
      final double bitmapHeight = logicalSize * pixelRatio;

      // Calculate scale factor to fit the SVG in the desired size
      final double scaleX = bitmapWidth / pictureInfo.size.width;
      final double scaleY = bitmapHeight / pictureInfo.size.height;
      final double scale = scaleX < scaleY ? scaleX : scaleY;
      final double adjustedScale = scale / pixelRatio;

      final double translateX =
          (logicalSize - (pictureInfo.size.width * adjustedScale)) /
              2 *
              pixelRatio;
      final double translateY =
          (logicalSize - (pictureInfo.size.height * adjustedScale)) /
              2 *
              pixelRatio;

      // Transform and draw
      canvas.translate(translateX, translateY);
      canvas.scale(adjustedScale * pixelRatio);
      canvas.drawPicture(pictureInfo.picture);

      // Convert to image
      final ui.Image image = await recorder.endRecording().toImage(
            bitmapWidth.toInt(),
            bitmapHeight.toInt(),
          );

      final ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);
      if (byteData != null) {
        final Uint8List uint8List = byteData.buffer.asUint8List();
        final gmaps.BitmapDescriptor icon =
            gmaps.BitmapDescriptor.fromBytes(uint8List);

        if (mounted) {
          setState(() {
            _freeParkingIcon = icon;
          });
        }
      }
    } catch (e) {
      print("Error loading custom free parking icon: $e");
      // Fallback to default marker if SVG loading fails
      _freeParkingIcon = gmaps.BitmapDescriptor.defaultMarkerWithHue(
          gmaps.BitmapDescriptor.hueGreen);
    }
  }

  // Helper method to calculate the centroid of a polygon
  gmaps.LatLng _calculatePolygonCentroid(List<gmaps.LatLng> points) {
    double centroidLat = 0;
    double centroidLng = 0;

    for (gmaps.LatLng point in points) {
      centroidLat += point.latitude;
      centroidLng += point.longitude;
    }

    return gmaps.LatLng(
      centroidLat / points.length,
      centroidLng / points.length,
    );
  }

  // Navigation method - opens external maps for directions
  Future<void> _openNavigation(ParkingModel parking) async {
    double lat, lng;

    // Determine coordinates - use centroid for blue zones, direct coordinates for single spots
    if (parking.borderPoints.isNotEmpty) {
      List<gmaps.LatLng> polygonPoints = parking.borderPoints.map((point) {
        return gmaps.LatLng(point.latitude, point.longitude);
      }).toList();
      gmaps.LatLng centroid = _calculatePolygonCentroid(polygonPoints);
      lat = centroid.latitude;
      lng = centroid.longitude;
    } else {
      lat = parking.latitude;
      lng = parking.longitude;
    }

    // Create Google Maps URL with navigation
    final String url =
        'https://www.google.com/maps/dir/?api=1&destination=$lat,$lng';

    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        throw 'Could not launch navigation';
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Could not open navigation: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Show parking details dialog
  void _showParkingDialog(ParkingModel parking) {
    final themeMode = ref.read(themeNotifierProvider);
    final isDarkMode = themeMode == ThemeMode.dark;

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return Dialog(
          backgroundColor: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            constraints: const BoxConstraints(
              maxWidth: 400,
              maxHeight: 500,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with close button
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? const Color(0xFF2A2A2A)
                        : const Color(0xFFF5F5F5),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.local_parking,
                        color: Colors.blue,
                        size: 24,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          S.of(context).parkingZone,
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: isDarkMode
                                ? Colors.white
                                : const Color(0xFF424242),
                          ),
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.of(dialogContext).pop(),
                        icon: Icon(
                          Icons.close,
                          color: isDarkMode ? Colors.white70 : Colors.black54,
                        ),
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ],
                  ),
                ),

                // Content
                Flexible(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Parking type
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: parking.borderPoints.isNotEmpty
                                ? Colors.blue.withOpacity(0.2)
                                : Colors.green.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: parking.borderPoints.isNotEmpty
                                  ? Colors.blue
                                  : Colors.green,
                              width: 1,
                            ),
                          ),
                          child: Text(
                            parking.borderPoints.isNotEmpty
                                ? S.of(context).blueZone
                                : S.of(context).freeParking,
                            style: TextStyle(
                              color: parking.borderPoints.isNotEmpty
                                  ? Colors.blue[700]
                                  : Colors.green[700],
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Description or info
                        Text(
                          S.of(context).parkingInformations,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: isDarkMode
                                ? Colors.white
                                : const Color(0xFF424242),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: isDarkMode
                                ? const Color(0xFF2A2A2A)
                                : const Color(0xFFF8F9FA),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: isDarkMode
                                  ? const Color(0xFF3A3A3A)
                                  : const Color(0xFFE0E0E0),
                            ),
                          ),
                          child: Text(
                            parking.borderPoints.isNotEmpty
                                ? S.of(context).blueZoneParkingMessage
                                : S.of(context).freeParkingMessage,
                            style: TextStyle(
                              fontSize: 14,
                              color:
                                  isDarkMode ? Colors.white70 : Colors.black87,
                              height: 1.5,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Action buttons
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? const Color(0xFF2A2A2A)
                        : const Color(0xFFF5F5F5),
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(16),
                      bottomRight: Radius.circular(16),
                    ),
                  ),
                  child: Row(
                    children: [
                      // Navigate button
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            Navigator.of(dialogContext).pop();
                            _openNavigation(parking);
                          },
                          icon: const Icon(
                            Icons.navigation,
                            size: 18,
                            color: Colors.white,
                          ),
                          label: Text(S.of(context).navigate),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF22D400),
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(width: 12),

                      // Close button
                      TextButton(
                        onPressed: () => Navigator.of(dialogContext).pop(),
                        style: TextButton.styleFrom(
                          foregroundColor:
                              isDarkMode ? Colors.white70 : Colors.black54,
                          padding: const EdgeInsets.symmetric(
                              vertical: 12, horizontal: 16),
                        ),
                        child: Text(S.of(context).close),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _updateParkingDisplay(List<ParkingModel> parkingLots) {
    if (!mounted || _parkingIcon == null) {
      return;
    }

    Set<gmaps.Marker> loadedMarkers = {};
    Set<gmaps.Polygon> loadedPolygons = {};

    for (var parking in parkingLots) {
      // If parking has border points, create a polygon (blue zone)
      if (parking.borderPoints.isNotEmpty) {
        List<gmaps.LatLng> polygonPoints = parking.borderPoints.map((point) {
          return gmaps.LatLng(point.latitude, point.longitude);
        }).toList();

        if (polygonPoints.length >= 3) {
          // Only create polygon when zoomed in (above threshold)
          if (_currentZoom >= _zoomThreshold) {
            gmaps.Polygon polygon = gmaps.Polygon(
              polygonId: gmaps.PolygonId('parking_zone_${parking.id}'),
              points: polygonPoints,
              strokeColor: Colors.blue,
              strokeWidth: 3,
              fillColor: Colors.blue.withValues(alpha: 0.2),
              consumeTapEvents: true,
              onTap: () {
                print('TAPPPEDDDDDD');
                if (mounted) {
                  _showParkingDialog(parking);
                }
              },
            );

            loadedPolygons.add(polygon);
          }

          // Calculate centroid for marker placement
          gmaps.LatLng centroid = _calculatePolygonCentroid(polygonPoints);

          // Create parking icon marker (visible when zoomed out)
          if (_currentZoom < _zoomThreshold) {
            gmaps.Marker iconMarker = gmaps.Marker(
              markerId: gmaps.MarkerId('parking_zone_icon_${parking.id}'),
              position: centroid,
              icon: _parkingIcon!,
              onTap: () {
                if (mounted) {
                  _showParkingDialog(parking);
                }
              },
            );
            loadedMarkers.add(iconMarker);
          }
        } else {
          print("   - ❌ Not enough points for polygon (need 3+)");
        }
      }
      // If parking has no border points but has coordinates, create a marker
      else if (parking.latitude != 0.0 || parking.longitude != 0.0) {
        print(
            "   - Creating marker at ${parking.latitude}, ${parking.longitude}");
        gmaps.Marker marker = gmaps.Marker(
          markerId: gmaps.MarkerId('parking_${parking.id}'),
          position: gmaps.LatLng(parking.latitude, parking.longitude),
          icon: _freeParkingIcon!,
          onTap: () {
            if (mounted) {
              _showParkingDialog(parking);
            }
          },
        );

        loadedMarkers.add(marker);
      } else {
        print("   - ❌ No valid coordinates and no border points");
      }
    }

    if (mounted) {
      setState(() {
        _markers = loadedMarkers;
        _polygons = loadedPolygons;
      });
    }
  }

  void _onMapCreated(gmaps.GoogleMapController controller) {
    _mapController = controller;
  }

  void _onCameraMove(gmaps.CameraPosition position) {
    final newZoom = position.zoom;

    // Only update if zoom level crosses the threshold
    if ((_currentZoom < _zoomThreshold && newZoom >= _zoomThreshold) ||
        (_currentZoom >= _zoomThreshold && newZoom < _zoomThreshold)) {
      setState(() {
        _currentZoom = newZoom;
      });

      // Refresh parking display with new zoom level
      final parkingLots = ref.read(fetchAllParkingLotsProvider);
      parkingLots.whenData((parkings) {
        _updateParkingDisplay(parkings);
      });
    } else {
      _currentZoom = newZoom;
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeMode = ref.watch(themeNotifierProvider);
    final isDarkMode = themeMode == ThemeMode.dark;

    // Watch for parking data changes
    final parkingLots = ref.watch(fetchAllParkingLotsProvider);

    if (_isMapStyleLoaded && _mapController != null) {
      final currentThemeMode = ref.read(themeNotifierProvider);
      final currentIsDarkMode = currentThemeMode == ThemeMode.dark;
      if (currentIsDarkMode != isDarkMode) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            _loadMapStyle();
          }
        });
      }
    }

    return Scaffold(
      body: parkingLots.when(
        data: (parkings) {
          // Debug: Print each parking lot details
          for (int i = 0; i < parkings.length; i++) {
            final p = parkings[i];

            if (p.borderPoints.isNotEmpty) {
              for (int j = 0; j < p.borderPoints.length; j++) {}
            }
          }

          if (mounted) {
            _updateParkingDisplay(parkings);
          }
          return gmaps.GoogleMap(
            onMapCreated: _onMapCreated,
            onCameraMove: _onCameraMove,
            initialCameraPosition: gmaps.CameraPosition(
              target: _myCurrentLocation,
              zoom: 14.0,
            ),
            markers: _markers,
            polygons: _polygons,
            mapType: gmaps.MapType.normal,
            myLocationEnabled: true,
            compassEnabled: true,
            zoomControlsEnabled: true,
            tiltGesturesEnabled: true,
            mapToolbarEnabled: false,
            style: _mapStyle,
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (err, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 48,
                color: isDarkMode ? Colors.red[300] : Colors.red[600],
              ),
              const SizedBox(height: 16),
              Text(
                'Error loading parking data',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: isDarkMode ? Colors.white : const Color(0xFF424242),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                err.toString(),
                style: TextStyle(
                  fontSize: 14,
                  color: isDarkMode ? Colors.white70 : Colors.black54,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }
}
