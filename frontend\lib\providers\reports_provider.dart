import 'package:frontend/infrastructure/reports/reports_service.dart';
import 'package:frontend/models/enum_command.dart';
import 'package:frontend/models/reports_service_command.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'reports_provider.g.dart';

// Report submission state
@riverpod
class ReportSubmissionState extends _$ReportSubmissionState {
  @override
  AsyncValue<bool> build() => const AsyncValue.data(false);

  Future<void> submitReport({
    required String title,
    required String phoneNumber,
    required String description,
    required String address,
    required String type,
    required List<FileModel> files,
  }) async {
    state = const AsyncValue.loading();

    try {
      final success = await ReportsMobileService.submitReport(
        title: title,
        phoneNumber: phoneNumber,
        description: description,
        address: address,
        type: type,
        files: files,
      );

      state = AsyncValue.data(success);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<EnumModel?> getReportTypes() async {
    try {
      return await ReportsMobileService.getReportTypes();
    } catch (error) {
      print('Error fetching report types: $error');
      return null;
    }
  }

  void reset() {
    state = const AsyncValue.data(false);
  }
}
