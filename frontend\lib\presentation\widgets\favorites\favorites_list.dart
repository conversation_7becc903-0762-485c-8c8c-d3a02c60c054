import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/models/favourites_base_menu_item_command.dart';
import 'package:frontend/presentation/screens/general-information/general_information_screen.dart';
import 'package:frontend/providers/theme_provider.dart';
import 'package:frontend/l10n/generated/l10n.dart';
import '../../../domain/enums/favorite_filter_type.dart';
import '../../../providers/favorites/favorites_filter_provider.dart';
import '../../../providers/favorites/favourites_provider.dart';
import '../../../providers/favorites/search_query_provider.dart';
import '../../screens/tourism/tourism_screen.dart';
import 'favorites_list_item.dart';

class FavoritesList extends ConsumerWidget {
  const FavoritesList({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isDarkMode = ref.watch(themeNotifierProvider) == ThemeMode.dark;
    final favAsync = ref.watch(favouritesItemsProvider);
    final searchQuery = ref.watch(searchQueryProvider).toLowerCase();
    final selectedFilter = ref.watch(favouriteStateProvider);

    return favAsync.when(
      data: (favs) {
        // Explicitly type the list as BaseMenuItem
        final List<BaseMenuItem> allItems = <BaseMenuItem>[
          ...TourismScreen.tourismMenuItems,
          ...GeneralInformationScreen.generalInformationMenuItems,
        ];

        final matching = allItems.where((item) {
          final matchesSearch =
              item.label(context).toLowerCase().contains(searchQuery);
          final matchesFavorite = favs.contains(item.id);
          final matchesFilter = selectedFilter == FavoriteFilterType.all ||
              item.type == selectedFilter;

          return matchesSearch && matchesFavorite && matchesFilter;
        }).toList();

        if (matching.isEmpty) {
          return Center(
            child: Text(
              S.of(context).noFavorites,
              style: TextStyle(
                color: isDarkMode ? Colors.white70 : Colors.black54,
                fontSize: 16,
              ),
            ),
          );
        }

        return ListView.builder(
          itemCount: matching.length,
          itemBuilder: (context, index) {
            final item = matching[index];
            return FavoriteListItem(item: item);
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (e, _) => const Center(child: Text('Error loading favorites')),
    );
  }
}
