import 'package:dio/dio.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:frontend/models/get_message_command.dart';

class AnnouncementService {
  static final Dio _dio = Dio(BaseOptions(
    baseUrl: dotenv.env['BASE_URL']!, // Load BASE_URL from .env
  ));

  /// **Fetch All Announcements**
  static Future<List<MessageModel>> fetchAnnouncements({
    String? languageCode,
  }) async {
    const String announcementEndpoint = 'Announcements';

    // Build query parameters dynamically
    final Map<String, dynamic> queryParams = {};

    if (languageCode != null && languageCode.isNotEmpty) {
      queryParams['languageCode'] = languageCode;
    }

    try {
      print('ANNOUNCEMENT REQUEST: Fetching all announcements');
      print('PARAMS FOR REQUEST: $queryParams');

      final response = await _dio.get(
        announcementEndpoint,
        queryParameters: queryParams.isNotEmpty ? queryParams : null,
      );

      if (response.statusCode == 200) {
        print('ANNOUNCEMENT RESPONSE: ${response.data}');

        List<dynamic> jsonData = response.data;
        return jsonData.map((item) => MessageModel.fromJson(item)).toList();
      } else {
        throw Exception(
            'Failed to load announcements with status ${response.statusCode}');
      }
    } on DioException catch (e) {
      print("❌ Announcement Service Dio Error: ${e.response?.statusCode}");
      print("📩 Response Data: ${e.response?.data}");
      print("📝 Error Message: ${e.message}");
      return [];
    } catch (e) {
      print("⚠ Announcement Service Unexpected Error: $e");
      return [];
    }
  }
}
