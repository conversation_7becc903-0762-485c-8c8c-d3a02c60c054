import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/l10n/generated/l10n.dart';
import 'package:frontend/presentation/widgets/html_utils.dart';
import 'package:frontend/providers/messages_provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:frontend/models/get_message_command.dart';

// Removed read status providers and logic

class MessagesScreen extends ConsumerWidget {
  const MessagesScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final announcementsAsync = ref.watch(fetchAllAnnouncementsProvider);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              // Header Section with Background Image
              Container(
                width: double.infinity,
                height:
                    MediaQuery.of(context).orientation == Orientation.landscape
                        ? 150.0
                        : MediaQuery.of(context).size.height * 0.25,
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage('assets/images/settings_image.png'),
                    fit: BoxFit.cover,
                    colorFilter: ColorFilter.mode(
                      isDarkMode
                          ? Colors.white.withValues(alpha: 0.3)
                          : Color(0x3322D400),
                      BlendMode.darken,
                    ),
                  ),
                ),
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withValues(alpha: 0.3),
                      ],
                    ),
                  ),
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.mail_outline,
                          size: MediaQuery.of(context).orientation ==
                                  Orientation.landscape
                              ? 30
                              : 40,
                          color: Colors.white,
                        ),
                        SizedBox(
                          height: MediaQuery.of(context).orientation ==
                                  Orientation.landscape
                              ? 6
                              : 8,
                        ),
                        Text(
                          S.of(context).announcementHeaderMessage,
                          textAlign: TextAlign.center,
                          style: GoogleFonts.roboto(
                            fontSize: MediaQuery.of(context).orientation ==
                                    Orientation.landscape
                                ? 16
                                : 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // Content Section
              Padding(
                padding:
                    MediaQuery.of(context).orientation == Orientation.landscape
                        ? const EdgeInsets.all(16.0)
                        : const EdgeInsets.all(20.0),
                child: announcementsAsync.when(
                  data: (announcements) => _buildContent(
                    context,
                    ref,
                    isDarkMode,
                    announcements,
                  ),
                  loading: () => _buildLoadingState(context, isDarkMode),
                  error: (error, stackTrace) => _buildErrorState(
                    context,
                    ref,
                    isDarkMode,
                    error.toString(),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContent(
    BuildContext context,
    WidgetRef ref,
    bool isDarkMode,
    List<MessageModel> announcements,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header (removed Mark All as Read button)
        Text(
          S.of(context).recentAnnouncements,
          style: GoogleFonts.roboto(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: isDarkMode ? Colors.white : Color(0xFF626262),
          ),
        ),

        SizedBox(height: 16),

        // Announcements List
        if (announcements.isEmpty)
          _buildEmptyState(context, isDarkMode)
        else
          ...announcements.map((announcement) => Padding(
                padding: const EdgeInsets.only(bottom: 12.0),
                child: _buildMessageCard(
                  context: context,
                  isDarkMode: isDarkMode,
                  announcement: announcement,
                  onTap: () {
                    _showMessageDetail(context, ref, announcement);
                  },
                ),
              )),
      ],
    );
  }

  Widget _buildLoadingState(BuildContext context, bool isDarkMode) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          S.of(context).recentAnnouncements,
          style: GoogleFonts.roboto(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: isDarkMode ? Colors.white : Color(0xFF626262),
          ),
        ),
        SizedBox(height: 32),
        Center(
          child: Column(
            children: [
              CircularProgressIndicator(
                color: Color(0xFF22D400),
              ),
              SizedBox(height: 16),
              Text(
                S.of(context).loadingAnnouncements,
                style: GoogleFonts.roboto(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: isDarkMode ? Colors.white70 : Color(0xFF888888),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildErrorState(
      BuildContext context, WidgetRef ref, bool isDarkMode, String error) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          S.of(context).recentAnnouncements,
          style: GoogleFonts.roboto(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: isDarkMode ? Colors.white : Color(0xFF626262),
          ),
        ),
        SizedBox(height: 32),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: isDarkMode ? Color(0xFF333333) : Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 6,
                offset: Offset(0, 3),
              ),
            ],
          ),
          child: Column(
            children: [
              Icon(
                Icons.error_outline,
                size: 48,
                color: Colors.red,
              ),
              SizedBox(height: 16),
              Text(
                S.of(context).failedToLoadAnnouncements,
                style: GoogleFonts.roboto(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: isDarkMode ? Colors.white : Color(0xFF626262),
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 8),
              Text(
                S.of(context).pleaseCheckYourConnection,
                style: GoogleFonts.roboto(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: isDarkMode ? Colors.white70 : Color(0xFF888888),
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  //ref.refresh(fetchAllAnnouncementsProvider);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Color(0xFF22D400),
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  'Retry',
                  style: GoogleFonts.roboto(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMessageCard({
    required BuildContext context,
    required bool isDarkMode,
    required MessageModel announcement,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isDarkMode ? Color(0xFF333333) : Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 6,
              offset: Offset(0, 3),
            ),
          ],
        ),
        child: Row(
          children: [
            // Message icon
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Color(0xFF22D400).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(
                Icons.mail_outline,
                color: Color(0xFF22D400),
                size: 20,
              ),
            ),
            SizedBox(width: 12),

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    announcement.title,
                    style: GoogleFonts.roboto(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: isDarkMode ? Colors.white : Color(0xFF626262),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 4),
                  // Use HtmlUtils for content preview (strip HTML for preview)
                  Text(
                    _stripHtmlTags(announcement.content),
                    style: GoogleFonts.roboto(
                      fontSize: 13,
                      fontWeight: FontWeight.w400,
                      color: isDarkMode ? Colors.white70 : Color(0xFF888888),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 4),
                  Text(
                    _formatTime(announcement.date, context),
                    style: GoogleFonts.roboto(
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      color: isDarkMode ? Colors.white60 : Color(0xFFAAAAAA),
                    ),
                  ),
                ],
              ),
            ),

            // Arrow icon
            Icon(
              Icons.arrow_forward_ios,
              color: Color(0xFF22D400),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(40),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.mail_outline,
            size: 64,
            color: isDarkMode ? Colors.white30 : Color(0xFFCCCCCC),
          ),
          SizedBox(height: 16),
          Text(
            S.of(context).nouAnnouncements,
            style: GoogleFonts.roboto(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: isDarkMode ? Colors.white60 : Color(0xFF888888),
            ),
          ),
          SizedBox(height: 8),
          Text(
            S.of(context).noAnnouncementsMessage,
            textAlign: TextAlign.center,
            style: GoogleFonts.roboto(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: isDarkMode ? Colors.white70 : Color(0xFFAAAAAA),
            ),
          ),
        ],
      ),
    );
  }

  void _showMessageDetail(
      BuildContext context, WidgetRef ref, MessageModel announcement) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => MessageDetailModal(announcement: announcement),
    );
  }

  String _formatTime(DateTime dateTime, BuildContext context) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 60) {
      return '${S.of(context).ago} ${difference.inMinutes}${S.of(context).m}';
    } else if (difference.inHours < 24) {
      return '${S.of(context).ago} ${difference.inHours}${S.of(context).h}';
    } else if (difference.inDays < 7) {
      // Handle singular/plural for days
      if (difference.inDays == 1) {
        return '${S.of(context).ago} ${difference.inDays} ${S.of(context).day}';
      } else {
        return '${S.of(context).ago} ${difference.inDays} ${S.of(context).days}';
      }
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }

  // Helper method to strip HTML tags for preview text
  String _stripHtmlTags(String htmlString) {
    RegExp exp = RegExp(r"<[^>]*>", multiLine: true, caseSensitive: true);
    return htmlString.replaceAll(exp, '').trim();
  }
}

class MessageDetailModal extends StatelessWidget {
  final MessageModel announcement;

  const MessageDetailModal({Key? key, required this.announcement})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: BoxDecoration(
        color: isDarkMode ? Color(0xFF2A2A2A) : Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            margin: EdgeInsets.only(top: 12),
            decoration: BoxDecoration(
              color: isDarkMode ? Colors.white30 : Color(0xFFCCCCCC),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: Color(0xFF22D400).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(24),
                  ),
                  child: Icon(
                    Icons.mail_outline,
                    color: Color(0xFF22D400),
                    size: 24,
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        announcement.title,
                        style: GoogleFonts.roboto(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: isDarkMode ? Colors.white : Color(0xFF626262),
                        ),
                      ),
                      Text(
                        _formatTime(announcement.date, context),
                        style: GoogleFonts.roboto(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          color:
                              isDarkMode ? Colors.white60 : Color(0xFFAAAAAA),
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: Icon(
                    Icons.close,
                    color: isDarkMode ? Colors.white60 : Color(0xFF888888),
                  ),
                ),
              ],
            ),
          ),

          Divider(
            color: isDarkMode ? Colors.white10 : Color(0xFFEEEEEE),
            thickness: 1,
          ),

          // Content with HTML support
          Expanded(
            child: Padding(
              padding: EdgeInsets.all(20),
              child: SingleChildScrollView(
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: isDarkMode ? Color(0xFF333333) : Color(0xFFF8F8F8),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: EdgeInsets.all(16),
                  child: HtmlUtils.createStyledHtmlWidget(
                    htmlContent: announcement.content,
                    isDarkMode: isDarkMode,
                    baseFontSize: 15.0,
                    lineHeight: 1.5,
                    fontFamily: 'Roboto',
                    onLinkTap: (url, attributes, element) {
                      // Handle link navigation here
                      print('Link tapped: $url');
                      // You can add url_launcher here if needed
                    },
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatTime(DateTime dateTime, BuildContext context) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 60) {
      return '${S.of(context).ago} ${difference.inMinutes}${S.of(context).m}';
    } else if (difference.inHours < 24) {
      return '${S.of(context).ago} ${difference.inHours}${S.of(context).h}';
    } else if (difference.inDays < 7) {
      // Handle singular/plural for days
      if (difference.inDays == 1) {
        return '${S.of(context).ago} ${difference.inDays} ${S.of(context).day}';
      } else {
        return '${S.of(context).ago} ${difference.inDays} ${S.of(context).days}';
      }
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }
}
