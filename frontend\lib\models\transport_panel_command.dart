import 'package:frontend/models/transport_departure_command.dart';

class PanelModel {
  final int pid;
  final List<LineModel> ls;

  PanelModel({
    required this.pid,
    required this.ls,
  });

  factory PanelModel.fromJson(Map<String, dynamic> json) {
    return PanelModel(
      pid: json['pid'],
      ls: (json['ls'] as List<dynamic>)
          .map((line) => LineModel.fromJson(line))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'pid': pid,
      'ls': ls.map((line) => line.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'PanelModel(pid: $pid, ls: $ls)';
  }
}

class LineModel {
  final int id;
  final String n;
  final List<DepartureModel> ts;

  LineModel({
    required this.id,
    required this.n,
    required this.ts,
  });

  factory LineModel.fromJson(Map<String, dynamic> json) {
    return LineModel(
      id: json['id'],
      n: json['n'],
      ts: (json['ts'] as List<dynamic>)
          .map((departure) => DepartureModel.fromJson(departure))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'n': n,
      'ts': ts.map((departure) => departure.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'LineModel(id: $id, n: $n, ts: $ts)';
  }
}
