import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../providers/language_provider.dart';
import '../../providers/theme_provider.dart';
import '../../providers/auth_providers.dart';
import '../../providers/notifications_provider.dart';
import '../../l10n/generated/l10n.dart';

class SettingsOptions extends ConsumerWidget {
  final UserAuthState userState; // ✅ Accept userState as parameter

  const SettingsOptions({
    super.key,
    required this.userState,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeNotifier = ref.read(themeNotifierProvider.notifier);
    final themeMode = ref.watch(themeNotifierProvider);
    final langNotifier = ref.read(languageNotifierProvider.notifier);
    final currentLocale = ref.watch(languageNotifierProvider);
    final authNotifier = ref.read(authNotifierProvider.notifier);
    final notificationNotifier =
        ref.read(notificationNotifierProvider.notifier);
    final notificationSettings = ref.watch(notificationNotifierProvider);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          _buildSettingsTile(
            context,
            title: S.of(context).language,
            subtitle: _getLanguageName(currentLocale.languageCode),
            icon: Icons.language,
            onTap: () => _showLanguagePicker(context, langNotifier),
          ),
          const SizedBox(height: 10),

          _buildSettingsTile(
            context,
            title: S
                .of(context)
                .notifications, // You can add this to your localization
            subtitle: _getNotificationStatus(context, notificationSettings),
            icon: Icons.notifications,
            onTap: () => _showNotificationsPicker(
                context, notificationNotifier, notificationSettings),
          ),
          const SizedBox(height: 10),

          _buildSettingsTile(
            context,
            title: S.of(context).darkMode,
            icon: themeMode == ThemeMode.dark
                ? Icons.dark_mode
                : Icons.light_mode,
            trailing: Switch(
              value: themeMode == ThemeMode.dark,
              onChanged: (value) => themeNotifier.toggleTheme(),
            ),
          ),
          const SizedBox(height: 20),

          // ✅ Conditional Button - Register or Logout
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () async {
                if (userState.isAuthenticated) {
                  // Show logout confirmation dialog
                  final shouldLogout = await _showLogoutDialog(context);
                  if (shouldLogout == true) {
                    await authNotifier.logoutUser();
                  }
                } else {
                  // Navigate to registration
                  context.push('/registration');
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor:
                    userState.isAuthenticated ? Colors.red : Colors.green,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8)),
              ),
              child: Text(
                userState.isAuthenticated
                    ? "Logout" // You can add this to your localization
                    : S.of(context).registerButton,
                style: const TextStyle(fontSize: 16, color: Colors.white),
              ),
            ),
          ),

          // ✅ Show Login button if not authenticated
          if (!userState.isAuthenticated) ...[
            const SizedBox(height: 10),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton(
                onPressed: () {
                  context.push('/login'); // Navigate to login screen
                },
                style: OutlinedButton.styleFrom(
                  side: const BorderSide(color: Colors.green),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8)),
                ),
                child: Text(
                  S.of(context).login,
                  style: const TextStyle(fontSize: 16, color: Colors.green),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// **Show Logout Confirmation Dialog**
  Future<bool?> _showLogoutDialog(BuildContext context) {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text("Logout"),
        content: const Text("Are you sure you want to logout?"),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text("Cancel"),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text("Logout"),
          ),
        ],
      ),
    );
  }

  /// **Reusable Settings Tile**
  Widget _buildSettingsTile(
    BuildContext context, {
    required String title,
    String? subtitle,
    required IconData icon,
    VoidCallback? onTap,
    Widget? trailing,
  }) {
    final theme = Theme.of(context);

    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        leading: Icon(icon, color: theme.primaryColor),
        title: Text(title,
            style: TextStyle(
                fontSize: 16, color: theme.textTheme.bodyLarge?.color)),
        subtitle: subtitle != null
            ? Text(subtitle,
                style: TextStyle(color: theme.textTheme.bodySmall?.color))
            : null,
        trailing:
            trailing ?? const Icon(Icons.chevron_right, color: Colors.black45),
        onTap: onTap,
      ),
    );
  }

  /// **Language Picker Modal**
  void _showLanguagePicker(
      BuildContext context, LanguageNotifier langNotifier) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                S.of(context).language,
                style:
                    const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 10),
              ListTile(
                title: const Text("English"),
                onTap: () {
                  langNotifier.setLanguage(const Locale('en'));
                  Navigator.pop(context);
                },
              ),
              ListTile(
                title: const Text("Български"),
                onTap: () {
                  langNotifier.setLanguage(const Locale('bg'));
                  Navigator.pop(context);
                },
              ),
              ListTile(
                title: const Text("Русский"), // Add this
                onTap: () {
                  langNotifier.setLanguage(const Locale('ru'));
                  Navigator.pop(context);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  /// **Notifications Picker Modal** - FIXED VERSION
  void _showNotificationsPicker(BuildContext context,
      NotificationNotifier notifier, NotificationSettings settings) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true, // ✅ Allow custom height control
      builder: (context) {
        return Consumer(
          builder: (context, ref, child) {
            // Watch the provider inside the modal to get real-time updates
            final currentSettings = ref.watch(notificationNotifierProvider);

            return Container(
              // ✅ Constrain height to prevent overflow
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height *
                    0.85, // Max 85% of screen
              ),
              child: SingleChildScrollView(
                // ✅ Make it scrollable
                child: Container(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        S.of(context).notifications,
                        style: const TextStyle(
                            fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 10),

                      // ✅ System permission warning banner
                      if (!currentSettings.hasSystemPermission)
                        Container(
                          margin: const EdgeInsets.only(bottom: 16),
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.orange.withOpacity(0.1),
                            border: Border.all(color: Colors.orange),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.warning, color: Colors.orange),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      S.of(context).systemPermissionRequired,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: Colors.orange,
                                      ),
                                    ),
                                    Text(
                                      S.of(context).systemPermissionMessage,
                                      style: const TextStyle(fontSize: 12),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),

                      ListTile(
                        leading: const Icon(Icons.article),
                        title: Text(S.of(context).news),
                        subtitle: Text(S.of(context).newsDescription),
                        trailing: Switch(
                          value: currentSettings.news,
                          onChanged: currentSettings.hasSystemPermission
                              ? (value) => notifier.setNewsNotification(value)
                              : (value) {
                                  if (value) {
                                    // Show system permission dialog
                                    _showSystemPermissionDialog(
                                        context, notifier);
                                  } else {
                                    notifier.setNewsNotification(false);
                                  }
                                },
                        ),
                      ),
                      ListTile(
                        leading: const Icon(Icons.event),
                        title: Text(S.of(context).events),
                        subtitle: Text(S.of(context).eventsDescription),
                        trailing: Switch(
                          value: currentSettings.events,
                          onChanged: currentSettings.hasSystemPermission
                              ? (value) => notifier.setEventsNotification(value)
                              : (value) {
                                  if (value) {
                                    _showSystemPermissionDialog(
                                        context, notifier);
                                  } else {
                                    notifier.setEventsNotification(false);
                                  }
                                },
                        ),
                      ),
                      ListTile(
                        leading: const Icon(Icons.build),
                        title: Text(S.of(context).repairs),
                        subtitle: Text(S.of(context).repairsDescription),
                        trailing: Switch(
                          value: currentSettings.repairs,
                          onChanged: currentSettings.hasSystemPermission
                              ? (value) =>
                                  notifier.setRepairsNotification(value)
                              : (value) {
                                  if (value) {
                                    _showSystemPermissionDialog(
                                        context, notifier);
                                  } else {
                                    notifier.setRepairsNotification(false);
                                  }
                                },
                        ),
                      ),
                      ListTile(
                        leading: const Icon(Icons.mail),
                        title: Text(S.of(context).messages),
                        subtitle: Text(S.of(context).announcementHeaderMessage),
                        trailing: Switch(
                          value: currentSettings.announcements,
                          onChanged: currentSettings.hasSystemPermission
                              ? (value) =>
                                  notifier.setAnnouncementsNotification(value)
                              : (value) {
                                  if (value) {
                                    _showSystemPermissionDialog(
                                        context, notifier);
                                  } else {
                                    notifier
                                        .setAnnouncementsNotification(false);
                                  }
                                },
                        ),
                      ),
                      const SizedBox(height: 10),
                      // ✅ Add bottom padding to ensure button is visible
                      Padding(
                        padding: EdgeInsets.only(
                          bottom: MediaQuery.of(context).viewInsets.bottom + 16,
                        ),
                        child: SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            child: Text(S.of(context).close),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// ✅ Show system permission dialog with translations
  void _showSystemPermissionDialog(
      BuildContext context, NotificationNotifier notifier) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(S.of(context).enableNotifications),
        content: Text(S.of(context).enableNotificationsMessage),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(S.of(context).cancel),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              // Request system permission
              await notifier.requestSystemPermission();
            },
            child: Text(S.of(context).enable),
          ),
        ],
      ),
    );
  }

  /// **Get Notification Status String** ✅ Improved to handle all combinations
  String _getNotificationStatus(
      BuildContext context, NotificationSettings settings) {
    int enabledCount = 0;
    List<String> enabledTypes = [];

    if (settings.news) {
      enabledCount++;
      enabledTypes.add(S.of(context).news);
    }
    if (settings.events) {
      enabledCount++;
      enabledTypes.add(S.of(context).events);
    }
    if (settings.repairs) {
      enabledCount++;
      enabledTypes.add(S.of(context).repairs);
    }
    if (settings.announcements) {
      enabledCount++;
      enabledTypes.add(S.of(context).messages);
    }

    // Handle different combinations
    switch (enabledCount) {
      case 0:
        return S.of(context).allDisabled;
      case 1:
        return enabledTypes.first;
      case 2:
        return "${enabledTypes[0]} & ${enabledTypes[1]}";
      case 3:
        return "${enabledTypes[0]}, ${enabledTypes[1]} & ${enabledTypes[2]}";
      case 4:
        return S.of(context).allEnabled;
      default:
        return S.of(context).allDisabled;
    }
  }

  /// **Get Language Name Based on Locale**
  String _getLanguageName(String code) {
    switch (code) {
      case 'en':
        return 'English';
      case 'bg':
        return 'Български';
      case 'ru':
        return 'Русский';
      default:
        return 'Unknown';
    }
  }
}
