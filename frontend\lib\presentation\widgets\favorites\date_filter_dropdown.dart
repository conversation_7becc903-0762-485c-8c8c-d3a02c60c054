import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/domain/enums/date_filter.dart';
import 'package:frontend/providers/date_dropdown_provider.dart';
import 'package:frontend/providers/theme_provider.dart';
import 'package:frontend/l10n/generated/l10n.dart';
import 'package:google_fonts/google_fonts.dart';

class DateFilterDropdown extends ConsumerWidget {
  const DateFilterDropdown({super.key});

  String _getDateFilterLabel(DateFilter filter, BuildContext context) {
    switch (filter) {
      case DateFilter.all:
        return S.of(context).all;
      case DateFilter.today:
        return S.of(context).today;
      case DateFilter.thisWeek:
        return S.of(context).thisWeek;
      case DateFilter.thisMonth:
        return S.of(context).thisMonth;
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isDarkMode = ref.watch(themeNotifierProvider) == ThemeMode.dark;
    final selectedDateFilter = ref.watch(dateFilterStateProvider);
    final dateFilterNotifier = ref.read(dateFilterStateProvider.notifier);

    return Container(
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF333333) : Colors.white,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.symmetric(horizontal: 12.0),
      child: DropdownButton<DateFilter>(
        value: selectedDateFilter,
        underline: const SizedBox(),
        dropdownColor: isDarkMode ? const Color(0xFF333333) : Colors.white,
        icon: Icon(
          Icons.sort,
          color: isDarkMode ? Colors.white : const Color(0xFF626262),
        ),
        style: TextStyle(
          color: isDarkMode ? Colors.white : const Color(0xFF626262),
          fontSize: 14,
        ),
        onChanged: (DateFilter? newValue) {
          if (newValue != null) {
            dateFilterNotifier.setFilter(newValue);
          }
        },
        items: DateFilter.values.map((filter) {
          return DropdownMenuItem<DateFilter>(
            value: filter,
            child: Text(
              _getDateFilterLabel(filter, context),
              style: GoogleFonts.roboto(
                fontWeight: FontWeight.w400,
                fontSize: 14,
                color: isDarkMode ? Colors.white : const Color(0xFF626262),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}
