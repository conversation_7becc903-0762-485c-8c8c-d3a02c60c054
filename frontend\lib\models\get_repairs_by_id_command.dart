class RepairDetailsModel {
  final int id;
  final String? name;
  final String? description;
  final double latitude;
  final double longitude;
  final DateTime? startDate;
  final DateTime? endDate;
  final List<AreaPoint>? areaPoints;
  final List<ImageModel>? images;

  RepairDetailsModel({
    required this.id,
    required this.name,
    required this.description,
    required this.latitude,
    required this.longitude,
    this.startDate,
    this.endDate,
    this.areaPoints,
    this.images,
  });

  factory RepairDetailsModel.fromJson(Map<String, dynamic> json) {
    return RepairDetailsModel(
      id: json['id'] as int,
      name: json['name'] as String?,
      description: json['description'] as String?,
      latitude: (json['latitude'] ?? 0).toDouble(),
      longitude: (json['longitude'] ?? 0).toDouble(),
      startDate:
          json['startDate'] != null ? DateTime.parse(json['startDate']) : null,
      endDate: json['endDate'] != null ? DateTime.parse(json['endDate']) : null,
      areaPoints: (json['areaPoints'] as List<dynamic>?)
          ?.map((point) => AreaPoint.fromJson(point as Map<String, dynamic>))
          .toList(),
      images: (json['images'] as List<dynamic>?)
          ?.map((image) => ImageModel.fromJson(image as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'latitude': latitude,
      'longitude': longitude,
      'startDate': startDate?.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'areaPoints': areaPoints?.map((point) => point.toJson()).toList(),
      'images': images?.map((image) => image.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'RepairDetailsModel(id: $id, name: $name, description: $description, latitude: $latitude, longitude: $longitude, startDate: $startDate, endDate: $endDate, areaPoints: $areaPoints, images: $images)';
  }
}

class AreaPoint {
  final int id;
  final int index;
  final double latitude;
  final double longitude;
  final int repairId;

  AreaPoint({
    required this.id,
    required this.index,
    required this.latitude,
    required this.longitude,
    required this.repairId,
  });

  factory AreaPoint.fromJson(Map<String, dynamic> json) {
    return AreaPoint(
      id: json['id'] ?? 0,
      index: json['index'] ?? 0,
      latitude: (json['latitude'] ?? 0).toDouble(),
      longitude: (json['longitude'] ?? 0).toDouble(),
      repairId: json['repairId'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'index': index,
      'latitude': latitude,
      'longitude': longitude,
      'repairId': repairId,
    };
  }

  @override
  String toString() {
    return 'AreaPoint(id: $id, index: $index, latitude: $latitude, longitude: $longitude, repairId: $repairId)';
  }
}

class ImageModel {
  final int id;
  final String? path;
  final String? key;
  final String? preSignedUrl;
  final String? content;
  final bool isCover;

  ImageModel({
    required this.id,
    required this.path,
    required this.key,
    required this.preSignedUrl,
    required this.content,
    required this.isCover,
  });

  factory ImageModel.fromJson(Map<String, dynamic> json) {
    return ImageModel(
      id: json['id'] as int,
      path: json['path'] as String?,
      key: json['key'] as String?,
      preSignedUrl: json['preSignedUrl'] as String?,
      content: json['content'] as String?,
      isCover: json['isCover'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'path': path,
      'key': key,
      'preSignedUrl': preSignedUrl,
      'content': content,
      'isCover': isCover,
    };
  }

  @override
  String toString() {
    return 'ImageModel(id: $id, path: $path, key: $key, preSignedUrl: $preSignedUrl, content: $content, isCover: $isCover)';
  }
}
