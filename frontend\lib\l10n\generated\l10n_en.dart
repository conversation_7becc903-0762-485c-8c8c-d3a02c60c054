import 'l10n.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class SEn extends S {
  SEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Smart Yambol';

  @override
  String get guest => 'Guest';

  @override
  String get settings => 'Settings';

  @override
  String get darkMode => 'Dark Mode';

  @override
  String get language => 'Language';

  @override
  String get terms => 'Terms & Conditions';

  @override
  String get login => 'Login';

  @override
  String get tourism => 'Tourism';

  @override
  String get tourismDescription => 'Explore the beauty of Yambol and create lifelong memories!';

  @override
  String get weather => 'Weather';

  @override
  String get weatherDescription => 'Stay prepared! See the forecast and air quality.';

  @override
  String get events => 'Events';

  @override
  String get eventsDescription => 'Check out cultural and sports events in the city.';

  @override
  String get repairs => 'Repairs';

  @override
  String get repairsDescription => 'Stay informed! See where and when repairs are happening.';

  @override
  String get parking => 'Parking';

  @override
  String get parkingDescription => 'Find a parking spot or pay for blue zone parking via SMS.';

  @override
  String get generalInfo => 'General Info';

  @override
  String get generalInfoDescription => 'Discover everything you need to know about Yambol in one place.';

  @override
  String get transport => 'Transport';

  @override
  String get transportDescription => 'Up-to-date information on public transport routes and schedules.';

  @override
  String get cameras => 'Cameras';

  @override
  String get camerasDescription => 'Live-stream views from key locations in the city.';

  @override
  String get news => 'News';

  @override
  String get newsDescription => 'Stay updated! Follow the latest news and events.';

  @override
  String get taxes => 'Taxes';

  @override
  String get taxesDescription => 'View and pay your municipal taxes quickly and easily.';

  @override
  String get weddingServices => 'Weddings';

  @override
  String get weddingServicesDescription => 'Easily pick the perfect date and location for your wedding!';

  @override
  String get reports => 'Reports';

  @override
  String get reportsDescription => 'Report an issue in the city and help make it better.';

  @override
  String get favorites => 'Favorites';

  @override
  String get location => 'Location';

  @override
  String get home => 'Home';

  @override
  String get messages => 'Messages';

  @override
  String get emailOrUsername => 'Email or Username';

  @override
  String get password => 'Password';

  @override
  String get forgotPassword => 'Forgot Password?';

  @override
  String get loginButton => 'Log In';

  @override
  String get orContinueWith => '- or continue with -';

  @override
  String get createNewAccount => 'Create a new account';

  @override
  String get registerHere => 'here';

  @override
  String get register => 'Register';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get agreeToTerms => 'By clicking the Register button, you agree to the';

  @override
  String get registerTerms => 'terms & conditions';

  @override
  String get registerButton => 'Register';

  @override
  String get alreadyHaveAccount => 'Already have an account?';

  @override
  String get loginHere => 'Log in here';

  @override
  String get dontHaveAccount => 'Don\'t have an account?';

  @override
  String get firstName => 'First Name';

  @override
  String get lastName => 'Last Name';

  @override
  String get email => 'Email';

  @override
  String get emailRequired => 'Email is required';

  @override
  String get invalidEmail => 'Please enter a valid email address';

  @override
  String get registrationSuccessful => 'Registration successful!';

  @override
  String get passwordRequired => 'Password is required';

  @override
  String get passwordTooShort => 'Password must be at least 8 characters';

  @override
  String get confirmPasswordRequired => 'Please confirm your password';

  @override
  String get passwordsDoNotMatch => 'Passwords do not match';

  @override
  String get firstNameRequired => 'First name is required';

  @override
  String get lastNameRequired => 'Last name is required';

  @override
  String get tourismWelcomeMessage => 'Explore Yambol! Discover its landmarks and get inspired by its history.';

  @override
  String get tourismLegendsAndMyths => 'Legends and Myths';

  @override
  String get tourismSights => 'Landmarks';

  @override
  String get tourismCulturalAndArtisticSites => 'Cultural and Artistic Sites';

  @override
  String get tourismRoutesAndActivities => 'Routes and Activities';

  @override
  String get tourismFamilyEntertainment => 'Family Entertainment';

  @override
  String get tourismNightlife => 'Nightlife';

  @override
  String get tourismTransport => 'Transport';

  @override
  String get tourismTravelAgencies => 'Travel Agencies';

  @override
  String get loginSuccess => 'Login successful';

  @override
  String get loginError => 'Login failed';

  @override
  String get legendsAndMythsWelcomeMessage => 'Discover the mysteries of Yambol: Legends and myths that will captivate you';

  @override
  String get weatherTitle => 'Weather';

  @override
  String get cityName => 'Yambol';

  @override
  String get wind => 'Wind';

  @override
  String get humidity => 'Humidity';

  @override
  String get errorLoadingWeather => 'Error loading weather data';

  @override
  String get retry => 'Retry';

  @override
  String get monday => 'Monday';

  @override
  String get tuesday => 'Tuesday';

  @override
  String get wednesday => 'Wednesday';

  @override
  String get thursday => 'Thursday';

  @override
  String get friday => 'Friday';

  @override
  String get saturday => 'Saturday';

  @override
  String get sunday => 'Sunday';

  @override
  String get eventsWelcomeMessage => 'Don\'t miss anything! Explore cultural and sports events in the city!';

  @override
  String get search => 'Search...';

  @override
  String get tourismCulturalSites => 'Cultural sites';

  @override
  String get map => 'Map';

  @override
  String get all => 'All';

  @override
  String get sportEvents => 'Sport';

  @override
  String get cultureEvents => 'Culture';

  @override
  String get celebrationEvents => 'Celebrations';

  @override
  String get mon => 'Mon';

  @override
  String get tue => 'Tue';

  @override
  String get wed => 'Wed';

  @override
  String get thu => 'Thu';

  @override
  String get fri => 'Fri';

  @override
  String get sat => 'Sat';

  @override
  String get sun => 'Sun';

  @override
  String get january => 'January';

  @override
  String get february => 'February';

  @override
  String get march => 'March';

  @override
  String get april => 'April';

  @override
  String get may => 'May';

  @override
  String get june => 'June';

  @override
  String get july => 'July';

  @override
  String get august => 'August';

  @override
  String get september => 'September';

  @override
  String get october => 'October';

  @override
  String get november => 'November';

  @override
  String get december => 'December';

  @override
  String get today => 'Today';

  @override
  String get thisWeek => 'This week';

  @override
  String get thisMonth => 'This Month';

  @override
  String get noFavorites => 'No favourites';

  @override
  String get tourismScreenTitle => 'Tourism';

  @override
  String get more => 'More';

  @override
  String get selectCategory => 'Select category';

  @override
  String get emailConfirmationTitle => 'Email Confirmation';

  @override
  String get emailConfirmationMessage => 'Please check your email to confirm your account.';

  @override
  String get didNotReceiveEmail => 'Didn\'t receive the email?';

  @override
  String get resendVerificationEmail => 'Resend Verification Email';

  @override
  String get verificationEmailResent => 'Verification email resent!';

  @override
  String get errorOccurred => 'Error occurred';

  @override
  String get emailVerificationTitle => 'Email Verification';

  @override
  String get verifyEmailButton => 'Verify Email';

  @override
  String get emailVerifiedSuccessfully => 'Email verified successfully!';

  @override
  String get generalInfoWelcomeMessage => 'Yambol – business, education, and essential city information!';

  @override
  String get gasStations => 'Gas stations';

  @override
  String get generalInformationFull => 'General information';

  @override
  String get shop => 'Shops';

  @override
  String get restaurants => 'Restaurants';

  @override
  String get coffee => 'Coffee';

  @override
  String get bars => 'Bars';

  @override
  String get pastryShops => 'Pastry shops';

  @override
  String get establishments => 'Establishments';

  @override
  String get establishmentsWelcomeMessage => 'Cozy restaurants, traditional cuisine, and modern venues with a unique atmosphere!';

  @override
  String get hotels => 'Hotels';

  @override
  String get guestHouses => 'Guest houses';

  @override
  String get accommodation => 'Accommodation';

  @override
  String get accommodationWelcomeMessage => 'Comfortable hotels, cozy guesthouses, and great places to stay for every taste!';

  @override
  String get finance => 'Finance';

  @override
  String get banks => 'Banks';

  @override
  String get currencyExchanges => 'Currency Exchange';

  @override
  String get insuranceCompanies => 'Insurance';

  @override
  String get atms => 'ATMs';

  @override
  String get financeYambolWelcomeMessage => 'Secure banking services, affordable loans, and financial solutions for you in Yambol!';

  @override
  String get bioShops => 'Bio shops';

  @override
  String get farms => 'Farms';

  @override
  String get recycling => 'Recycling';

  @override
  String get ecoInitiatives => 'Eco initiatives';

  @override
  String get ecology => 'Ecology';

  @override
  String get ecoYambolWelcomeMessage => 'Clean nature, sustainable future, and eco-friendly solutions for Yambol!';

  @override
  String get culture => 'Culture';

  @override
  String get museums => 'Museums';

  @override
  String get theaters => 'Theaters';

  @override
  String get galleries => 'Galleries';

  @override
  String get cultureYambolWelcomeMessage => 'Rich cultural heritage, inspiring art, and traditions in Yambol!';

  @override
  String get education => 'Education';

  @override
  String get kindergardens => 'Kindergardens';

  @override
  String get nursery => 'Nursery';

  @override
  String get childNutritionCenter => 'Child nutrition center';

  @override
  String get schools => 'Schools';

  @override
  String get universities => 'Universities';

  @override
  String get developmentCenters => 'Development centers';

  @override
  String get health => 'Health';

  @override
  String get pharmacies => 'Pharmacies';

  @override
  String get medicalEstablishments => 'Medical establishments';

  @override
  String get doctorsOffices => 'Doctors offices';

  @override
  String get medicalLabs => 'Medical labs';

  @override
  String get veterinaries => 'Veterinaries';

  @override
  String get healthYambolWelcomeMessage => 'Health and care for everyone – quality medical services and a healthy lifestyle in Yambol!';

  @override
  String get educationYambolWelcomeMessage => 'Quality education for a bright future – innovative learning and development in Yambol!';

  @override
  String get sport => 'Sport';

  @override
  String get sportFacilities => 'Sport Facilities';

  @override
  String get sportYambolWelcomeMessage => 'Sports and an active lifestyle – modern facilities and opportunities for movement in Yambol!';

  @override
  String get newsYambolWelcomeMessage => 'Stay informed! Follow the latest news and events.';

  @override
  String get close => 'Close';

  @override
  String get description => 'Description';

  @override
  String get municipalityTaxes => 'Municipality Taxes';

  @override
  String get information => 'Information';

  @override
  String get chooseWayToPay => 'You can pay online using your CIN or with an electronic signature (which must be installed on your device).';

  @override
  String get waysToPay => 'Ways to pay';

  @override
  String get kinNumber => 'Pay with KIN';

  @override
  String get kinDesc => 'Fast and convenient payment with your KIN number';

  @override
  String get electronicSignature => 'Pay with electronic signature';

  @override
  String get electronicSignatureDesc => 'Secure payment with your electronic signature';

  @override
  String get userHelp => 'Need help? Contact us at phone: 0875 333 844';

  @override
  String get problemElectronicSignature => 'The electronic signature payment link could not be opened.';

  @override
  String get problemKin => 'The KIN payment link could not be opened.';

  @override
  String get navigate => 'Navigate';

  @override
  String get business => 'Business';

  @override
  String get landmarks => 'Landmarks';

  @override
  String get healthcare => 'Healthcare';

  @override
  String get sports => 'Sports';

  @override
  String get applyFilters => 'Apply Filters';

  @override
  String get filterCategories => 'Filter Categories';

  @override
  String get verifyingEmail => 'Verifying your email...';

  @override
  String get processingVerification => 'Processing verification...';

  @override
  String get retryVerification => 'Retry Verification';

  @override
  String get verificationFailed => 'Verification failed';

  @override
  String get checkSpamFolder => 'Don\'t forget to check your spam folder!';

  @override
  String get userAlreadyExists => 'An account with this email already exists!';

  @override
  String get invalidCredentials => 'Invalid email or password. Please check your credentials and try again.';

  @override
  String get emailNotVerified => 'Please verify your email address before logging in.';

  @override
  String get weakPassword => 'Password must be at least 8 characters long and contain uppercase, lowercase, and numbers.';

  @override
  String get invalidEmailFormat => 'Please enter a valid email address.';

  @override
  String get connectionTimeout => 'Connection timeout. Please check your internet connection and try again.';

  @override
  String get noInternet => 'No internet connection. Please check your network settings.';

  @override
  String get userNotFound => 'User not found. Please check your credentials or register a new account.';

  @override
  String get resourceNotFound => 'The requested resource was not found.';

  @override
  String get serverError => 'Server error. Please try again later.';

  @override
  String get validationError => 'Validation failed. Please check your input.';

  @override
  String get networkError => 'An unexpected error occurred.';

  @override
  String get accessDenied => 'Access denied.';

  @override
  String get passwordRequirements => 'Password must contain at least one uppercase letter, one lowercase letter, and one number';

  @override
  String get requirementMinLength => 'At least 6 characters';

  @override
  String get requirementUppercase => 'At least one uppercase letter (A-Z)';

  @override
  String get requirementNumber => 'At least one number (0-9)';

  @override
  String get requirementSpecialChar => 'At least one special character (!@#\$%^&*)';

  @override
  String get passwordMustContainUppercase => 'Password must contain at least one uppercase letter';

  @override
  String get passwordMustContainNumber => 'Password must contain at least one number';

  @override
  String get passwordMustContainSpecialChar => 'Password must contain at least one special character';

  @override
  String get dangerousBuildingOrSite => 'Dangerous Building or Site';

  @override
  String get streetLight => 'Street Light';

  @override
  String get trash => 'Trash';

  @override
  String get dangerousPlayground => 'Dangerous Playground';

  @override
  String get pothole => 'Pothole';

  @override
  String get brokenOrMissingSign => 'Broken or Missing Sign';

  @override
  String get illegalParking => 'Illegal Parking';

  @override
  String get hangingCables => 'Hanging Cables';

  @override
  String get waterLeakage => 'Water Leakage';

  @override
  String get fallenTree => 'Fallen Tree';

  @override
  String get collapsedRoad => 'Collapsed Road';

  @override
  String get illegalBuilding => 'Illegal Building';

  @override
  String get trafficLightNotWorking => 'Traffic Light Not Working';

  @override
  String get strayDogs => 'Stray Dogs';

  @override
  String get publicOrder => 'Public Order';

  @override
  String get abandonedCar => 'Abandoned Car';

  @override
  String get other => 'Other';

  @override
  String get title => 'Title';

  @override
  String get typeOfSignal => 'Type of Signal';

  @override
  String get phone => 'Phone';

  @override
  String get address => 'Address';

  @override
  String get files => 'Files';

  @override
  String get addFiles => 'Add Files';

  @override
  String get submitReport => 'Submit Report';

  @override
  String get noFilesSelected => 'No files selected';

  @override
  String get titleIsRequired => 'Title is required';

  @override
  String get phoneIsRequired => 'Phone is required';

  @override
  String get typeOfSignalIsRequired => 'Type of signal is required';

  @override
  String get reportSubmittedSuccessfully => 'Report submitted successfully!';

  @override
  String get reportSubmissionFailed => 'Report submission failed. Please try again.';

  @override
  String get reportAnIssue => 'Report an issue';

  @override
  String get loginRequired => 'Login required to submit reports';

  @override
  String get pleaseLoginToSubmitReports => 'Please login to submit reports';

  @override
  String get selectSource => 'Select source';

  @override
  String get camera => 'Camera';

  @override
  String get gallery => 'Gallery';

  @override
  String get passwordResetEmailSent => 'Password reset email sent successfully!';

  @override
  String get resetYourPassword => 'Reset Your Password';

  @override
  String get enterEmailToReset => 'Enter your email address and we\'ll send you a link to reset your password.';

  @override
  String get sendResetLink => 'Send Reset Link';

  @override
  String get rememberYourPassword => 'Remember your password?';

  @override
  String get backToLogin => 'Back to Login';

  @override
  String get emailAddress => 'Email Address';

  @override
  String get errorSendingResetEmail => 'Error sending reset email. Please try again.';

  @override
  String get linkExpired => 'Link Expired';

  @override
  String get resetLinkExpiredMessage => 'This password reset link has expired. Please request a new one.';

  @override
  String get invalidLink => 'Invalid Link';

  @override
  String get invalidLinkMessage => 'This password reset link is invalid. Please request a new one.';

  @override
  String get requestNewLink => 'Request New Link';

  @override
  String get passwordResetSuccess => 'Password reset successfully!';

  @override
  String get passwordResetError => 'Failed to reset password. Please try again.';

  @override
  String get resetPassword => 'Reset Password';

  @override
  String get createNewPassword => 'Create New Password';

  @override
  String get enterNewPasswordMessage => 'Enter your new password below. Make sure it\'s strong and secure.';

  @override
  String get newPassword => 'New Password';

  @override
  String get confirmNewPassword => 'Confirm New Password';

  @override
  String get resetPasswordButton => 'Reset Password';

  @override
  String get repairStatusOngoing => '🔧 In Progress';

  @override
  String get repairStatusCompleted => '✅ Completed';

  @override
  String get repairStatusScheduled => '📅 Scheduled';

  @override
  String get notSpecified => 'Not specified';

  @override
  String get unknownRepair => 'Unknown repair';

  @override
  String get start => 'Start';

  @override
  String get end => 'End';

  @override
  String get noDescriptionAvailable => 'No description available.';

  @override
  String get repairNotFound => 'Repair not found';

  @override
  String get loadingError => 'Loading error';

  @override
  String get notifications => 'Notifications';

  @override
  String get allEnabled => 'All enabled';

  @override
  String get newsOnly => 'News only';

  @override
  String get eventsOnly => 'Events only';

  @override
  String get allDisabled => 'All disabled';

  @override
  String get repairsOnly => 'Repairs only';

  @override
  String get systemPermissionRequired => 'System Permission Required';

  @override
  String get systemPermissionMessage => 'Enable notifications in your phone settings to receive alerts.';

  @override
  String get enableNotifications => 'Enable Notifications';

  @override
  String get enableNotificationsMessage => 'To receive notifications, you need to allow notifications in your phone settings. Would you like to enable them now?';

  @override
  String get enable => 'Enable';

  @override
  String get cancel => 'Cancel';

  @override
  String get selectABusStop => 'Select a bus stop';

  @override
  String get busStop => 'Bus stop';

  @override
  String get busStopNotFound => 'Bus stop not found';

  @override
  String get busStopNotSelected => 'Bus stop not selected';

  @override
  String get busStopSelected => 'Bus stop selected';

  @override
  String get bussArrivals => 'Bus arrivals';

  @override
  String get now => 'Now';

  @override
  String get min => 'min';

  @override
  String get noBusArrivals => 'No bus arrivals';

  @override
  String get noBusArrivalsMessage => 'There are no bus arrivals at this stop at the moment.';

  @override
  String get scheduled => 'Scheduled';

  @override
  String get tapForBusArrivals => 'Tap for bus arrivals';

  @override
  String get live => 'LIVE';

  @override
  String get offline => 'OFFLINE';

  @override
  String get stream_active => 'Stream Active';

  @override
  String get connection_failed => 'Connection Failed';

  @override
  String get real_time_video_feed => 'Real-time video feed';

  @override
  String get unable_to_connect => 'Unable to connect to camera';

  @override
  String get camera_controls => 'Camera Controls';

  @override
  String get refresh => 'Refresh';

  @override
  String get full_screen => 'Full Screen';

  @override
  String get exit_full_screen => 'Exit Full Screen';

  @override
  String get loading_stream => 'Loading stream...';

  @override
  String get connection_failed_title => 'Connection Failed';

  @override
  String get tap_refresh_to_try_again => 'Tap refresh to try again';

  @override
  String get help_text => 'This is a live stream from Yambol city center. Use the controls above to refresh.';

  @override
  String get yambol_live_camera => 'Yambol Live Camera';

  @override
  String get city_center => 'City Center';

  @override
  String get live_stream => 'Live Stream';

  @override
  String get airQuality => 'Air Quality';

  @override
  String get airQualityDescription => 'Breathe easy! Monitor the air quality and take care of your health.';

  @override
  String get blueZoneParkingMessage => 'This is a blue zone parking area. Parking fees may apply during business hours.';

  @override
  String get freeParkingMessage => 'This is a free parking spot. No parking fees apply.';

  @override
  String get parkingDetails => 'Parking Details';

  @override
  String get blueZone => 'Blue Zone';

  @override
  String get freeParking => 'Free Parking';

  @override
  String get parkingZone => 'Parking Zone';

  @override
  String get parkingInformations => 'Parking Informations';

  @override
  String get air_quality_zone => 'Air Quality Zone';

  @override
  String get weather_station => 'Weather Station';

  @override
  String get loading_details => 'Loading details...';

  @override
  String get loading_sensor_data => 'Loading sensor data...';

  @override
  String get error_loading_data => 'Error loading data';

  @override
  String get error_loading_sensor_data => 'Error loading sensor data';

  @override
  String get try_again => 'Try again';

  @override
  String get no_sensor_data_available => 'No data available from desired sensors';

  @override
  String get sensor_humidity => 'Air Humidity';

  @override
  String get sensor_temperature => 'Air Temperature';

  @override
  String get sensor_pressure => 'Atmospheric Pressure';

  @override
  String get sensor_pm25 => 'PM 2.5';

  @override
  String get sensor_pm10 => 'PM 10.0';

  @override
  String get sensor_radiation => 'Gamma Radiation';

  @override
  String get sensor_pm => 'Fine Particulate Matter';

  @override
  String get status_normal => 'Normal';

  @override
  String get status_moderate => 'Moderate';

  @override
  String get status_dangerous => 'Dangerous';

  @override
  String get select_air_quality_zone => 'Select air quality zone';

  @override
  String get loading_zones => 'Loading zones...';

  @override
  String get error_loading_zones => 'Error loading zones';

  @override
  String zone_with_number(String number) {
    return 'Zone $number';
  }

  @override
  String number_label(String number) {
    return 'Number: $number';
  }

  @override
  String get active => 'Active';

  @override
  String get inactive => 'Inactive';

  @override
  String get tap_for_details => 'Tap for details';
  String get recentAnnouncements => 'Recent Announcements';

  @override
  String get ago => 'ago';

  @override
  String get h => 'h';

  @override
  String get m => 'm';

  @override
  String get day => 'day';

  @override
  String get days => 'days';

  @override
  String get hour => 'hour';

  @override
  String get hours => 'hours';

  @override
  String get minute => 'minute';

  @override
  String get minutes => 'minutes';

  @override
  String get markAllAsRead => 'Mark all as read';

  @override
  String get loadingAnnouncements => 'Loading announcements...';

  @override
  String get failedToLoadAnnouncements => 'Failed to load announcements';

  @override
  String get pleaseCheckYourConnection => 'Please check your connection and try again.';

  @override
  String get noAnnouncementsMessage => 'You don\'t have any announcements yet.\nCheck back later for updates.';

  @override
  String get nouAnnouncements => 'No announcements';

  @override
  String get announcementHeaderMessage => 'Important announcements from Yambol Municipality';
}
