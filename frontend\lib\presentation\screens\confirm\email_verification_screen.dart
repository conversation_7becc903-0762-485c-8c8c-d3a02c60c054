import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:frontend/l10n/generated/l10n.dart';
import 'package:frontend/providers/auth_providers.dart';

class EmailVerificationScreen extends ConsumerStatefulWidget {
  final String userId;
  final String token;

  const EmailVerificationScreen({
    super.key,
    required this.userId,
    required this.token,
  });

  @override
  ConsumerState<EmailVerificationScreen> createState() =>
      _EmailVerificationScreenState();
}

class _EmailVerificationScreenState
    extends ConsumerState<EmailVerificationScreen> {
  bool hasInitialized = false;

  @override
  void initState() {
    super.initState();
    // Trigger verification automatically when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!hasInitialized) {
        hasInitialized = true;
        ref
            .read(authNotifierProvider.notifier)
            .verifyEmail(widget.userId, widget.token);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final l10n = S.of(context);

    // Listen to auth state changes
    ref.listen<AsyncValue<void>>(authNotifierProvider, (previous, next) {
      next.when(
        data: (_) {
          // Success - navigate to settings
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text("✅ ${l10n.emailVerifiedSuccessfully}"),
              backgroundColor: Colors.green,
            ),
          );

          context.pushReplacement('/login', extra: {'showBackButton': false});
        },
        loading: () {
          // Show loading state
        },
        error: (error, stack) {
          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text("❌ ${l10n.errorOccurred}: $error"),
              backgroundColor: Colors.red,
            ),
          );
        },
      );
    });

    final authState = ref.watch(authNotifierProvider);

    return Scaffold(
      appBar: AppBar(title: Text(l10n.emailVerificationTitle)),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Show loading spinner
            if (authState is AsyncLoading) ...[
              const CircularProgressIndicator(),
              const SizedBox(height: 20),
              Text(
                l10n.verifyingEmail,
                style: const TextStyle(fontSize: 16),
              ),
            ],

            // Show error state with retry option
            if (authState is AsyncError) ...[
              Icon(
                Icons.error_outline,
                size: 80,
                color: Colors.red,
              ),
              const SizedBox(height: 20),
              Text(
                "❌ ${l10n.errorOccurred}: ${authState.error}",
                style: const TextStyle(fontSize: 16, color: Colors.red),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: () {
                  ref
                      .read(authNotifierProvider.notifier)
                      .verifyEmail(widget.userId, widget.token);
                },
                child: Text(l10n.retryVerification),
              ),
            ],

            // Initial state (should quickly move to loading)
            if (authState is! AsyncLoading && authState is! AsyncError) ...[
              Icon(
                Icons.email_outlined,
                size: 80,
                color: Colors.blue,
              ),
              const SizedBox(height: 20),
              Text(
                l10n.processingVerification,
                style: const TextStyle(fontSize: 16),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
