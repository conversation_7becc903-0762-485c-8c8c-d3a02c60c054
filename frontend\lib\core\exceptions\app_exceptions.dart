// lib/core/exceptions/app_exceptions.dart

/// Base exception class for all API errors
abstract class AppException implements Exception {
  final String message;
  final int? statusCode;
  final dynamic originalError;

  const AppException(this.message, {this.statusCode, this.originalError});

  @override
  String toString() => message;
}

/// Authentication related exceptions
class AuthException extends AppException {
  const AuthException(super.message, {super.statusCode, super.originalError});
}

/// Validation related exceptions
class ValidationException extends AppException {
  final Map<String, List<String>>? fieldErrors;

  const ValidationException(
    super.message, {
    super.statusCode,
    super.originalError,
    this.fieldErrors,
  });
}

/// Network related exceptions
class NetworkException extends AppException {
  const NetworkException(super.message,
      {super.statusCode, super.originalError});
}

/// Server related exceptions
class ServerException extends AppException {
  const ServerException(super.message, {super.statusCode, super.originalError});
}

/// User already exists exception
class UserAlreadyExistsException extends AuthException {
  const UserAlreadyExistsException() : super('userAlreadyExists');
}

/// Invalid credentials exception
class InvalidCredentialsException extends AuthException {
  const InvalidCredentialsException() : super('invalidCredentials');
}

/// Email not verified exception
class EmailNotVerifiedException extends AuthException {
  const EmailNotVerifiedException() : super('emailNotVerified');
}

/// Weak password exception
class WeakPasswordException extends ValidationException {
  const WeakPasswordException() : super('weakPassword');
}

/// Invalid email format exception
class InvalidEmailException extends ValidationException {
  const InvalidEmailException() : super('invalidEmailFormat');
}

/// Connection timeout exception
class ConnectionTimeoutException extends NetworkException {
  const ConnectionTimeoutException() : super('connectionTimeout');
}

/// No internet connection exception
class NoInternetException extends NetworkException {
  const NoInternetException() : super('noInternet');
}

/// User not found exception (for 404 entity errors)
class UserNotFoundException extends AuthException {
  const UserNotFoundException([String? customMessage])
      : super(customMessage ?? 'userNotFound');
}

/// Resource not found exception (for general 404 errors)
class ResourceNotFoundException extends NetworkException {
  const ResourceNotFoundException([String? customMessage])
      : super(customMessage ?? 'resourceNotFound');
}

/// Server error exception
class ServerErrorException extends ServerException {
  const ServerErrorException() : super('serverError');
}

/// Validation error exception
class ValidationErrorException extends ValidationException {
  const ValidationErrorException() : super('validationError');
}

/// Network error exception
class NetworkErrorException extends NetworkException {
  const NetworkErrorException() : super('networkError');
}

/// Access denied exception
class AccessDeniedException extends AuthException {
  const AccessDeniedException() : super('accessDenied');
}
