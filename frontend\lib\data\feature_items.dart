import 'package:flutter/material.dart';

final List<Map<String, dynamic>> featureItems = [
  {
    'icon': Icons.apartment,
    'translationKey': 'tourism',
    'descriptionKey': 'tourism_description',
    'color': Color(0xFFA1AADF),
    'disabled': false
  },
  {
    'icon': Icons.cloud,
    'translationKey': 'weather',
    'descriptionKey': 'weather_description',
    'color': Color(0xFF95D4D1),
    'disabled': false
  },
  {
    'icon': Icons.event,
    'translationKey': 'events',
    'descriptionKey': 'events_description',
    'color': Color(0xFFFFE9E2),
    'disabled': false
  },
  {
    'icon': Icons.build,
    'translationKey': 'repairs',
    'descriptionKey': 'repairs_description',
    'color': Color(0xFFFDFFC8),
    'disabled': false
  },
  {
    'icon': Icons.local_parking,
    'translationKey': 'parking',
    'descriptionKey': 'parking_description',
    'color': Color(0xFFF2D4A3),
    'disabled': false
  },
  {
    'icon': Icons.info,
    'translationKey': 'general_info',
    'descriptionKey': 'general_info_description',
    'color': Color(0xFFBABBBA),
    'disabled': false
  },
  {
    'icon': Icons.bus_alert,
    'translationKey': 'transport',
    'descriptionKey': 'transport_description',
    'color': Color(0xFFAC003F),
    'disabled': false
  },
  {
    'icon': Icons.videocam,
    'translationKey': 'cameras',
    'descriptionKey': 'cameras_description',
    'color': Color(0xFFA1AADF),
    'disabled': false
  },
  {
    'icon': Icons.article,
    'translationKey': 'news',
    'descriptionKey': 'news_description',
    'color': Color(0xFF598863),
    'disabled': false
  },
  {
    'icon': Icons.paid,
    'translationKey': 'taxes',
    'descriptionKey': 'taxes_description',
    'color': Color(0xFFB8FDFF),
    'disabled': false
  },
  {
    'icon': Icons.favorite,
    'translationKey': 'weddings',
    'descriptionKey': 'weddings_description',
    'color': Color(0xFF68FFA7),
    'disabled': false
  },
  {
    'icon': Icons.warning,
    'translationKey': 'reports',
    'descriptionKey': 'reports_description',
    'color': Color(0xFFF7C5FF),
    'disabled': false
  },
  {
    'icon': Icons.air,
    'translationKey': 'air_quality',
    'descriptionKey': 'air_quality_description',
    'color': Color(0xFFC16AB1),
    'disabled': false
  },
];
