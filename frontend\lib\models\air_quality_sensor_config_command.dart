// Sensor Configuration Model
class SensorConfig {
  final List<AlarmConfig> alarmConfigList;
  final List<String> allowedUnits;
  final String defaultVisualizationType;
  final String description;
  final double flowConversionCoefficient;
  final FlowUnit flowUnit;
  final int iconId;
  final String iconName;
  final String manufacturer;
  final String model;
  final String name;
  final int orderNumber;
  final int precision;
  final int scale;
  final String serialNumber;
  final String units;
  final bool useInSensorsAggregation;
  final Map<String, String> valueLabels;
  final ValueRanges valueRanges;
  final bool virtual;
  final bool visible;
  final WildPointFilter wildPointFilter;

  SensorConfig({
    required this.alarmConfigList,
    required this.allowedUnits,
    required this.defaultVisualizationType,
    required this.description,
    required this.flowConversionCoefficient,
    required this.flowUnit,
    required this.iconId,
    required this.iconName,
    required this.manufacturer,
    required this.model,
    required this.name,
    required this.orderNumber,
    required this.precision,
    required this.scale,
    required this.serialNumber,
    required this.units,
    required this.useInSensorsAggregation,
    required this.valueLabels,
    required this.valueRanges,
    required this.virtual,
    required this.visible,
    required this.wildPointFilter,
  });

  factory SensorConfig.fromJson(Map<String, dynamic> json) {
    return SensorConfig(
      alarmConfigList: (json['alarmConfigList'] as List?)
              ?.map((e) => AlarmConfig.fromJson(e))
              .toList() ??
          [],
      allowedUnits: (json['allowedUnits'] as List?)?.cast<String>() ?? [],
      defaultVisualizationType: json['defaultVisualizationType'] ?? '',
      description: json['description'] ?? '',
      flowConversionCoefficient:
          (json['flowConversionCoefficient'] ?? 0).toDouble(),
      flowUnit: FlowUnit.fromJson(json['flowUnit'] ?? {}),
      iconId: json['iconId'] ?? 0,
      iconName: json['iconName'] ?? '',
      manufacturer: json['manufacturer'] ?? '',
      model: json['model'] ?? '',
      name: json['name'] ?? '',
      orderNumber: json['orderNumber'] ?? 0,
      precision: json['precision'] ?? 0,
      scale: json['scale'] ?? 0,
      serialNumber: json['serialNumber'] ?? '',
      units: json['units'] ?? '',
      useInSensorsAggregation: json['useInSensorsAggregation'] ?? false,
      valueLabels: Map<String, String>.from(json['valueLabels'] ?? {}),
      valueRanges: ValueRanges.fromJson(json['valueRanges'] ?? {}),
      virtual: json['virtual'] ?? false,
      visible: json['visible'] ?? false,
      wildPointFilter: WildPointFilter.fromJson(json['wildPointFilter'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'alarmConfigList': alarmConfigList.map((e) => e.toJson()).toList(),
      'allowedUnits': allowedUnits,
      'defaultVisualizationType': defaultVisualizationType,
      'description': description,
      'flowConversionCoefficient': flowConversionCoefficient,
      'flowUnit': flowUnit.toJson(),
      'iconId': iconId,
      'iconName': iconName,
      'manufacturer': manufacturer,
      'model': model,
      'name': name,
      'orderNumber': orderNumber,
      'precision': precision,
      'scale': scale,
      'serialNumber': serialNumber,
      'units': units,
      'useInSensorsAggregation': useInSensorsAggregation,
      'valueLabels': valueLabels,
      'valueRanges': valueRanges.toJson(),
      'virtual': virtual,
      'visible': visible,
      'wildPointFilter': wildPointFilter.toJson(),
    };
  }
}

// Alarm Configuration Model
class AlarmConfig {
  final bool enableEmailNotification;
  final bool enableMobileNotification;

  AlarmConfig({
    required this.enableEmailNotification,
    required this.enableMobileNotification,
  });

  factory AlarmConfig.fromJson(Map<String, dynamic> json) {
    return AlarmConfig(
      enableEmailNotification: json['enableEmailNotification'] ?? false,
      enableMobileNotification: json['enableMobileNotification'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'enableEmailNotification': enableEmailNotification,
      'enableMobileNotification': enableMobileNotification,
    };
  }
}

// Flow Unit Model
class FlowUnit {
  final String timeUnits;
  final String units;

  FlowUnit({
    required this.timeUnits,
    required this.units,
  });

  factory FlowUnit.fromJson(Map<String, dynamic> json) {
    return FlowUnit(
      timeUnits: json['timeUnits'] ?? '',
      units: json['units'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'timeUnits': timeUnits,
      'units': units,
    };
  }
}

// Value Ranges Model
class ValueRanges {
  final List<double> boundaries;
  final List<String> keys;

  ValueRanges({
    required this.boundaries,
    required this.keys,
  });

  factory ValueRanges.fromJson(Map<String, dynamic> json) {
    return ValueRanges(
      boundaries: (json['boundaries'] as List?)
              ?.map<double>((e) => (e ?? 0).toDouble())
              .toList() ??
          [],
      keys: (json['keys'] as List?)?.cast<String>() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'boundaries': boundaries,
      'keys': keys,
    };
  }
}

// Wild Point Filter Model
class WildPointFilter {
  final double wildPointThreshold;

  WildPointFilter({
    required this.wildPointThreshold,
  });

  factory WildPointFilter.fromJson(Map<String, dynamic> json) {
    return WildPointFilter(
      wildPointThreshold: (json['wildPointThreshold'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'wildPointThreshold': wildPointThreshold,
    };
  }
}
