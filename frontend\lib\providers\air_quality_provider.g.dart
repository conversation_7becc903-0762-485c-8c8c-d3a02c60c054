// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'air_quality_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchCurrentReadingsHash() =>
    r'2fec0b65881cb30cd07353bcee6b2fe231272ef1';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [fetchCurrentReadings].
@ProviderFor(fetchCurrentReadings)
const fetchCurrentReadingsProvider = FetchCurrentReadingsFamily();

/// See also [fetchCurrentReadings].
class FetchCurrentReadingsFamily
    extends Family<AsyncValue<List<SensorReadingModel>>> {
  /// See also [fetchCurrentReadings].
  const FetchCurrentReadingsFamily();

  /// See also [fetchCurrentReadings].
  FetchCurrentReadingsProvider call({
    required String deviceNumber,
    int every = 1,
  }) {
    return FetchCurrentReadingsProvider(
      deviceNumber: deviceNumber,
      every: every,
    );
  }

  @override
  FetchCurrentReadingsProvider getProviderOverride(
    covariant FetchCurrentReadingsProvider provider,
  ) {
    return call(
      deviceNumber: provider.deviceNumber,
      every: provider.every,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchCurrentReadingsProvider';
}

/// See also [fetchCurrentReadings].
class FetchCurrentReadingsProvider
    extends AutoDisposeFutureProvider<List<SensorReadingModel>> {
  /// See also [fetchCurrentReadings].
  FetchCurrentReadingsProvider({
    required String deviceNumber,
    int every = 1,
  }) : this._internal(
          (ref) => fetchCurrentReadings(
            ref as FetchCurrentReadingsRef,
            deviceNumber: deviceNumber,
            every: every,
          ),
          from: fetchCurrentReadingsProvider,
          name: r'fetchCurrentReadingsProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$fetchCurrentReadingsHash,
          dependencies: FetchCurrentReadingsFamily._dependencies,
          allTransitiveDependencies:
              FetchCurrentReadingsFamily._allTransitiveDependencies,
          deviceNumber: deviceNumber,
          every: every,
        );

  FetchCurrentReadingsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.deviceNumber,
    required this.every,
  }) : super.internal();

  final String deviceNumber;
  final int every;

  @override
  Override overrideWith(
    FutureOr<List<SensorReadingModel>> Function(
            FetchCurrentReadingsRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchCurrentReadingsProvider._internal(
        (ref) => create(ref as FetchCurrentReadingsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        deviceNumber: deviceNumber,
        every: every,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<SensorReadingModel>> createElement() {
    return _FetchCurrentReadingsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchCurrentReadingsProvider &&
        other.deviceNumber == deviceNumber &&
        other.every == every;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, deviceNumber.hashCode);
    hash = _SystemHash.combine(hash, every.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchCurrentReadingsRef
    on AutoDisposeFutureProviderRef<List<SensorReadingModel>> {
  /// The parameter `deviceNumber` of this provider.
  String get deviceNumber;

  /// The parameter `every` of this provider.
  int get every;
}

class _FetchCurrentReadingsProviderElement
    extends AutoDisposeFutureProviderElement<List<SensorReadingModel>>
    with FetchCurrentReadingsRef {
  _FetchCurrentReadingsProviderElement(super.provider);

  @override
  String get deviceNumber =>
      (origin as FetchCurrentReadingsProvider).deviceNumber;
  @override
  int get every => (origin as FetchCurrentReadingsProvider).every;
}

String _$fetchAllLocationsHash() => r'5be88eaaacb3375a93e205094c501390e101bbeb';

/// See also [fetchAllLocations].
@ProviderFor(fetchAllLocations)
final fetchAllLocationsProvider =
    AutoDisposeFutureProvider<List<AirQualityLocationModel>>.internal(
  fetchAllLocations,
  name: r'fetchAllLocationsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchAllLocationsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchAllLocationsRef
    = AutoDisposeFutureProviderRef<List<AirQualityLocationModel>>;
String _$fetchAllZonesHash() => r'66fec55aaf76055e55d3f907d2a196f64c8d6817';

/// See also [fetchAllZones].
@ProviderFor(fetchAllZones)
final fetchAllZonesProvider =
    AutoDisposeFutureProvider<List<AirQualityZoneModel>>.internal(
  fetchAllZones,
  name: r'fetchAllZonesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchAllZonesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchAllZonesRef
    = AutoDisposeFutureProviderRef<List<AirQualityZoneModel>>;
String _$fetchZoneDetailsHash() => r'e966043c92ae9f21342b7e157cf31ee3e294c241';

/// See also [fetchZoneDetails].
@ProviderFor(fetchZoneDetails)
const fetchZoneDetailsProvider = FetchZoneDetailsFamily();

/// See also [fetchZoneDetails].
class FetchZoneDetailsFamily extends Family<AsyncValue<AirQualityZoneModel?>> {
  /// See also [fetchZoneDetails].
  const FetchZoneDetailsFamily();

  /// See also [fetchZoneDetails].
  FetchZoneDetailsProvider call({
    required String deviceNumber,
  }) {
    return FetchZoneDetailsProvider(
      deviceNumber: deviceNumber,
    );
  }

  @override
  FetchZoneDetailsProvider getProviderOverride(
    covariant FetchZoneDetailsProvider provider,
  ) {
    return call(
      deviceNumber: provider.deviceNumber,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchZoneDetailsProvider';
}

/// See also [fetchZoneDetails].
class FetchZoneDetailsProvider
    extends AutoDisposeFutureProvider<AirQualityZoneModel?> {
  /// See also [fetchZoneDetails].
  FetchZoneDetailsProvider({
    required String deviceNumber,
  }) : this._internal(
          (ref) => fetchZoneDetails(
            ref as FetchZoneDetailsRef,
            deviceNumber: deviceNumber,
          ),
          from: fetchZoneDetailsProvider,
          name: r'fetchZoneDetailsProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$fetchZoneDetailsHash,
          dependencies: FetchZoneDetailsFamily._dependencies,
          allTransitiveDependencies:
              FetchZoneDetailsFamily._allTransitiveDependencies,
          deviceNumber: deviceNumber,
        );

  FetchZoneDetailsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.deviceNumber,
  }) : super.internal();

  final String deviceNumber;

  @override
  Override overrideWith(
    FutureOr<AirQualityZoneModel?> Function(FetchZoneDetailsRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchZoneDetailsProvider._internal(
        (ref) => create(ref as FetchZoneDetailsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        deviceNumber: deviceNumber,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<AirQualityZoneModel?> createElement() {
    return _FetchZoneDetailsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchZoneDetailsProvider &&
        other.deviceNumber == deviceNumber;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, deviceNumber.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchZoneDetailsRef
    on AutoDisposeFutureProviderRef<AirQualityZoneModel?> {
  /// The parameter `deviceNumber` of this provider.
  String get deviceNumber;
}

class _FetchZoneDetailsProviderElement
    extends AutoDisposeFutureProviderElement<AirQualityZoneModel?>
    with FetchZoneDetailsRef {
  _FetchZoneDetailsProviderElement(super.provider);

  @override
  String get deviceNumber => (origin as FetchZoneDetailsProvider).deviceNumber;
}

String _$selectedLocationHash() => r'93b2c390aba674cdd2857bfa5e420372e7d583c9';

/// See also [SelectedLocation].
@ProviderFor(SelectedLocation)
final selectedLocationProvider = AutoDisposeNotifierProvider<SelectedLocation,
    AirQualityLocationModel?>.internal(
  SelectedLocation.new,
  name: r'selectedLocationProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$selectedLocationHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectedLocation = AutoDisposeNotifier<AirQualityLocationModel?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
