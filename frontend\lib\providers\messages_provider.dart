import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/infrastructure/messages/message_service.dart';
import 'package:frontend/models/get_message_command.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:frontend/providers/language_provider.dart';

part 'messages_provider.g.dart';

// Provider to fetch all announcements with language code
@riverpod
Future<List<MessageModel>> fetchAllAnnouncements(Ref ref) async {
  print("Fetching all announcements");

  // Get the current language code from language provider
  final languageCode = ref.watch(languageNotifierProvider).languageCode;

  return AnnouncementService.fetchAnnouncements(
    languageCode: languageCode,
  );
}
